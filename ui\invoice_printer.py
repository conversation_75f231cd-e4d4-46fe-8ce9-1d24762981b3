#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام طباعة الفواتير - نظام نقاط البيع والمحاسبة
Invoice Printer System - POS and Accounting System
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTextEdit, QMessageBox, QFileDialog)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QTextDocument, QTextCursor
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
import os
from datetime import datetime

class InvoicePrinter(QDialog):
    """نافذة طباعة الفواتير"""
    
    def __init__(self, db_manager, invoice_id, invoice_type='sale', parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.invoice_id = invoice_id
        self.invoice_type = invoice_type
        self.invoice_data = None
        self.invoice_items = []
        
        self.init_ui()
        self.load_invoice_data()
        self.generate_invoice_html()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("🖨️ طباعة الفاتورة")
        self.setGeometry(100, 100, 800, 600)
        
        layout = QVBoxLayout()
        
        # عنوان
        title = QLabel("معاينة الفاتورة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # منطقة المعاينة
        self.preview_area = QTextEdit()
        self.preview_area.setReadOnly(True)
        layout.addWidget(self.preview_area)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        # زر الطباعة
        print_btn = QPushButton("🖨️ طباعة")
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196f3;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #1976d2;
            }
        """)
        print_btn.clicked.connect(self.print_invoice)
        buttons_layout.addWidget(print_btn)
        
        # زر حفظ PDF
        save_pdf_btn = QPushButton("💾 حفظ PDF")
        save_pdf_btn.setStyleSheet("""
            QPushButton {
                background-color: #4caf50;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        save_pdf_btn.clicked.connect(self.save_as_pdf)
        buttons_layout.addWidget(save_pdf_btn)
        
        # زر إغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
        
    def load_invoice_data(self):
        """تحميل بيانات الفاتورة"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            # تحميل بيانات الفاتورة الأساسية
            if self.invoice_type == 'sale':
                cursor.execute('''
                    SELECT i.*, c.name as customer_name, c.phone as customer_phone, c.address as customer_address
                    FROM invoices i
                    LEFT JOIN customers c ON i.customer_id = c.id
                    WHERE i.id = ? AND i.invoice_type = 'sale'
                ''', (self.invoice_id,))
            else:
                cursor.execute('''
                    SELECT i.*, s.name as supplier_name, s.phone as supplier_phone, s.address as supplier_address
                    FROM invoices i
                    LEFT JOIN suppliers s ON i.supplier_id = s.id
                    WHERE i.id = ? AND i.invoice_type = 'purchase'
                ''', (self.invoice_id,))
            
            self.invoice_data = cursor.fetchone()
            
            # تحميل أصناف الفاتورة
            cursor.execute('''
                SELECT ii.*, p.name as product_name, p.unit
                FROM invoice_items ii
                JOIN products p ON ii.product_id = p.id
                WHERE ii.invoice_id = ?
                ORDER BY ii.id
            ''', (self.invoice_id,))
            
            self.invoice_items = cursor.fetchall()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل بيانات الفاتورة:\n{str(e)}")
        finally:
            conn.close()
    
    def generate_invoice_html(self):
        """إنشاء HTML للفاتورة"""
        if not self.invoice_data:
            self.preview_area.setPlainText("خطأ: لم يتم تحميل بيانات الفاتورة")
            return

        try:
            
        # تحديد نوع الفاتورة
        invoice_title = "فاتورة مبيعات" if self.invoice_type == 'sale' else "فاتورة مشتريات"
        entity_name = self.invoice_data.get('customer_name', 'عميل نقدي') if self.invoice_type == 'sale' else self.invoice_data.get('supplier_name', 'مورد نقدي')
        entity_phone = self.invoice_data.get('customer_phone', '') if self.invoice_type == 'sale' else self.invoice_data.get('supplier_phone', '')
        entity_address = self.invoice_data.get('customer_address', '') if self.invoice_type == 'sale' else self.invoice_data.get('supplier_address', '')
        
        html = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <style>
                body {{
                    font-family: 'Arial', sans-serif;
                    margin: 20px;
                    direction: rtl;
                    text-align: right;
                }}
                .header {{
                    text-align: center;
                    border-bottom: 3px solid #2196f3;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }}
                .company-name {{
                    font-size: 24px;
                    font-weight: bold;
                    color: #2196f3;
                    margin-bottom: 10px;
                }}
                .invoice-title {{
                    font-size: 20px;
                    font-weight: bold;
                    color: #333;
                }}
                .invoice-info {{
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 30px;
                }}
                .info-section {{
                    width: 45%;
                }}
                .info-title {{
                    font-weight: bold;
                    color: #2196f3;
                    border-bottom: 1px solid #ddd;
                    padding-bottom: 5px;
                    margin-bottom: 10px;
                }}
                .items-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }}
                .items-table th, .items-table td {{
                    border: 1px solid #ddd;
                    padding: 10px;
                    text-align: center;
                }}
                .items-table th {{
                    background-color: #2196f3;
                    color: white;
                    font-weight: bold;
                }}
                .items-table tr:nth-child(even) {{
                    background-color: #f9f9f9;
                }}
                .totals {{
                    float: left;
                    width: 300px;
                    margin-top: 20px;
                }}
                .total-row {{
                    display: flex;
                    justify-content: space-between;
                    padding: 5px 0;
                    border-bottom: 1px solid #ddd;
                }}
                .final-total {{
                    font-weight: bold;
                    font-size: 18px;
                    color: #2196f3;
                    border-top: 2px solid #2196f3;
                    padding-top: 10px;
                }}
                .footer {{
                    margin-top: 50px;
                    text-align: center;
                    color: #666;
                    font-size: 12px;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="company-name">شركة شادي الطيب للتجارة</div>
                <div class="invoice-title">{invoice_title}</div>
            </div>
            
            <div class="invoice-info">
                <div class="info-section">
                    <div class="info-title">بيانات الفاتورة</div>
                    <p><strong>رقم الفاتورة:</strong> {self.invoice_data['invoice_number']}</p>
                    <p><strong>تاريخ الفاتورة:</strong> {self.invoice_data['invoice_date']}</p>
                    <p><strong>طريقة الدفع:</strong> {self.get_payment_method_text(self.invoice_data['payment_method'])}</p>
                </div>
                <div class="info-section">
                    <div class="info-title">بيانات {'العميل' if self.invoice_type == 'sale' else 'المورد'}</div>
                    <p><strong>الاسم:</strong> {entity_name}</p>
                    <p><strong>الهاتف:</strong> {entity_phone or 'غير محدد'}</p>
                    <p><strong>العنوان:</strong> {entity_address or 'غير محدد'}</p>
                </div>
            </div>
            
            <table class="items-table">
                <thead>
                    <tr>
                        <th>م</th>
                        <th>اسم الصنف</th>
                        <th>الوحدة</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
        """
        
        # إضافة أصناف الفاتورة
        for i, item in enumerate(self.invoice_items, 1):
            # التعامل مع البيانات بحذر
            product_name = item.get('product_name', 'غير محدد')
            unit = item.get('unit', 'قطعة')
            quantity = item.get('quantity', 0)
            unit_price = item.get('unit_price', 0)
            total_price = item.get('total_price', 0)

            html += f"""
                    <tr>
                        <td>{i}</td>
                        <td>{product_name}</td>
                        <td>{unit}</td>
                        <td>{quantity}</td>
                        <td>{unit_price:.2f}</td>
                        <td>{total_price:.2f}</td>
                    </tr>
            """
        
        html += f"""
                </tbody>
            </table>
            
            <div class="totals">
                <div class="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>{self.invoice_data['total_amount']:.2f} جنيه</span>
                </div>
                <div class="total-row">
                    <span>الخصم:</span>
                    <span>{self.invoice_data['discount_amount']:.2f} جنيه</span>
                </div>
                <div class="total-row final-total">
                    <span>الإجمالي النهائي:</span>
                    <span>{self.invoice_data['final_amount']:.2f} جنيه</span>
                </div>
                <div class="total-row">
                    <span>المدفوع:</span>
                    <span>{self.invoice_data['paid_amount']:.2f} جنيه</span>
                </div>
                <div class="total-row">
                    <span>المتبقي:</span>
                    <span>{self.invoice_data['remaining_amount']:.2f} جنيه</span>
                </div>
            </div>
            
            <div class="footer">
                <p>شكراً لتعاملكم معنا</p>
                <p>تم إنشاء هذه الفاتورة بواسطة نظام نقاط البيع - {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
            </div>
        </body>
        </html>
        """

            self.preview_area.setHtml(html)

        except Exception as e:
            error_msg = f"خطأ في إنشاء معاينة الفاتورة:\n{str(e)}"
            self.preview_area.setPlainText(error_msg)
            print(f"خطأ في generate_invoice_html: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def get_payment_method_text(self, method):
        """تحويل طريقة الدفع إلى نص عربي"""
        methods = {
            'cash': 'نقدي',
            'bank_transfer': 'تحويل بنكي',
            'credit': 'آجل',
            'partial': 'جزئي'
        }
        return methods.get(method, method)
    
    def print_invoice(self):
        """طباعة الفاتورة"""
        try:
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            
            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec_() == QPrintDialog.Accepted:
                document = QTextDocument()
                document.setHtml(self.preview_area.toHtml())
                document.print_(printer)
                QMessageBox.information(self, "نجح", "تم إرسال الفاتورة للطباعة بنجاح!")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الطباعة:\n{str(e)}")
    
    def save_as_pdf(self):
        """حفظ الفاتورة كملف PDF"""
        try:
            # اختيار مكان الحفظ
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ الفاتورة كـ PDF",
                f"فاتورة_{self.invoice_data['invoice_number']}.pdf",
                "PDF Files (*.pdf)"
            )
            
            if filename:
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(filename)
                printer.setPageSize(QPrinter.A4)
                
                document = QTextDocument()
                document.setHtml(self.preview_area.toHtml())
                document.print_(printer)
                
                QMessageBox.information(self, "نجح", f"تم حفظ الفاتورة بنجاح في:\n{filename}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ PDF:\n{str(e)}")
