#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إنشاء فاتورة الشراء - نظام نقاط البيع والمحاسبة
Purchase Invoice Creation Window - POS and Accounting System
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QComboBox, QFormLayout,
                            QMessageBox, QHeaderView, QAbstractItemView,
                            QFrame, QGroupBox, QSpinBox, QDoubleSpinBox,
                            QDateEdit, QTextEdit, QCheckBox, QSplitter,
                            QGridLayout, QShortcut, QWidget, QCompleter)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTimer
from PyQt5.QtGui import QFont, QColor, QKeySequence
from utils.config import Config
from datetime import datetime
import uuid

class PurchaseInvoiceNewWindow(QDialog):
    """نافذة إنشاء فاتورة الشراء الجديدة"""

    # إشارات
    invoice_saved = pyqtSignal()
    invoice_printed = pyqtSignal()

    def __init__(self, db_manager, user_data, invoice_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        self.invoice_id = invoice_id
        self.invoice_items = []
        self.init_ui()

        if invoice_id:
            self.load_invoice_data()
        else:
            self.generate_invoice_number()

        # تحميل المنتجات مرة أخرى للتأكد
        if hasattr(self, 'product_combo'):
            self.load_products()
            
    def load_products(self):
        """تحميل قائمة المنتجات"""
        try:
            print("🔄 تحميل المنتجات...")
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT id, name, barcode, purchase_price FROM products WHERE is_active = 1 ORDER BY name")
            products = cursor.fetchall()
            
            if hasattr(self, 'product_combo') and self.product_combo is not None:
                # حفظ الاختيار الحالي
                current_selection = self.product_combo.currentData()
                
                # مسح القائمة وإعادة تعبئتها
                self.product_combo.clear()
                self.product_combo.addItem("اختر المنتج...", None)
                
                for product in products:
                    # تحسين عرض المنتج مع السعر
                    barcode_text = product[2] if product[2] else "بدون باركود"
                    price_text = f"{product[3]:.2f}" if product[3] else "0.00"
                    display_name = f"{product[1]} | {barcode_text} | {price_text} ج.م"
                    self.product_combo.addItem(display_name, product[0])
                
                print(f"✅ تم تحميل {len(products)} منتج في القائمة")
                print(f"📊 إجمالي العناصر: {self.product_combo.count()}")
                
                # استعادة الاختيار إذا كان موجوداً
                if current_selection:
                    index = self.product_combo.findData(current_selection)
                    if index >= 0:
                        self.product_combo.setCurrentIndex(index)
                
                # إضافة معالج لتحديث السعر عند اختيار منتج (مرة واحدة فقط)
                try:
                    self.product_combo.currentIndexChanged.disconnect()
                except:
                    pass
                self.product_combo.currentIndexChanged.connect(self.on_product_selected)
                
                # تم تحميل المنتجات بنجاح (بدون رسالة)
                if len(products) == 0:
                    print("⚠️ لا توجد منتجات نشطة في قاعدة البيانات")
            else:
                print("❌ القائمة المنسدلة غير موجودة!")
                QMessageBox.critical(self, "خطأ", "القائمة المنسدلة غير موجودة!")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ خطأ في تحميل المنتجات: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المنتجات:\n{str(e)}")

    def init_ui(self):
        """تهيئة واجهة المستخدم - مطابقة تماماً لفاتورة البيع"""
        title = "عرض فاتورة الشراء" if self.invoice_id else "فاتورة شراء جديدة"
        self.setWindowTitle(title)
        self.setGeometry(150, 150, 1200, 800)

        # إضافة اختصارات لوحة المفاتيح
        # اختصار F11 لملء الشاشة
        self.fullscreen_shortcut = QShortcut(QKeySequence("F11"), self)
        self.fullscreen_shortcut.activated.connect(self.toggle_fullscreen)

        # اختصار Ctrl+M لتكبير النافذة
        self.maximize_shortcut = QShortcut(QKeySequence("Ctrl+M"), self)
        self.maximize_shortcut.activated.connect(self.toggle_maximize)

        # تطبيق نفس التصميم المستخدم في فاتورة البيع
        self.setStyleSheet("""
            QWidget {
                background-color: #c8e6c9;
                font-family: 'Arial';
                font-size: 11px;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #81c784;
                border-radius: 3px;
                margin: 2px;
                padding-top: 8px;
                background-color: #e8f5e8;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 3px 0 3px;
                color: #2e7d32;
                font-size: 12px;
                font-weight: bold;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
                border: 1px solid #a5d6a7;
                border-radius: 2px;
                padding: 3px;
                background-color: white;
                font-size: 11px;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
                border: 2px solid #66bb6a;
            }
            QPushButton {
                background-color: #66bb6a;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 3px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #4caf50;
            }
            QPushButton:pressed {
                background-color: #388e3c;
            }
            QTableWidget {
                gridline-color: #a5d6a7;
                background-color: white;
                alternate-background-color: #f1f8e9;
                border: 1px solid #81c784;
                border-radius: 3px;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #c8e6c9;
            }
            QHeaderView::section {
                background-color: #81c784;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
                font-size: 11px;
            }
            QLabel {
                color: #2e7d32;
                font-weight: bold;
                font-size: 11px;
            }
            QTextEdit {
                border: 1px solid #a5d6a7;
                border-radius: 2px;
                background-color: white;
                font-size: 11px;
            }
            QCheckBox {
                color: #2e7d32;
                font-weight: bold;
                font-size: 11px;
            }
            QCheckBox::indicator {
                width: 15px;
                height: 15px;
            }
            QCheckBox::indicator:unchecked {
                border: 1px solid #a5d6a7;
                background-color: white;
                border-radius: 2px;
            }
            QCheckBox::indicator:checked {
                border: 1px solid #66bb6a;
                background-color: #66bb6a;
                border-radius: 2px;
            }
        """)

        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # معلومات الفاتورة - تصميم مطابق لفاتورة البيع
        header_group = QGroupBox("معلومات الفاتورة")
        header_layout = QGridLayout()
        header_layout.setSpacing(8)
        header_layout.setContentsMargins(10, 15, 10, 10)

        # الصف الأول - رقم الفاتورة والتاريخ
        invoice_num_label = QLabel("رقم الفاتورة:")
        invoice_num_label.setStyleSheet("font-weight: bold;")
        header_layout.addWidget(invoice_num_label, 0, 0)

        self.invoice_number_label = QLabel("سيتم إنشاؤه تلقائياً")
        self.invoice_number_label.setStyleSheet("""
            background-color: #fff3e0;
            padding: 4px 8px;
            border: 1px solid #ffcc02;
            border-radius: 2px;
            font-weight: bold;
            color: #e65100;
        """)
        header_layout.addWidget(self.invoice_number_label, 0, 1)

        date_label = QLabel("التاريخ:")
        date_label.setStyleSheet("font-weight: bold;")
        header_layout.addWidget(date_label, 0, 2)

        self.invoice_date = QDateEdit()
        self.invoice_date.setDate(QDate.currentDate())
        self.invoice_date.setEnabled(not self.invoice_id)
        self.invoice_date.setStyleSheet("min-width: 100px;")
        header_layout.addWidget(self.invoice_date, 0, 3)

        # الصف الثاني - المورد ونوع السعر
        supplier_label = QLabel("المورد:")
        supplier_label.setStyleSheet("font-weight: bold;")
        header_layout.addWidget(supplier_label, 1, 0)

        self.supplier_combo = QComboBox()
        self.supplier_combo.setEnabled(not self.invoice_id)

        # إصلاح شريط التمرير الكبير في قائمة المورد
        self.supplier_combo.setMaxVisibleItems(8)
        self.supplier_combo.view().setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        self.supplier_combo.setStyleSheet("""
            QComboBox {
                min-width: 180px;
                min-height: 30px;
                font-size: 12px;
                padding: 5px;
                padding-right: 25px;
            }
            QComboBox QAbstractItemView {
                max-height: 250px;
                min-height: 80px;
                selection-background-color: #4CAF50;
                selection-color: white;
                outline: none;
            }
            QComboBox QAbstractItemView::item {
                height: 25px;
                padding: 3px;
            }
            QComboBox::drop-down {
                width: 25px;
                border: none;
                background: transparent;
            }
            QComboBox::down-arrow {
                width: 0; height: 0; border-left: 5px solid transparent; border-right: 5px solid transparent; border-top: 5px solid #666666;
                
                
            }
            
        """)

        self.load_suppliers()
        header_layout.addWidget(self.supplier_combo, 1, 1)

        price_type_label = QLabel("نوع السعر:")
        price_type_label.setStyleSheet("font-weight: bold;")
        header_layout.addWidget(price_type_label, 1, 2)

        self.price_type_combo = QComboBox()
        self.price_type_combo.addItems(["سعر شراء", "سعر خاص", "سعر الجملة"])
        self.price_type_combo.setEnabled(not self.invoice_id)

        # إضافة السهم لقائمة نوع السعر
        self.price_type_combo.setMaxVisibleItems(8)
        self.price_type_combo.view().setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        self.price_type_combo.setStyleSheet("""
            QComboBox {
                min-width: 100px;
                min-height: 30px;
                font-size: 12px;
                padding: 5px;
                padding-right: 25px;
            }
            QComboBox QAbstractItemView {
                max-height: 200px;
                min-height: 80px;
                selection-background-color: #4CAF50;
                selection-color: white;
                outline: none;
            }
            QComboBox QAbstractItemView::item {
                height: 25px;
                padding: 3px;
            }
            QComboBox::drop-down {
                width: 25px;
                border: none;
                background: transparent;
            }
            QComboBox::down-arrow {
                width: 0; height: 0; border-left: 5px solid transparent; border-right: 5px solid transparent; border-top: 5px solid #666666;
                
                
            }
            
        """)
        header_layout.addWidget(self.price_type_combo, 1, 3)

        # إضافة مساحة فارغة
        header_layout.setColumnStretch(4, 1)

        header_group.setLayout(header_layout)
        main_layout.addWidget(header_group)

        # أصناف الفاتورة - تصميم مطابق لفاتورة البيع
        items_group = QGroupBox("أصناف الفاتورة")
        items_layout = QVBoxLayout()

        # شريط إضافة صنف - تصميم مبسط مثل فاتورة البيع
        if not self.invoice_id:
            add_item_layout = QHBoxLayout()
            add_item_layout.setSpacing(5)

            # المنتج
            product_label = QLabel("المنتج:")
            product_label.setStyleSheet("font-weight: bold; min-width: 50px;")
            add_item_layout.addWidget(product_label)

            self.product_combo = QComboBox()
            self.product_combo.setEnabled(not self.invoice_id)
            self.product_combo.setEditable(True)
            self.product_combo.setInsertPolicy(QComboBox.NoInsert)

            # إصلاح حجم القائمة المنسدلة لإظهار المنتجات بشكل صحيح
            self.product_combo.setMaxVisibleItems(8)  # عرض 8 عناصر كحد أقصى
            self.product_combo.view().setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

            self.product_combo.setStyleSheet("""
                QComboBox {
                    min-width: 200px;
                    min-height: 30px;
                    font-size: 12px;
                    padding: 5px;
                    padding-right: 25px;
                }
                QComboBox QAbstractItemView {
                    max-height: 250px;
                    min-height: 80px;
                    selection-background-color: #4CAF50;
                    selection-color: white;
                    outline: none;
                }
                QComboBox QAbstractItemView::item {
                    height: 25px;
                    padding: 3px;
                }
                QComboBox::drop-down {
                    width: 25px;
                    border: none;
                    background: transparent;
                }
                QComboBox::down-arrow {
                    width: 0; height: 0; border-left: 5px solid transparent; border-right: 5px solid transparent; border-top: 5px solid #666666;
                    
                    
                }
                
            """)

            # إضافة العنصر الافتراضي أولاً
            self.product_combo.addItem("اختر المنتج...", None)

            # إعداد البحث في القائمة
            completer = self.product_combo.completer()
            if completer:
                completer.setCompletionMode(QCompleter.PopupCompletion)
                completer.setFilterMode(Qt.MatchContains)
                completer.setCaseSensitivity(Qt.CaseInsensitive)

            # إعداد القائمة لتظهر عند النقر
            self.product_combo.setInsertPolicy(QComboBox.NoInsert)

            # إعداد القائمة لتظهر عند النقر أو التركيز
            def setup_auto_popup():
                """إعداد القائمة المنسدلة لتظهر تلقائياً"""
                line_edit = self.product_combo.lineEdit()

                if line_edit:
                    # عند النقر على الحقل النصي
                    original_mouse_press = line_edit.mousePressEvent
                    def line_edit_mouse_press(event):
                        original_mouse_press(event)
                        # إظهار القائمة فوراً عند النقر
                        if not self.product_combo.view().isVisible():
                            QTimer.singleShot(10, lambda: self.product_combo.showPopup())
                    line_edit.mousePressEvent = line_edit_mouse_press

                    # عند التركيز على الحقل
                    original_focus_in = line_edit.focusInEvent
                    def line_edit_focus_in(event):
                        original_focus_in(event)
                        # إظهار القائمة عند التركيز
                        if not self.product_combo.view().isVisible():
                            QTimer.singleShot(50, lambda: self.product_combo.showPopup())
                    line_edit.focusInEvent = line_edit_focus_in

                # عند النقر على السهم أو أي مكان في الـ ComboBox
                original_combo_mouse_press = self.product_combo.mousePressEvent
                def combo_mouse_press(event):
                    original_combo_mouse_press(event)
                    # إظهار القائمة عند النقر على أي مكان في الـ ComboBox
                    if not self.product_combo.view().isVisible():
                        QTimer.singleShot(10, lambda: self.product_combo.showPopup())
                self.product_combo.mousePressEvent = combo_mouse_press

            setup_auto_popup()

            # ثم تحميل المنتجات
            self.load_products()
            add_item_layout.addWidget(self.product_combo)

            # زر منتج جديد
            add_product_btn = QPushButton("منتج جديد")
            add_product_btn.setStyleSheet("""
                QPushButton {
                    background-color: #ff9800;
                    color: white;
                    font-weight: bold;
                    padding: 4px 8px;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #f57c00;
                }
            """)
            add_product_btn.clicked.connect(self.add_new_product)
            add_item_layout.addWidget(add_product_btn)

            # زر إعادة تحميل المنتجات
            reload_products_btn = QPushButton("🔄")
            reload_products_btn.setToolTip("إعادة تحميل المنتجات")
            reload_products_btn.setStyleSheet("""
                QPushButton {
                    background-color: #2196f3;
                    color: white;
                    font-weight: bold;
                    padding: 4px 8px;
                    min-width: 30px;
                }
                QPushButton:hover {
                    background-color: #1976d2;
                }
            """)
            reload_products_btn.clicked.connect(self.load_products)
            add_item_layout.addWidget(reload_products_btn)

            # الكمية
            quantity_label = QLabel("الكمية:")
            quantity_label.setStyleSheet("font-weight: bold; min-width: 40px;")
            add_item_layout.addWidget(quantity_label)

            self.quantity_input = QSpinBox()
            self.quantity_input.setMinimum(1)
            self.quantity_input.setMaximum(9999)
            self.quantity_input.setValue(1)
            self.quantity_input.setEnabled(not self.invoice_id)
            self.quantity_input.setStyleSheet("min-width: 60px;")
            add_item_layout.addWidget(self.quantity_input)

            # السعر
            price_label = QLabel("السعر:")
            price_label.setStyleSheet("font-weight: bold; min-width: 40px;")
            add_item_layout.addWidget(price_label)

            self.price_input = QDoubleSpinBox()
            self.price_input.setMaximum(999999.99)
            self.price_input.setDecimals(2)
            self.price_input.setEnabled(not self.invoice_id)
            self.price_input.setStyleSheet("min-width: 80px;")
            add_item_layout.addWidget(self.price_input)

            # زر الإضافة
            add_item_btn = QPushButton("إضافة")
            add_item_btn.setStyleSheet("""
                QPushButton {
                    background-color: #4caf50;
                    color: white;
                    font-weight: bold;
                    padding: 4px 12px;
                    min-width: 60px;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
            """)
            add_item_btn.clicked.connect(self.add_item)
            add_item_layout.addWidget(add_item_btn)

            add_item_layout.addStretch()
            items_layout.addLayout(add_item_layout)

        # جدول الأصناف - تصميم مطابق لفاتورة البيع
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels([
            "اسم الصنف", "الكمية", "السعر", "الإجمالي", "حذف"
        ])

        # تنسيق الجدول مطابق لفاتورة البيع
        header = self.items_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # عمود المنتج قابل للتمدد
        header.setSectionResizeMode(1, QHeaderView.Fixed)    # عمود الكمية ثابت
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # عمود السعر ثابت
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # عمود الإجمالي ثابت
        header.setSectionResizeMode(4, QHeaderView.Fixed)    # عمود الحذف ثابت

        # تحديد عرض الأعمدة مطابق لفاتورة البيع
        self.items_table.setColumnWidth(1, 70)   # الكمية
        self.items_table.setColumnWidth(2, 90)   # السعر
        self.items_table.setColumnWidth(3, 100)  # الإجمالي
        self.items_table.setColumnWidth(4, 60)   # حذف

        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setAlternatingRowColors(True)

        # تحديد ارتفاع الصفوف
        self.items_table.verticalHeader().setDefaultSectionSize(25)
        self.items_table.verticalHeader().setVisible(False)

        items_layout.addWidget(self.items_table)
        items_group.setLayout(items_layout)
        main_layout.addWidget(items_group)

        # إجماليات الفاتورة - تصميم مطابق لفاتورة البيع
        totals_frame = QFrame()
        totals_frame.setStyleSheet("""
            QFrame {
                background-color: #e8f5e8;
                border: 1px solid #81c784;
                border-radius: 3px;
                margin: 2px;
            }
        """)
        totals_layout = QHBoxLayout()
        totals_layout.setSpacing(15)
        totals_layout.setContentsMargins(10, 8, 10, 8)

        # المجموع الفرعي
        subtotal_label = QLabel("المجموع الفرعي:")
        subtotal_label.setStyleSheet("font-weight: bold;")
        totals_layout.addWidget(subtotal_label)

        self.subtotal_label = QLabel("0.00")
        self.subtotal_label.setStyleSheet("""
            background-color: white;
            padding: 3px 8px;
            border: 1px solid #a5d6a7;
            border-radius: 2px;
            font-weight: bold;
            min-width: 80px;
        """)
        totals_layout.addWidget(self.subtotal_label)

        # الخصم
        discount_label = QLabel("الخصم:")
        discount_label.setStyleSheet("font-weight: bold;")
        totals_layout.addWidget(discount_label)

        self.discount_input = QDoubleSpinBox()
        self.discount_input.setMaximum(999999.99)
        self.discount_input.setDecimals(2)
        self.discount_input.setEnabled(not self.invoice_id)
        self.discount_input.valueChanged.connect(self.calculate_totals)
        self.discount_input.setStyleSheet("min-width: 80px;")
        totals_layout.addWidget(self.discount_input)

        # الإجمالي النهائي
        total_label = QLabel("الإجمالي النهائي:")
        total_label.setStyleSheet("font-weight: bold;")
        totals_layout.addWidget(total_label)

        self.total_label = QLabel("0.00")
        self.total_label.setStyleSheet("""
            background-color: #fff3e0;
            padding: 3px 8px;
            border: 1px solid #ffcc02;
            border-radius: 2px;
            font-weight: bold;
            color: #e65100;
            min-width: 80px;
        """)
        totals_layout.addWidget(self.total_label)

        totals_layout.addStretch()
        totals_frame.setLayout(totals_layout)
        main_layout.addWidget(totals_frame)

        # طريقة الدفع - تصميم مطابق لفاتورة البيع
        if not self.invoice_id:
            payment_group = QGroupBox("طريقة الدفع")
            payment_layout = QHBoxLayout()
            payment_layout.setSpacing(10)
            payment_layout.setContentsMargins(10, 10, 10, 10)

            # طريقة الدفع
            payment_method_label = QLabel("طريقة الدفع:")
            payment_method_label.setStyleSheet("font-weight: bold; min-width: 80px;")
            payment_layout.addWidget(payment_method_label)

            self.payment_method_combo = QComboBox()
            payment_methods = {"cash": "نقدي", "bank": "بنكي", "credit": "آجل"}
            for key, value in payment_methods.items():
                self.payment_method_combo.addItem(value, key)

            # إضافة السهم لقائمة طريقة الدفع
            self.payment_method_combo.setMaxVisibleItems(8)
            self.payment_method_combo.view().setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

            self.payment_method_combo.setStyleSheet("""
                QComboBox {
                    min-width: 120px;
                    min-height: 30px;
                    font-size: 12px;
                    padding: 5px;
                    padding-right: 25px;
                }
                QComboBox QAbstractItemView {
                    max-height: 200px;
                    min-height: 80px;
                    selection-background-color: #4CAF50;
                    selection-color: white;
                    outline: none;
                }
                QComboBox QAbstractItemView::item {
                    height: 25px;
                    padding: 3px;
                }
                QComboBox::drop-down {
                    width: 25px;
                    border: none;
                    background: transparent;
                }
                QComboBox::down-arrow {
                    width: 0; height: 0; border-left: 5px solid transparent; border-right: 5px solid transparent; border-top: 5px solid #666666;
                    
                    
                }
                
            """)
            payment_layout.addWidget(self.payment_method_combo)

            # المبلغ المدفوع
            paid_amount_label = QLabel("المبلغ المدفوع:")
            paid_amount_label.setStyleSheet("font-weight: bold; min-width: 80px;")
            payment_layout.addWidget(paid_amount_label)

            self.paid_amount_input = QDoubleSpinBox()
            self.paid_amount_input.setMaximum(999999.99)
            self.paid_amount_input.setDecimals(2)
            self.paid_amount_input.setStyleSheet("min-width: 100px;")
            self.paid_amount_input.valueChanged.connect(self.calculate_remaining)
            payment_layout.addWidget(self.paid_amount_input)

            # المتبقي
            remaining_label = QLabel("المتبقي:")
            remaining_label.setStyleSheet("font-weight: bold; min-width: 50px;")
            payment_layout.addWidget(remaining_label)

            self.remaining_label = QLabel("0.00")
            self.remaining_label.setStyleSheet("""
                background-color: #ffebee;
                padding: 3px 8px;
                border: 1px solid #f44336;
                border-radius: 2px;
                font-weight: bold;
                color: #c62828;
                min-width: 80px;
            """)
            payment_layout.addWidget(self.remaining_label)

            payment_layout.addStretch()
            payment_group.setLayout(payment_layout)
            main_layout.addWidget(payment_group)

        # الأزرار - تصميم مطابق لفاتورة البيع
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(8)
        buttons_layout.setContentsMargins(10, 10, 10, 10)

        if not self.invoice_id:
            save_btn = QPushButton("حفظ الفاتورة")
            save_btn.setStyleSheet("""
                QPushButton {
                    background-color: #4caf50;
                    color: white;
                    font-weight: bold;
                    padding: 8px 16px;
                    border-radius: 3px;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
            """)
            save_btn.clicked.connect(self.save_invoice)
            buttons_layout.addWidget(save_btn)

        cancel_btn = QPushButton("إغلاق")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 3px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        cancel_btn.clicked.connect(self.close)
        buttons_layout.addWidget(cancel_btn)

        buttons_layout.addStretch()
        
        buttons_widget = QWidget()
        buttons_widget.setLayout(buttons_layout)
        main_layout.addWidget(buttons_widget)

        self.setLayout(main_layout)

    def toggle_fullscreen(self):
        """تبديل وضع ملء الشاشة"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()

    def toggle_maximize(self):
        """تبديل وضع التكبير"""
        if self.isMaximized():
            self.showNormal()
        else:
            self.showMaximized()

    def create_toolbar(self):
        """إنشاء شريط الأدوات العلوي"""
        self.toolbar_frame = QFrame()
        self.toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        self.toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: #b3e5fc;
                border: 1px solid #4fc3f7;
                border-radius: 3px;
                padding: 4px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(self.toolbar_frame)
        toolbar_layout.setContentsMargins(6, 4, 6, 4)
        
        # عنوان النافذة
        title_label = QLabel("🧾 فاتورة شراء")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #01579b;
                padding: 4px;
            }
        """)
        toolbar_layout.addWidget(title_label)
        
        toolbar_layout.addStretch()
        
        # أزرار التحكم
        minimize_btn = QPushButton("🗕")
        minimize_btn.setFixedSize(30, 25)
        minimize_btn.clicked.connect(self.showMinimized)
        toolbar_layout.addWidget(minimize_btn)
        
        maximize_btn = QPushButton("🗖")
        maximize_btn.setFixedSize(30, 25)
        maximize_btn.clicked.connect(self.toggle_maximize)
        toolbar_layout.addWidget(maximize_btn)
        
        close_btn = QPushButton("✕")
        close_btn.setFixedSize(30, 25)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        close_btn.clicked.connect(self.close)
        toolbar_layout.addWidget(close_btn)

    def create_invoice_info_section(self):
        """إنشاء قسم معلومات الفاتورة"""
        self.invoice_info_group = QGroupBox("معلومات الفاتورة")
        layout = QFormLayout()
        layout.setSpacing(4)

        # رقم الفاتورة
        self.invoice_number_edit = QLineEdit()
        self.invoice_number_edit.setReadOnly(True)
        layout.addRow("رقم الفاتورة:", self.invoice_number_edit)

        # تاريخ الفاتورة
        self.invoice_date_edit = QDateEdit()
        self.invoice_date_edit.setDate(QDate.currentDate())
        self.invoice_date_edit.setCalendarPopup(True)
        layout.addRow("تاريخ الفاتورة:", self.invoice_date_edit)

        # تاريخ الاستحقاق
        self.due_date_edit = QDateEdit()
        self.due_date_edit.setDate(QDate.currentDate().addDays(30))
        self.due_date_edit.setCalendarPopup(True)
        layout.addRow("تاريخ الاستحقاق:", self.due_date_edit)

        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(60)
        layout.addRow("ملاحظات:", self.notes_edit)

        self.invoice_info_group.setLayout(layout)

    def create_supplier_section(self):
        """إنشاء قسم معلومات المورد"""
        self.supplier_group = QGroupBox("معلومات المورد")
        layout = QFormLayout()
        layout.setSpacing(4)

        # اختيار المورد
        supplier_layout = QHBoxLayout()
        self.supplier_combo = QComboBox()
        self.supplier_combo.setEditable(True)
        self.supplier_combo.currentTextChanged.connect(self.on_supplier_changed)
        supplier_layout.addWidget(self.supplier_combo)

        # زر إضافة مورد جديد
        add_supplier_btn = QPushButton("➕")
        add_supplier_btn.setFixedSize(30, 25)
        add_supplier_btn.clicked.connect(self.add_new_supplier)
        supplier_layout.addWidget(add_supplier_btn)

        layout.addRow("المورد:", supplier_layout)

        # معلومات المورد
        self.supplier_phone_label = QLabel("-")
        layout.addRow("الهاتف:", self.supplier_phone_label)

        self.supplier_address_label = QLabel("-")
        layout.addRow("العنوان:", self.supplier_address_label)

        self.supplier_balance_label = QLabel("0.00")
        layout.addRow("الرصيد:", self.supplier_balance_label)

        self.supplier_group.setLayout(layout)

        # تحميل الموردين
        self.load_suppliers()

    def create_payment_section(self):
        """إنشاء قسم معلومات الدفع"""
        self.payment_group = QGroupBox("معلومات الدفع")
        layout = QFormLayout()
        layout.setSpacing(4)

        # طريقة الدفع
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(["نقدي", "تحويل بنكي", "آجل"])
        self.payment_method_combo.currentTextChanged.connect(self.on_payment_method_changed)
        layout.addRow("طريقة الدفع:", self.payment_method_combo)

        # المبلغ المدفوع
        self.paid_amount_edit = QDoubleSpinBox()
        self.paid_amount_edit.setMaximum(999999.99)
        self.paid_amount_edit.setDecimals(2)
        self.paid_amount_edit.valueChanged.connect(self.calculate_totals)
        layout.addRow("المبلغ المدفوع:", self.paid_amount_edit)

        # حالة الدفع
        self.payment_status_label = QLabel("غير مدفوعة")
        layout.addRow("حالة الدفع:", self.payment_status_label)

        self.payment_group.setLayout(layout)

    def create_items_section(self):
        """إنشاء قسم الأصناف"""
        self.items_group = QGroupBox("أصناف الفاتورة")
        layout = QVBoxLayout()

        # شريط أدوات الأصناف
        items_toolbar = QHBoxLayout()

        add_item_btn = QPushButton("➕ إضافة صنف")
        add_item_btn.clicked.connect(self.add_item)
        items_toolbar.addWidget(add_item_btn)

        remove_item_btn = QPushButton("➖ حذف صنف")
        remove_item_btn.clicked.connect(self.remove_item)
        items_toolbar.addWidget(remove_item_btn)

        items_toolbar.addStretch()

        # بحث سريع
        search_label = QLabel("بحث:")
        items_toolbar.addWidget(search_label)

        self.item_search_edit = QLineEdit()
        self.item_search_edit.setPlaceholderText("اسم المنتج أو الكود...")
        items_toolbar.addWidget(self.item_search_edit)

        layout.addLayout(items_toolbar)

        # جدول الأصناف
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "المنتج", "الكود", "الكمية", "سعر الشراء", "الخصم", "الإجمالي", "حذف"
        ])

        # تنسيق الجدول
        header = self.items_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # المنتج
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # الكود
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الكمية
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # السعر
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # الخصم
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الإجمالي
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # حذف

        self.items_table.setAlternatingRowColors(True)
        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)

        layout.addWidget(self.items_table)
        self.items_group.setLayout(layout)

    def create_totals_section(self):
        """إنشاء قسم الإجماليات"""
        self.totals_group = QGroupBox("الإجماليات")
        layout = QFormLayout()
        layout.setSpacing(4)

        # إجمالي قبل الخصم
        self.subtotal_label = QLabel("0.00")
        self.subtotal_label.setStyleSheet("font-weight: bold; font-size: 12px;")
        layout.addRow("الإجمالي الفرعي:", self.subtotal_label)

        # إجمالي الخصم
        self.discount_label = QLabel("0.00")
        layout.addRow("إجمالي الخصم:", self.discount_label)

        # الضريبة
        tax_layout = QHBoxLayout()
        self.tax_checkbox = QCheckBox("ضريبة القيمة المضافة")
        self.tax_rate_edit = QDoubleSpinBox()
        self.tax_rate_edit.setMaximum(100)
        self.tax_rate_edit.setValue(14)
        self.tax_rate_edit.setSuffix("%")
        self.tax_rate_edit.valueChanged.connect(self.calculate_totals)
        tax_layout.addWidget(self.tax_checkbox)
        tax_layout.addWidget(self.tax_rate_edit)
        layout.addRow("الضريبة:", tax_layout)

        self.tax_amount_label = QLabel("0.00")
        layout.addRow("مبلغ الضريبة:", self.tax_amount_label)

        # الإجمالي النهائي
        self.total_label = QLabel("0.00")
        self.total_label.setStyleSheet("""
            font-weight: bold;
            font-size: 14px;
            color: #01579b;
            background-color: #e1f5fe;
            padding: 4px;
            border-radius: 3px;
        """)
        layout.addRow("الإجمالي النهائي:", self.total_label)

        # المتبقي
        self.remaining_label = QLabel("0.00")
        self.remaining_label.setStyleSheet("font-weight: bold; color: #d32f2f;")
        layout.addRow("المتبقي:", self.remaining_label)

        self.totals_group.setLayout(layout)

    def create_bottom_toolbar(self):
        """إنشاء شريط الأدوات السفلي"""
        self.bottom_toolbar_frame = QFrame()
        self.bottom_toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        self.bottom_toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: #b3e5fc;
                border: 1px solid #4fc3f7;
                border-radius: 3px;
                padding: 4px;
            }
        """)

        toolbar_layout = QHBoxLayout(self.bottom_toolbar_frame)
        toolbar_layout.setContentsMargins(6, 4, 6, 4)

        # أزرار العمليات
        save_btn = QPushButton("💾 حفظ الفاتورة")
        save_btn.clicked.connect(self.save_invoice)
        toolbar_layout.addWidget(save_btn)

        preview_btn = QPushButton("👁️ معاينة")
        preview_btn.clicked.connect(self.preview_invoice)
        toolbar_layout.addWidget(preview_btn)

        print_btn = QPushButton("🖨️ طباعة")
        print_btn.clicked.connect(self.print_invoice)
        toolbar_layout.addWidget(print_btn)

        toolbar_layout.addStretch()

        # معلومات المستخدم
        user_label = QLabel(f"المستخدم: {self.user_data.get('username', 'غير محدد')}")
        user_label.setStyleSheet("color: #01579b; font-weight: bold;")
        toolbar_layout.addWidget(user_label)

        # زر الإغلاق
        close_btn = QPushButton("✕ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        close_btn.clicked.connect(self.close)
        toolbar_layout.addWidget(close_btn)

    def generate_invoice_number(self):
        """توليد رقم فاتورة جديد"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # الحصول على آخر رقم فاتورة
            cursor.execute("""
                SELECT MAX(CAST(SUBSTR(invoice_number, 3) AS INTEGER))
                FROM purchase_invoices
                WHERE invoice_number LIKE 'P-%'
            """)
            result = cursor.fetchone()

            if result[0]:
                next_number = result[0] + 1
            else:
                next_number = 1

            invoice_number = f"P-{next_number:06d}"
            self.invoice_number_label.setText(invoice_number)

            conn.close()

        except Exception as e:
            print(f"خطأ في توليد رقم الفاتورة: {e}")
            # رقم افتراضي
            self.invoice_number_label.setText(f"P-{datetime.now().strftime('%Y%m%d%H%M%S')}")

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute("SELECT id, name FROM suppliers WHERE is_active = 1 ORDER BY name")
            suppliers = cursor.fetchall()

            self.supplier_combo.clear()
            self.supplier_combo.addItem("اختر المورد...", None)

            for supplier in suppliers:
                self.supplier_combo.addItem(supplier[1], supplier[0])

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الموردين:\n{str(e)}")
    
    def load_products(self):
        """تحميل قائمة المنتجات"""
        try:
            print("🔄 بدء تحميل المنتجات...")
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute("SELECT id, name, barcode, purchase_price FROM products WHERE is_active = 1 ORDER BY name")
            products = cursor.fetchall()
            print(f"📦 تم العثور على {len(products)} منتج")

            if hasattr(self, 'product_combo') and self.product_combo is not None:
                # حفظ الاختيار الحالي
                current_selection = self.product_combo.currentData()

                # مسح القائمة وإعادة تعبئتها
                self.product_combo.clear()
                self.product_combo.addItem("اختر المنتج...", None)

                for product in products:
                    # تحسين عرض المنتج مع السعر
                    barcode_text = product[2] if product[2] else "بدون باركود"
                    price_text = f"{product[3]:.2f}" if product[3] else "0.00"
                    display_name = f"{product[1]} | {barcode_text} | {price_text} ج.م"
                    self.product_combo.addItem(display_name, product[0])

                print(f"✅ تم تحميل {len(products)} منتج في القائمة")
                print(f"📊 إجمالي العناصر: {self.product_combo.count()}")

                # استعادة الاختيار إذا كان موجوداً
                if current_selection:
                    index = self.product_combo.findData(current_selection)
                    if index >= 0:
                        self.product_combo.setCurrentIndex(index)

                # إضافة معالج لتحديث السعر عند اختيار منتج (مرة واحدة فقط)
                try:
                    self.product_combo.currentIndexChanged.disconnect()
                except:
                    pass
                self.product_combo.currentIndexChanged.connect(self.on_product_selected)

                # تم تحميل المنتجات بنجاح (بدون رسالة)
                if len(products) == 0:
                    print("⚠️ لا توجد منتجات نشطة في قاعدة البيانات")
            else:
                print("❌ القائمة المنسدلة غير موجودة!")
                QMessageBox.critical(self, "خطأ", "القائمة المنسدلة غير موجودة!")

            conn.close()

        except Exception as e:
            print(f"❌ خطأ في تحميل المنتجات: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المنتجات:\n{str(e)}")
    
    def load_products_silently(self):
        """تحميل قائمة المنتجات بدون رسائل"""
        try:
            print("🔄 تحميل المنتجات بصمت...")
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute("SELECT id, name, barcode, purchase_price FROM products WHERE is_active = 1 ORDER BY name")
            products = cursor.fetchall()

            if hasattr(self, 'product_combo') and self.product_combo is not None:
                # حفظ الاختيار الحالي
                current_selection = self.product_combo.currentData()

                # مسح القائمة وإعادة تعبئتها
                self.product_combo.clear()
                self.product_combo.addItem("اختر المنتج...", None)

                for product in products:
                    # تحسين عرض المنتج مع السعر
                    barcode_text = product[2] if product[2] else "بدون باركود"
                    price_text = f"{product[3]:.2f}" if product[3] else "0.00"
                    display_name = f"{product[1]} | {barcode_text} | {price_text} ج.م"
                    self.product_combo.addItem(display_name, product[0])

                print(f"✅ تم تحميل {len(products)} منتج بصمت")

                # استعادة الاختيار إذا كان موجوداً
                if current_selection:
                    index = self.product_combo.findData(current_selection)
                    if index >= 0:
                        self.product_combo.setCurrentIndex(index)

                # إضافة معالج لتحديث السعر عند اختيار منتج (مرة واحدة فقط)
                try:
                    self.product_combo.currentIndexChanged.disconnect()
                except:
                    pass
                self.product_combo.currentIndexChanged.connect(self.on_product_selected)

            conn.close()

        except Exception as e:
            print(f"❌ خطأ في تحميل المنتجات بصمت: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المنتجات:\n{str(e)}")

    def on_product_selected(self):
        """عند اختيار منتج من القائمة"""
        if not hasattr(self, 'product_combo') or not hasattr(self, 'price_input'):
            return

        product_id = self.product_combo.currentData()
        if product_id:
            try:
                conn = self.db_manager.get_connection()
                cursor = conn.cursor()

                # تعديل الاستعلام ليتوافق مع هيكل جدول المنتجات
                cursor.execute("""
                    SELECT purchase_price, retail_price, current_stock, unit, barcode, name, description 
                    FROM products 
                    WHERE id = ?
                """, (product_id,))
                result = cursor.fetchone()

                if result and hasattr(self, 'price_input'):
                    purchase_price = result[0] or 0
                    selling_price = result[1] or 0
                    stock = result[2] or 0
                    unit = result[3] or "قطعة"
                    barcode = result[4] or "بدون باركود"
                    name = result[5] or "غير محدد"
                    description = result[6] or ""
                    
                    # تحديث السعر
                    self.price_input.setValue(purchase_price)
                    
                    # عرض معلومات المنتج في شريط الحالة أو تلميح
                    tooltip_text = f"اسم المنتج: {name}\n"
                    tooltip_text += f"الباركود: {barcode}\n"
                    tooltip_text += f"سعر الشراء: {purchase_price:.2f} ج.م\n"
                    tooltip_text += f"سعر البيع: {selling_price:.2f} ج.م\n"
                    tooltip_text += f"الكمية المتوفرة: {stock} {unit}"
                    if description:
                        tooltip_text += f"\nالوصف: {description}"
                    
                    self.product_combo.setToolTip(tooltip_text)
                    
                    # طباعة معلومات المنتج في وحدة التحكم
                    print(f"📦 تم اختيار المنتج: {name}")
                    print(f"   💰 سعر الشراء: {purchase_price:.2f} ج.م")
                    print(f"   💰 سعر البيع: {selling_price:.2f} ج.م")
                    print(f"   📊 الكمية المتوفرة: {stock} {unit}")
                    print(f"   🏷️ الباركود: {barcode}")
                else:
                    print("⚠️ لم يتم العثور على معلومات المنتج")

                conn.close()

            except Exception as e:
                print(f"❌ خطأ في تحميل معلومات المنتج: {e}")
                import traceback
                traceback.print_exc()
                QMessageBox.warning(self, "خطأ", f"فشل في تحميل معلومات المنتج:\n{str(e)}")

    def on_supplier_changed(self):
        """عند تغيير المورد"""
        supplier_id = self.supplier_combo.currentData()
        if supplier_id:
            try:
                conn = self.db_manager.get_connection()
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT name, phone, address, current_balance
                    FROM suppliers
                    WHERE id = ?
                """, (supplier_id,))

                supplier = cursor.fetchone()
                if supplier:
                    self.supplier_phone_label.setText(supplier[1] or "-")
                    self.supplier_address_label.setText(supplier[2] or "-")
                    self.supplier_balance_label.setText(f"{supplier[3] or 0:.2f}")

                conn.close()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات المورد:\n{str(e)}")
        else:
            self.supplier_phone_label.setText("-")
            self.supplier_address_label.setText("-")
            self.supplier_balance_label.setText("0.00")

    def add_new_supplier(self):
        """إضافة مورد جديد"""
        QMessageBox.information(self, "إضافة مورد", "يرجى إضافة الموردين من قائمة الموردين الرئيسية")

    def on_payment_method_changed(self):
        """عند تغيير طريقة الدفع"""
        payment_method = self.payment_method_combo.currentText()

        if payment_method == "نقدي":
            # في حالة النقدي، تعيين المبلغ المدفوع للإجمالي
            total = float(self.total_label.text().replace(",", ""))
            self.paid_amount_edit.setValue(total)
        elif payment_method == "آجل":
            # في حالة الآجل، تعيين المبلغ المدفوع لصفر
            self.paid_amount_edit.setValue(0)

        self.calculate_totals()

    def add_item(self):
        """إضافة صنف جديد"""
        try:
            # التحقق من البيانات
            product_id = self.product_combo.currentData()
            if not product_id:
                QMessageBox.warning(self, "خطأ", "يرجى اختيار منتج")
                return
            
            quantity = self.quantity_input.value()
            price = self.price_input.value()
            
            if quantity <= 0:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال كمية صحيحة")
                return
            
            if price <= 0:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال سعر صحيح")
                return
            
            # إضافة الصنف إلى الجدول
            product_name = self.product_combo.currentText()
            total = quantity * price
            
            # إضافة الصنف لقائمة الفاتورة
            item = {
                'product_id': product_id,
                'product_name': product_name.split(' - ')[0],  # إزالة الباركود من الاسم
                'quantity': quantity,
                'price': price,
                'total': total
            }
            
            self.invoice_items.append(item)
            self.update_items_table()
            self.calculate_totals()
            
            # مسح الحقول
            self.product_combo.setCurrentIndex(0)
            self.quantity_input.setValue(1)
            self.price_input.setValue(0)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة الصنف:\n{str(e)}")
    
    def update_items_table(self):
        """تحديث جدول الأصناف"""
        self.items_table.setRowCount(len(self.invoice_items))
        
        for row, item in enumerate(self.invoice_items):
            self.items_table.setItem(row, 0, QTableWidgetItem(item['product_name']))
            self.items_table.setItem(row, 1, QTableWidgetItem(str(item['quantity'])))
            self.items_table.setItem(row, 2, QTableWidgetItem(f"{item['price']:.2f}"))
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{item['total']:.2f}"))
            
            # زر الحذف
            delete_btn = QPushButton("حذف")
            delete_btn.clicked.connect(lambda: self.remove_item(row))
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    padding: 2px 6px;
                    border-radius: 2px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            self.items_table.setCellWidget(row, 4, delete_btn)
    
    def remove_item(self, row):
        """حذف صنف من الفاتورة"""
        if 0 <= row < len(self.invoice_items):
            del self.invoice_items[row]
            self.update_items_table()
            self.calculate_totals()
    
    def calculate_remaining(self):
        """حساب المبلغ المتبقي"""
        try:
            total = float(self.total_label.text())
            paid = self.paid_amount_input.value()
            remaining = total - paid
            self.remaining_label.setText(f"{remaining:.2f}")
        except:
            self.remaining_label.setText("0.00")
    
    def add_new_product(self):
        """إضافة منتج جديد بمعلومات كاملة"""
        try:
            from PyQt5.QtWidgets import QInputDialog, QFormLayout, QDialogButtonBox, QVBoxLayout, QGroupBox
            
            # إنشاء نافذة مخصصة لإضافة منتج
            dialog = QDialog(self)
            dialog.setWindowTitle("إضافة منتج جديد")
            dialog.setModal(True)
            dialog.resize(500, 500)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout()
            
            # مجموعة المعلومات الأساسية
            basic_group = QGroupBox("معلومات المنتج الأساسية")
            basic_layout = QFormLayout()
            
            # اسم المنتج
            name_input = QLineEdit()
            name_input.setPlaceholderText("أدخل اسم المنتج")
            basic_layout.addRow("اسم المنتج:", name_input)
            
            # الباركود
            barcode_input = QLineEdit()
            barcode_input.setPlaceholderText("أدخل الباركود (اختياري)")
            basic_layout.addRow("الباركود:", barcode_input)
            
            # الوحدة
            unit_input = QLineEdit()
            unit_input.setText("قطعة")
            unit_input.setPlaceholderText("مثال: قطعة، كيلو، لتر")
            basic_layout.addRow("الوحدة:", unit_input)
            
            # الكمية الأولية
            initial_stock_input = QSpinBox()
            initial_stock_input.setMaximum(999999)
            initial_stock_input.setValue(0)
            initial_stock_input.setSuffix(" قطعة")
            basic_layout.addRow("الكمية الأولية:", initial_stock_input)
            
            # الحد الأدنى للتنبيه
            min_stock_input = QSpinBox()
            min_stock_input.setMaximum(999999)
            min_stock_input.setValue(5)
            min_stock_input.setSuffix(" قطعة")
            basic_layout.addRow("الحد الأدنى للتنبيه:", min_stock_input)
            
            basic_group.setLayout(basic_layout)
            main_layout.addWidget(basic_group)
            
            # مجموعة الأسعار
            price_group = QGroupBox("أسعار المنتج")
            price_layout = QFormLayout()
            
            # سعر الشراء
            purchase_price_input = QDoubleSpinBox()
            purchase_price_input.setMaximum(999999.99)
            purchase_price_input.setDecimals(2)
            purchase_price_input.setValue(0)
            purchase_price_input.setSuffix(" ج.م")
            price_layout.addRow("سعر الشراء:", purchase_price_input)
            
            # سعر البيع بالجملة
            wholesale_price_input = QDoubleSpinBox()
            wholesale_price_input.setMaximum(999999.99)
            wholesale_price_input.setDecimals(2)
            wholesale_price_input.setValue(0)
            wholesale_price_input.setSuffix(" ج.م")
            price_layout.addRow("سعر البيع بالجملة:", wholesale_price_input)
            
            # سعر البيع نصف الجملة
            semi_wholesale_price_input = QDoubleSpinBox()
            semi_wholesale_price_input.setMaximum(999999.99)
            semi_wholesale_price_input.setDecimals(2)
            semi_wholesale_price_input.setValue(0)
            semi_wholesale_price_input.setSuffix(" ج.م")
            price_layout.addRow("سعر البيع نصف الجملة:", semi_wholesale_price_input)
            
            # سعر البيع بالقطاعي (التجزئة)
            retail_price_input = QDoubleSpinBox()
            retail_price_input.setMaximum(999999.99)
            retail_price_input.setDecimals(2)
            retail_price_input.setValue(0)
            retail_price_input.setSuffix(" ج.م")
            price_layout.addRow("سعر البيع بالقطاعي:", retail_price_input)
            
            price_group.setLayout(price_layout)
            main_layout.addWidget(price_group)
            
            # الوصف
            description_group = QGroupBox("وصف المنتج")
            description_layout = QVBoxLayout()
            description_input = QTextEdit()
            description_input.setPlaceholderText("وصف المنتج (اختياري)")
            description_layout.addWidget(description_input)
            description_group.setLayout(description_layout)
            main_layout.addWidget(description_group)
            
            # أزرار
            buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            buttons.accepted.connect(dialog.accept)
            buttons.rejected.connect(dialog.reject)
            
            # تخصيص الأزرار للعربية
            buttons.button(QDialogButtonBox.Ok).setText("إضافة")
            buttons.button(QDialogButtonBox.Cancel).setText("إلغاء")
            
            main_layout.addWidget(buttons)
            dialog.setLayout(main_layout)
            
            # عرض النافذة
            if dialog.exec_() == QDialog.Accepted:
                name = name_input.text().strip()
                barcode = barcode_input.text().strip() or None
                purchase_price = purchase_price_input.value()
                wholesale_price = wholesale_price_input.value()
                semi_wholesale_price = semi_wholesale_price_input.value()
                retail_price = retail_price_input.value()
                initial_stock = initial_stock_input.value()
                min_stock = min_stock_input.value()
                unit = unit_input.text().strip() or "قطعة"
                description = description_input.toPlainText().strip() or None
                
                if not name:
                    QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المنتج")
                    return
                try:
                    # إضافة المنتج إلى قاعدة البيانات
                    conn = self.db_manager.get_connection()
                    cursor = conn.cursor()
                    
                    cursor.execute('''
                        INSERT INTO products (
                            name, barcode, purchase_price, wholesale_price, 
                            semi_wholesale_price, retail_price, current_stock, 
                            min_stock_alert, unit, description, is_active
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
                    ''', (
                        name, barcode, purchase_price, wholesale_price,
                        semi_wholesale_price, retail_price, initial_stock,
                        min_stock, unit, description
                    ))
                    
                    product_id = cursor.lastrowid
                    conn.commit()
                    conn.close()
                    
                    # إعادة تحميل قائمة المنتجات
                    self.load_products()
                    
                    # تحديد المنتج المضاف حديثاً
                    for i in range(self.product_combo.count()):
                        if self.product_combo.itemData(i) == product_id:
                            self.product_combo.setCurrentIndex(i)
                            break
                    
                    # تحديث السعر في حقل الإدخال
                    self.price_input.setValue(purchase_price)
                    
                    QMessageBox.information(self, "تمت الإضافة", 
                        f"تمت إضافة المنتج '{name}' بنجاح\n\n"
                        f"المعلومات المضافة:\n"
                        f"- سعر الشراء: {purchase_price:.2f} ج.م\n"
                        f"- سعر البيع بالجملة: {wholesale_price:.2f} ج.م\n"
                        f"- سعر البيع نصف الجملة: {semi_wholesale_price:.2f} ج.م\n"
                        f"- سعر البيع بالقطاعي: {retail_price:.2f} ج.م\n"
                        f"- الكمية الأولية: {initial_stock} {unit}"
                    )
                    
                except Exception as e:
                    print(f"❌ خطأ في إضافة المنتج إلى قاعدة البيانات: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    QMessageBox.critical(self, "خطأ في قاعدة البيانات", f"تعذر إضافة المنتج إلى قاعدة البيانات:\n{str(e)}")
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء نافذة إضافة المنتج: {str(e)}")
            import traceback
            traceback.print_exc()
                
            QMessageBox.critical(self, "خطأ", f"تعذر إضافة المنتج:\n{str(e)}")

    def add_product_to_table(self, product):
        """إضافة منتج إلى الجدول"""
        row = self.items_table.rowCount()
        self.items_table.insertRow(row)

        # اسم المنتج
        product_item = QTableWidgetItem(product['name'])
        product_item.setData(Qt.UserRole, product['id'])
        self.items_table.setItem(row, 0, product_item)

        # كود المنتج
        self.items_table.setItem(row, 1, QTableWidgetItem(product.get('barcode', product.get('code', ''))))

        # الكمية
        quantity_spin = QSpinBox()
        quantity_spin.setMinimum(1)
        quantity_spin.setMaximum(9999)
        quantity_spin.setValue(1)
        quantity_spin.valueChanged.connect(self.calculate_row_total)
        self.items_table.setCellWidget(row, 2, quantity_spin)

        # سعر الشراء
        price_spin = QDoubleSpinBox()
        price_spin.setMaximum(999999.99)
        price_spin.setDecimals(2)
        price_spin.setValue(product.get('purchase_price', 0))
        price_spin.valueChanged.connect(self.calculate_row_total)
        self.items_table.setCellWidget(row, 3, price_spin)

        # الخصم
        discount_spin = QDoubleSpinBox()
        discount_spin.setMaximum(999999.99)
        discount_spin.setDecimals(2)
        discount_spin.setValue(0)
        discount_spin.valueChanged.connect(self.calculate_row_total)
        self.items_table.setCellWidget(row, 4, discount_spin)

        # الإجمالي
        total_item = QTableWidgetItem("0.00")
        total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
        self.items_table.setItem(row, 5, total_item)

        # زر الحذف
        delete_btn = QPushButton("🗑️")
        delete_btn.setFixedSize(30, 25)
        delete_btn.clicked.connect(lambda: self.remove_item_row(row))
        self.items_table.setCellWidget(row, 6, delete_btn)

        # حساب الإجمالي
        self.calculate_row_total()

    def calculate_row_total(self):
        """حساب إجمالي الصف"""
        sender = self.sender()
        if sender:
            # العثور على الصف
            for row in range(self.items_table.rowCount()):
                quantity_widget = self.items_table.cellWidget(row, 2)
                price_widget = self.items_table.cellWidget(row, 3)
                discount_widget = self.items_table.cellWidget(row, 4)

                if sender in [quantity_widget, price_widget, discount_widget]:
                    quantity = quantity_widget.value()
                    price = price_widget.value()
                    discount = discount_widget.value()

                    total = (quantity * price) - discount

                    total_item = self.items_table.item(row, 5)
                    total_item.setText(f"{total:.2f}")
                    break

        self.calculate_totals()

    def remove_item(self):
        """حذف الصنف المحدد"""
        current_row = self.items_table.currentRow()
        if current_row >= 0:
            self.items_table.removeRow(current_row)
            self.calculate_totals()
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار صنف للحذف")

    def remove_item_row(self, row):
        """حذف صف محدد"""
        self.items_table.removeRow(row)
        self.calculate_totals()

    def calculate_totals(self):
        """حساب الإجماليات - مطابق لفاتورة البيع"""
        subtotal = sum(item['total'] for item in self.invoice_items)
        discount = self.discount_input.value()
        total = subtotal - discount
        
        self.subtotal_label.setText(f"{subtotal:.2f}")
        self.total_label.setText(f"{total:.2f}")
        
        # حساب المتبقي إذا كان موجود
        if hasattr(self, 'paid_amount_input') and hasattr(self, 'remaining_label'):
            self.calculate_remaining()

    def save_invoice(self):
        """حفظ الفاتورة"""
        try:
            # التحقق من البيانات
            if self.supplier_combo.currentIndex() == 0:
                QMessageBox.warning(self, "خطأ", "يرجى اختيار مورد")
                return
                
            if not self.invoice_items:
                QMessageBox.warning(self, "خطأ", "يرجى إضافة منتجات على الأقل")
                return

            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # بيانات الفاتورة
            invoice_number = self.invoice_number_label.text()
            invoice_date = self.invoice_date.date().toString('yyyy-MM-dd')
            supplier_id = self.supplier_combo.currentData()
            discount = self.discount_input.value()
            subtotal = sum(item['total'] for item in self.invoice_items)
            total = subtotal - discount

            user_id = self.user_data.get('id', 1)

            # بدء المعاملة
            try:
                # إدخال الفاتورة
                cursor.execute('''
                    INSERT INTO invoices (invoice_number, invoice_type, invoice_date, supplier_id,
                                         discount_amount, total_amount, final_amount, user_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (invoice_number, 'purchase', invoice_date, supplier_id,
                      discount, subtotal, total, user_id))
                
                # الحصول على معرف الفاتورة
                invoice_id = cursor.lastrowid
                
                # إدخال الأصناف
                for item in self.invoice_items:
                    cursor.execute('''
                        INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, total_price)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (invoice_id, item['product_id'], item['quantity'], item['price'], item['total']))
                    
                    # تحديث المخزون (الشراء يزيد المخزون)
                    cursor.execute('''
                        UPDATE products
                        SET stock = stock + ?
                        WHERE id = ?
                    ''', (item['quantity'], item['product_id']))
                
                # تأكيد المعاملة
                conn.commit()
                QMessageBox.information(self, "تم الحفظ", "تم حفظ الفاتورة بنجاح")
                self.close()
                
            except Exception as e:
                conn.rollback()
                raise e
                
            finally:
                conn.close()
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الفاتورة:\n{str(e)}")

    def validate_invoice(self):
        """التحقق من صحة بيانات الفاتورة"""
        if not self.supplier_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار المورد")
            return False

        if self.items_table.rowCount() == 0:
            QMessageBox.warning(self, "تحذير", "يرجى إضافة أصناف للفاتورة")
            return False

        return True

    def preview_invoice(self):
        """معاينة الفاتورة"""
        if not self.validate_invoice():
            return

        try:
            # جمع بيانات الفاتورة الحالية
            invoice_data = self.get_invoice_data()
            if not invoice_data:
                return
                
            # فتح نافذة المعاينة
            from ui.invoice_preview import InvoicePreview
            preview_window = InvoicePreview(invoice_data, "purchase", self)
            preview_window.exec_()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في معاينة الفاتورة:\n{str(e)}")

    def print_invoice(self):
        """طباعة الفاتورة"""
        if not self.invoice_id:
            QMessageBox.warning(self, "تحذير", "يرجى حفظ الفاتورة أولاً")
            return

        try:
            # محاولة استخدام أداة الطباعة المتقدمة
            from ui.professional_printer import ProfessionalPrinter
            printer_dialog = ProfessionalPrinter(self.db_manager, self.invoice_id, 'purchase', self)
            printer_dialog.exec_()
            
        except Exception as e:
            # في حالة عدم وجود أداة الطباعة، استخدم طريقة بديلة
            try:
                invoice_data = self.get_invoice_data()
                if invoice_data:
                    self.simple_print_invoice(invoice_data)
                else:
                    QMessageBox.warning(self, "خطأ", "لم يتم العثور على بيانات الفاتورة")
            except Exception as e2:
                QMessageBox.critical(self, "خطأ", f"فشل في طباعة الفاتورة:\n{str(e2)}")
                
    def simple_print_invoice(self, invoice_data):
        """طباعة بسيطة للفاتورة"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont
            
            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)
            
            if print_dialog.exec_() == QPrintDialog.Accepted:
                painter = QPainter(printer)
                
                # إعداد الخط
                font = QFont("Arial", 12)
                painter.setFont(font)
                
                # طباعة عنوان الفاتورة
                y = 100
                painter.drawText(100, y, f"فاتورة شراء - {invoice_data.get('invoice_number', 'غير محدد')}")
                y += 40
                painter.drawText(100, y, f"المورد: {invoice_data.get('supplier_name', 'غير محدد')}")
                y += 30
                painter.drawText(100, y, f"التاريخ: {invoice_data.get('invoice_date', 'غير محدد')}")
                y += 50
                
                # طباعة الملاحظات
                if invoice_data.get('notes'):
                    painter.drawText(100, y, f"الملاحظات: {invoice_data['notes']}")
                    y += 30
                
                # طباعة الإجماليات
                painter.drawText(100, y, f"المجموع الفرعي: {invoice_data.get('subtotal', 0)}")
                y += 25
                painter.drawText(100, y, f"الضريبة: {invoice_data.get('tax', 0)}")
                y += 25
                painter.drawText(100, y, f"الخصم: {invoice_data.get('discount', 0)}")
                y += 25
                painter.drawText(100, y, f"الإجمالي: {invoice_data.get('total', 0)}")
                
                painter.end()
                QMessageBox.information(self, "نجح", "تم إرسال الفاتورة للطباعة")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة الفاتورة:\n{str(e)}")

    def get_invoice_data(self):
        """الحصول على بيانات الفاتورة"""
        return {
            'invoice_number': self.invoice_number_label.text(),
            'invoice_date': self.invoice_date.date().toString('yyyy-MM-dd'),
            'supplier_name': self.supplier_combo.currentText(),
            'subtotal': float(self.subtotal_label.text().replace(",", "")),
            'discount': self.discount_input.value(),
            'total_amount': float(self.total_label.text().replace(",", "")),
            'paid_amount': self.paid_amount_input.value() if hasattr(self, 'paid_amount_input') else 0,
            'payment_method': self.payment_method_combo.currentText() if hasattr(self, 'payment_method_combo') else 'نقدي',
            'items': self.invoice_items
        }



    def load_invoice_data(self):
        """تحميل بيانات فاتورة موجودة"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # تحميل بيانات الفاتورة
            cursor.execute("""
                SELECT i.*, s.name as supplier_name, s.phone, s.address
                FROM invoices i
                LEFT JOIN suppliers s ON i.supplier_id = s.id
                WHERE i.id = ? AND i.invoice_type = 'purchase'
            """, (self.invoice_id,))

            invoice = cursor.fetchone()
            if not invoice:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة")
                return

            # تعبئة البيانات
            self.invoice_number_label.setText(invoice[1])
            self.invoice_date.setDate(QDate.fromString(invoice[3], 'yyyy-MM-dd'))

            # تعيين المورد
            for i in range(self.supplier_combo.count()):
                if self.supplier_combo.itemData(i) == invoice[4]:
                    self.supplier_combo.setCurrentIndex(i)
                    break

            # تعيين الخصم والمبالغ
            if hasattr(self, 'discount_input'):
                self.discount_input.setValue(invoice[5] or 0)
            # تحميل أصناف الفاتورة
            cursor.execute("""
                SELECT ii.*, p.name, p.barcode
                FROM invoice_items ii
                LEFT JOIN products p ON ii.product_id = p.id
                WHERE ii.invoice_id = ?
            """, (self.invoice_id,))

            items = cursor.fetchall()

            # مسح الأصناف الحالية
            self.invoice_items = []

            for item in items:
                # إضافة الصنف لقائمة الفاتورة
                invoice_item = {
                    'product_id': item[2],
                    'product_name': item[6] or 'منتج غير معروف',
                    'quantity': item[3],
                    'price': item[4],
                    'total': item[5]
                }
                self.invoice_items.append(invoice_item)

            # تحديث الجدول
            self.update_items_table()
            self.calculate_totals()

            conn.close()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات الفاتورة:\n{str(e)}")
