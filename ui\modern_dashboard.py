"""
الشاشة الرئيسية الحديثة - مشابهة للصورة المرجعية
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QFrame, QGridLayout, QScrollArea,
                            QMessageBox, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPalette, QColor, QIcon

# استيراد جميع النوافذ الوظيفية
from ui.products_window import ProductsWindow
from ui.customers_window import CustomersWindow
from ui.suppliers_window import SuppliersWindow
from ui.sales_window import SalesWindow
from ui.purchases_main import PurchasesMainWindow
from ui.basic_returns import BasicReturnsWindow
from ui.banking_window import BankingWindow
from ui.reports_window import ReportsWindow
from ui.users_window import UsersWindow
from ui.expenses_window import ExpensesWindow
from ui.financial_management import FinancialManagementWindow
from ui.multi_sales_window import MultiSalesWindow
from ui.backup_window import BackupWindow
from ui.settings_window import SettingsWindow

class ModernDashboard(QWidget):
    logout_requested = pyqtSignal()
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة المستخدم الحديثة"""
        self.setWindowTitle("نظام الطيب للتجارة والتوزيع - الشاشة الرئيسية")
        self.setGeometry(100, 100, 1400, 800)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # الشريط الجانبي الأيمن
        sidebar = self.create_sidebar()
        main_layout.addWidget(sidebar)
        
        # المنطقة الرئيسية
        main_area = self.create_main_area()
        main_layout.addWidget(main_area)
        
        self.setLayout(main_layout)
        
        # تطبيق الستايل العام - ألوان مريحة للعين
        self.setStyleSheet("""
            QWidget {
                font-family: 'Segoe UI', 'Tahoma', Arial;
                background-color: #f8f9fa;
                color: #2c3e50;
            }
        """)
        
    def create_sidebar(self):
        """إنشاء الشريط الجانبي"""
        sidebar = QFrame()
        sidebar.setFixedWidth(250)
        sidebar.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-right: 1px solid #dee2e6;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 20, 10, 20)
        layout.setSpacing(5)
        
        # عنوان الشريط الجانبي
        title_label = QLabel("الطيب")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-size: 24px;
                font-weight: bold;
                padding: 20px;
                background-color: #ffffff;
                border-radius: 10px;
                margin-bottom: 10px;
                border: 1px solid #e9ecef;
            }
        """)
        layout.addWidget(title_label)
        
        # معلومات المستخدم
        user_info = QLabel(f"مرحباً، {self.user_data.get('username', 'المستخدم')}")
        user_info.setAlignment(Qt.AlignCenter)
        user_info.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 12px;
                padding: 10px;
            }
        """)
        layout.addWidget(user_info)
        
        # الأزرار الجانبية - ألوان هادئة ومريحة للعين
        buttons = [
            ("المبيعات", "#28a745", "💰", self.open_sales),
            ("فواتير متعددة", "#198754", "🔥", self.open_multi_sales),
            ("المشتريات", "#6f42c1", "🛒", self.open_purchases),
            ("المخزون", "#007bff", "📦", self.open_inventory),
            ("المرتجعات", "#fd7e14", "🔄", self.open_services),
            ("العملاء", "#20c997", "👥", self.open_customers),
            ("الموردين", "#17a2b8", "🏭", self.open_suppliers),
            ("المصاريف", "#dc3545", "💸", self.open_expenses),
            ("الإدارة المالية", "#6610f2", "💼", self.open_financial_management),
            ("البنك والتحويلات", "#0dcaf0", "🏦", self.open_banking),
            ("التقارير", "#17a2b8", "📊", self.open_reports),
            ("الإعدادات", "#6c757d", "⚙️", self.open_settings)
        ]
        
        for text, color, icon, action in buttons:
            btn = self.create_sidebar_button(text, color, icon, action)
            layout.addWidget(btn)
        
        layout.addStretch()
        
        # زر تسجيل الخروج
        logout_btn = self.create_sidebar_button("تسجيل الخروج", "#dc3545", "🚪", self.logout)
        layout.addWidget(logout_btn)
        
        sidebar.setLayout(layout)
        return sidebar
        
    def create_sidebar_button(self, text, color, icon, action):
        """إنشاء زر في الشريط الجانبي"""
        btn = QPushButton(f"{icon}  {text}")
        btn.setMinimumHeight(50)
        btn.clicked.connect(action)
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px;
                font-size: 14px;
                font-weight: bold;
                text-align: left;
                margin: 2px;
            }}
            QPushButton:hover {{
                background-color: #495057;
                color: white;
            }}
            QPushButton:pressed {{
                background-color: #343a40;
            }}
        """)
        return btn
        
    def create_main_area(self):
        """إنشاء المنطقة الرئيسية"""
        main_area = QFrame()
        main_area.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: none;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # العنوان الرئيسي
        header = self.create_header()
        layout.addWidget(header)
        
        # البطاقات الإحصائية
        stats_area = self.create_stats_area()
        layout.addWidget(stats_area)
        
        # منطقة المحتوى الرئيسي
        content_area = self.create_content_area()
        layout.addWidget(content_area)
        
        main_area.setLayout(layout)
        return main_area
        
    def create_header(self):
        """إنشاء رأس الصفحة"""
        header = QFrame()
        header.setMaximumHeight(80)
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e3f2fd, stop:1 #bbdefb);
                border-radius: 15px;
                border: 1px solid #e1f5fe;
            }
        """)
        
        layout = QHBoxLayout()
        layout.setContentsMargins(30, 20, 30, 20)
        
        # عنوان إدارة المخزون
        title = QLabel("📊 إدارة المخزون")
        title.setStyleSheet("""
            QLabel {
                color: #1976d2;
                font-size: 24px;
                font-weight: bold;
            }
        """)
        layout.addWidget(title)
        
        layout.addStretch()
        
        # معلومات إضافية
        info = QLabel("نظام شامل لإدارة المبيعات والمشتريات")
        info.setStyleSheet("""
            QLabel {
                color: #424242;
                font-size: 14px;
            }
        """)
        layout.addWidget(info)
        
        header.setLayout(layout)
        return header
        
    def create_stats_area(self):
        """إنشاء منطقة الإحصائيات"""
        stats_frame = QFrame()
        stats_frame.setMaximumHeight(200)

        layout = QGridLayout()
        layout.setSpacing(15)

        # الحصول على البيانات الحقيقية من قاعدة البيانات
        stats_data = self.get_real_stats()

        # البطاقات الإحصائية - ألوان هادئة
        stats = [
            ("إجمالي المبيعات", f"{stats_data['total_sales']:,.0f} ج.م", "#28a745", "📈"),
            ("عدد المنتجات", f"{stats_data['total_products']} منتج", "#007bff", "📦"),
            ("تنبيهات المخزون", f"{stats_data['low_stock_alerts']} تنبيه", "#ffc107", "⚠️"),
            ("إجمالي العملاء", f"{stats_data['total_customers']} عميل", "#20c997", "👥")
        ]

        for i, (title, value, color, icon) in enumerate(stats):
            card = self.create_stat_card(title, value, color, icon)
            layout.addWidget(card, 0, i)

        stats_frame.setLayout(layout)
        return stats_frame

    def get_real_stats(self):
        """الحصول على الإحصائيات الحقيقية من قاعدة البيانات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # إجمالي المبيعات
            try:
                cursor.execute("SELECT COALESCE(SUM(total_amount), 0) FROM sales_invoices")
                total_sales = cursor.fetchone()[0]
            except Exception as e:
                print(f"خطأ في استعلام المبيعات: {e}")
                total_sales = 0
            
            # عدد المنتجات
            try:
                cursor.execute("SELECT COUNT(*) FROM products WHERE is_active = 1")
                total_products = cursor.fetchone()[0]
            except Exception as e:
                print(f"خطأ في استعلام المنتجات: {e}")
                total_products = 0
            
            # تنبيهات المخزون (المنتجات التي كميتها أقل من الحد الأدنى للتنبيه)
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM products 
                    WHERE current_stock < min_stock_alert 
                    AND is_active = 1
                """)
                low_stock_alerts = cursor.fetchone()[0]
            except Exception as e:
                print(f"خطأ في استعلام تنبيهات المخزون: {e}")
                # محاولة بديلة باستخدام اسم عمود آخر
                try:
                    cursor.execute("""
                        SELECT COUNT(*) FROM products 
                        WHERE stock < 10 
                        AND is_active = 1
                    """)
                    low_stock_alerts = cursor.fetchone()[0]
                except:
                    low_stock_alerts = 0
            
            # إجمالي العملاء
            try:
                cursor.execute("SELECT COUNT(*) FROM customers WHERE is_active = 1")
                total_customers = cursor.fetchone()[0]
            except Exception as e:
                print(f"خطأ في استعلام العملاء: {e}")
                total_customers = 0
            
            conn.close()
            
            return {
                'total_sales': total_sales,
                'total_products': total_products,
                'low_stock_alerts': low_stock_alerts,
                'total_customers': total_customers
            }
        except Exception as e:
            print(f"خطأ في الحصول على الإحصائيات: {e}")
            import traceback
            traceback.print_exc()
            # إرجاع قيم افتراضية في حالة الخطأ
            return {
                'total_sales': 0,
                'total_products': 0,
                'low_stock_alerts': 0,
                'total_customers': 0
            }
        
    def create_stat_card(self, title, value, color, icon):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setMinimumHeight(120)
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95), stop:1 rgba(248, 249, 250, 0.9));
                border-radius: 15px;
                border: 2px solid {color};
            }}
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 15, 20, 15)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 32px;
                color: {color};
            }}
        """)
        layout.addWidget(icon_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet(f"""
            QLabel {{
                color: {color};
                font-size: 18px;
                font-weight: bold;
            }}
        """)
        layout.addWidget(value_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 12px;
            }
        """)
        layout.addWidget(title_label)
        
        card.setLayout(layout)
        return card
        
    def create_content_area(self):
        """إنشاء منطقة المحتوى الرئيسي"""
        content = QFrame()
        content.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 15px;
                border: 1px solid #ddd;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان المحتوى
        content_title = QLabel("🎯 الوصول السريع")
        content_title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-size: 20px;
                font-weight: bold;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(content_title)
        
        # أزرار الوصول السريع
        quick_access = QGridLayout()
        quick_access.setSpacing(15)
        
        quick_buttons = [
            ("فاتورة بيع جديدة", "#28a745", "💰", self.quick_new_sale),
            ("فاتورة شراء جديدة", "#6f42c1", "🛒", self.quick_new_purchase),
            ("إضافة منتج جديد", "#007bff", "📦", self.quick_new_product),
            ("إضافة عميل جديد", "#20c997", "👤", self.quick_new_customer)
        ]

        for i, (text, color, icon, action) in enumerate(quick_buttons):
            btn = self.create_quick_button(text, color, icon, action)
            quick_access.addWidget(btn, i // 2, i % 2)
        
        layout.addLayout(quick_access)
        layout.addStretch()
        
        content.setLayout(layout)
        return content
        
    def create_quick_button(self, text, color, icon, action):
        """إنشاء زر وصول سريع"""
        btn = QPushButton(f"{icon}\n{text}")
        btn.setMinimumHeight(100)
        btn.clicked.connect(action)
        btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9), stop:1 rgba(248, 249, 250, 0.8));
                color: {color};
                border: 2px solid {color};
                border-radius: 12px;
                font-size: 14px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: {color};
                color: white;
            }}
        """)
        return btn
        
    # دوال الأزرار - ربط الوظائف الحقيقية
    def open_sales(self):
        """فتح نافذة المبيعات"""
        try:
            self.sales_window = SalesWindow(self.db_manager, self.user_data)
            self.sales_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة المبيعات:\n{str(e)}")

    def open_purchases(self):
        """فتح نافذة المشتريات"""
        try:
            self.purchases_window = PurchasesMainWindow(self.db_manager, self.user_data)
            self.purchases_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة المشتريات:\n{str(e)}")

    def open_inventory(self):
        """فتح نافذة المخزون"""
        try:
            self.products_window = ProductsWindow(self.db_manager, self.user_data)
            self.products_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة المخزون:\n{str(e)}")

    def open_services(self):
        """فتح نافذة الخدمات (المرتجعات)"""
        try:
            self.returns_window = BasicReturnsWindow(self.db_manager, self.user_data)
            self.returns_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة المرتجعات:\n{str(e)}")

    def open_customers(self):
        """فتح نافذة العملاء فقط"""
        try:
            # فتح نافذة العملاء فقط
            self.customers_window = CustomersWindow(self.db_manager, self.user_data)
            self.customers_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة العملاء:\n{str(e)}")

    def open_suppliers(self):
        """فتح نافذة الموردين"""
        try:
            self.suppliers_window = SuppliersWindow(self.db_manager, self.user_data)
            self.suppliers_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الموردين:\n{str(e)}")

    def open_reports(self):
        """فتح نافذة التقارير"""
        try:
            self.reports_window = ReportsWindow(self.db_manager, self.user_data)
            self.reports_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة التقارير:\n{str(e)}")

    def open_settings(self):
        """فتح نافذة الإعدادات"""
        try:
            self.settings_window = SettingsWindow(self.db_manager, self.user_data)
            self.settings_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الإعدادات:\n{str(e)}")

    # وظائف الوصول السريع
    def quick_new_sale(self):
        """فتح فاتورة بيع جديدة"""
        try:
            self.sales_window = SalesWindow(self.db_manager, self.user_data)
            self.sales_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح فاتورة البيع:\n{str(e)}")

    def quick_new_purchase(self):
        """فتح فاتورة شراء جديدة"""
        try:
            self.purchases_window = PurchasesMainWindow(self.db_manager, self.user_data)
            self.purchases_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح فاتورة الشراء:\n{str(e)}")

    def quick_new_product(self):
        """فتح نافذة إضافة منتج جديد"""
        try:
            self.products_window = ProductsWindow(self.db_manager, self.user_data)
            self.products_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة المنتجات:\n{str(e)}")

    def quick_new_customer(self):
        """فتح نافذة إضافة عميل جديد"""
        try:
            self.customers_window = CustomersWindow(self.db_manager, self.user_data)
            self.customers_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة العملاء:\n{str(e)}")

    # وظائف إضافية
    def open_multi_sales(self):
        """فتح نافذة الفواتير المتعددة"""
        try:
            self.multi_sales_window = MultiSalesWindow(self.db_manager, self.user_data)
            self.multi_sales_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح الفواتير المتعددة:\n{str(e)}")

    def open_expenses(self):
        """فتح نافذة المصاريف"""
        try:
            self.expenses_window = ExpensesWindow(self.db_manager, self.user_data)
            self.expenses_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة المصاريف:\n{str(e)}")

    def open_financial_management(self):
        """فتح نافذة الإدارة المالية"""
        try:
            self.financial_window = FinancialManagementWindow(self.db_manager, self.user_data)
            self.financial_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح الإدارة المالية:\n{str(e)}")

    def open_banking(self):
        """فتح نافذة البنك والتحويلات"""
        try:
            self.banking_window = BankingWindow(self.db_manager, self.user_data)
            self.banking_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة البنك:\n{str(e)}")

    def logout(self):
        reply = QMessageBox.question(self, "تسجيل الخروج",
                                   "هل أنت متأكد من تسجيل الخروج؟",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.logout_requested.emit()
            self.close()
