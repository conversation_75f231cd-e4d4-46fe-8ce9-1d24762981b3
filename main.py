#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام نقاط البيع والمحاسبة - الطيب للتجارة والتوزيع
Point of Sale and Accounting System - Al-Tayeb Trading & Distribution

المطور: شادي الطيب
العنوان: القاهرة
الهاتف: *********** / ***********
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout
from PyQt5.QtWidgets import QPushButton, QLabel, QStackedWidget, QMessageBox
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QIcon, QPixmap

# استيراد الوحدات المخصصة
from database.db_manager import DatabaseManager
from ui.login_window import LoginWindow
from ui.main_dashboard import MainDashboard
from utils.config import Config

class POSApplication(QMainWindow):
    """التطبيق الرئيسي لنظام نقاط البيع"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_user = None
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم الرئيسية"""
        self.setWindowTitle("نظام نقاط البيع والمحاسبة - الطيب للتجارة والتوزيع")
        self.setGeometry(100, 100, 1200, 800)
        
        # إعداد الخط العربي
        font = QFont("Arial", 12)
        self.setFont(font)
        
        # إنشاء الواجهة المكدسة
        self.stacked_widget = QStackedWidget()
        self.setCentralWidget(self.stacked_widget)
        
        # إنشاء نافذة تسجيل الدخول
        self.login_window = LoginWindow(self.db_manager)
        self.login_window.login_successful.connect(self.on_login_success)
        self.stacked_widget.addWidget(self.login_window)
        
        # عرض نافذة تسجيل الدخول
        self.stacked_widget.setCurrentWidget(self.login_window)
        
    def on_login_success(self, user_data):
        """معالج نجاح تسجيل الدخول"""
        self.current_user = user_data
        
        # إنشاء لوحة التحكم الرئيسية
        self.main_dashboard = MainDashboard(self.db_manager, self.current_user)
        self.main_dashboard.logout_requested.connect(self.on_logout)
        self.stacked_widget.addWidget(self.main_dashboard)
        
        # الانتقال للوحة التحكم
        self.stacked_widget.setCurrentWidget(self.main_dashboard)
        
    def on_logout(self):
        """معالج تسجيل الخروج"""
        self.current_user = None
        self.stacked_widget.removeWidget(self.main_dashboard)
        self.stacked_widget.setCurrentWidget(self.login_window)
        self.login_window.clear_fields()

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)
    
    # إعداد الترميز للعربية
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء التطبيق الرئيسي
    pos_app = POSApplication()
    pos_app.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
