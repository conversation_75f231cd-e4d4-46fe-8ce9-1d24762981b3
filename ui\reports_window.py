#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة التقارير المالية - نظام نقاط البيع والمحاسبة
Financial Reports Window - POS and Accounting System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QComboBox, QDialog, QFormLayout, 
                            QMessageBox, QHeaderView, QAbstractItemView,
                            QFrame, QGroupBox, QDateEdit, QTabWidget,
                            QTextEdit, QGridLayout, QSplitter, QCheckBox,
                            QRadioButton, QButtonGroup, QScrollArea)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QColor, <PERSON><PERSON><PERSON><PERSON>, QBrush, QLinearGradient
from utils.config import Config
from datetime import datetime, timedelta
import random  # للبيانات التجريبية

class ReportsWindow(QWidget):
    """نافذة التقارير المالية"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("التقارير المالية")
        self.setGeometry(100, 100, 1200, 700)

        # تطبيق التصميم العصري
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                font-family: 'Segoe UI', Arial, sans-serif;
                color: #2c3e50;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f1f3f4);
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px 15px;
                font-size: 13px;
                font-weight: 500;
                color: #2c3e50;
                min-height: 25px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-color: #667eea;
                color: white;
            }
            QTabWidget::pane {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f1f3f4);
                border: 2px solid #e0e0e0;
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 10px 20px;
                margin-right: 2px;
                font-weight: 500;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border-color: #667eea;
            }
            QTableWidget {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f0f0f0;
                selection-background-color: #667eea;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: black;
                padding: 10px;
                border: none;
                font-weight: 600;
            }
            QDateEdit {
                background-color: white;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
            }
            QDateEdit:focus {
                border-color: #667eea;
                outline: none;
            }
        """)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # شريط الأدوات العلوي (تم حذف التحليلات الذكية)
        toolbar_layout = QHBoxLayout()
        toolbar_layout.addStretch()
        main_layout.addLayout(toolbar_layout)

        # التبويبات
        tabs = QTabWidget()
        
        # تبويب تقرير المبيعات
        sales_tab = QWidget()
        self.setup_sales_report_tab(sales_tab)
        tabs.addTab(sales_tab, "تقرير المبيعات")
        
        # تبويب تقرير المشتريات
        purchases_tab = QWidget()
        self.setup_purchases_report_tab(purchases_tab)
        tabs.addTab(purchases_tab, "تقرير المشتريات")
        
        # تبويب الأرباح والخسائر
        profit_loss_tab = QWidget()
        self.setup_profit_loss_tab(profit_loss_tab)
        tabs.addTab(profit_loss_tab, "الأرباح والخسائر")
        
        # تبويب تقرير المخزون
        inventory_tab = QWidget()
        self.setup_inventory_tab(inventory_tab)
        tabs.addTab(inventory_tab, "تقرير المخزون")
        
        # تبويب التحاليل الذكية (الجديد)
        self.tabs = tabs  # حفظ مرجع للتبويبات
        main_layout.addWidget(tabs)
        self.setLayout(main_layout)


        
    def setup_sales_report_tab(self, tab):
        """إعداد تبويب تقرير المبيعات"""
        layout = QVBoxLayout()
        
        # فلاتر التاريخ
        filters_group = QGroupBox("فلاتر التقرير")
        filters_layout = QGridLayout()
        
        filters_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        self.sales_from_date = QDateEdit()
        self.sales_from_date.setDate(QDate.currentDate().addDays(-30))
        filters_layout.addWidget(self.sales_from_date, 0, 1)
        
        filters_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        self.sales_to_date = QDateEdit()
        self.sales_to_date.setDate(QDate.currentDate())
        filters_layout.addWidget(self.sales_to_date, 0, 3)
        
        generate_sales_btn = QPushButton("إنشاء التقرير")
        generate_sales_btn.clicked.connect(self.generate_sales_report)
        filters_layout.addWidget(generate_sales_btn, 0, 4)
        
        print_sales_btn = QPushButton("🖨️ طباعة الفاتورة")
        print_sales_btn.clicked.connect(self.print_selected_invoice)
        filters_layout.addWidget(print_sales_btn, 0, 5)

        export_sales_btn = QPushButton("تصدير Excel")
        export_sales_btn.clicked.connect(self.export_sales_report)
        filters_layout.addWidget(export_sales_btn, 0, 6)
        
        filters_group.setLayout(filters_layout)
        layout.addWidget(filters_group)
        
        # جدول تقرير المبيعات
        self.sales_report_table = QTableWidget()
        self.sales_report_table.setColumnCount(6)
        self.sales_report_table.setHorizontalHeaderLabels([
            "التاريخ", "رقم الفاتورة", "العميل", "المبلغ الإجمالي", "المدفوع", "المتبقي"
        ])
        
        header = self.sales_report_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.sales_report_table.setAlternatingRowColors(True)
        layout.addWidget(self.sales_report_table)
        
        # إحصائيات المبيعات
        self.sales_stats_label = QLabel()
        self.sales_stats_label.setStyleSheet("font-weight: bold; color: blue; padding: 10px;")
        layout.addWidget(self.sales_stats_label)
        
        tab.setLayout(layout)
        
    def setup_purchases_report_tab(self, tab):
        """إعداد تبويب تقرير المشتريات"""
        layout = QVBoxLayout()
        
        # فلاتر التاريخ
        filters_group = QGroupBox("فلاتر التقرير")
        filters_layout = QGridLayout()
        
        filters_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        self.purchases_from_date = QDateEdit()
        self.purchases_from_date.setDate(QDate.currentDate().addDays(-30))
        filters_layout.addWidget(self.purchases_from_date, 0, 1)
        
        filters_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        self.purchases_to_date = QDateEdit()
        self.purchases_to_date.setDate(QDate.currentDate())
        filters_layout.addWidget(self.purchases_to_date, 0, 3)
        
        generate_purchases_btn = QPushButton("إنشاء التقرير")
        generate_purchases_btn.clicked.connect(self.generate_purchases_report)
        filters_layout.addWidget(generate_purchases_btn, 0, 4)
        
        print_purchases_btn = QPushButton("🖨️ طباعة الفاتورة")
        print_purchases_btn.clicked.connect(self.print_selected_invoice)
        filters_layout.addWidget(print_purchases_btn, 0, 5)

        export_purchases_btn = QPushButton("تصدير Excel")
        export_purchases_btn.clicked.connect(self.export_purchases_report)
        filters_layout.addWidget(export_purchases_btn, 0, 6)
        
        filters_group.setLayout(filters_layout)
        layout.addWidget(filters_group)
        
        # جدول تقرير المشتريات
        self.purchases_report_table = QTableWidget()
        self.purchases_report_table.setColumnCount(6)
        self.purchases_report_table.setHorizontalHeaderLabels([
            "التاريخ", "رقم الفاتورة", "المورد", "المبلغ الإجمالي", "المدفوع", "المتبقي"
        ])
        
        header = self.purchases_report_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.purchases_report_table.setAlternatingRowColors(True)
        layout.addWidget(self.purchases_report_table)
        
        # إحصائيات المشتريات
        self.purchases_stats_label = QLabel()
        self.purchases_stats_label.setStyleSheet("font-weight: bold; color: blue; padding: 10px;")
        layout.addWidget(self.purchases_stats_label)
        
        tab.setLayout(layout)
        
    def setup_profit_loss_tab(self, tab):
        """إعداد تبويب الأرباح والخسائر"""
        layout = QVBoxLayout()
        
        # فلاتر التاريخ
        filters_group = QGroupBox("فترة التقرير")
        filters_layout = QGridLayout()
        
        filters_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        self.profit_from_date = QDateEdit()
        self.profit_from_date.setDate(QDate.currentDate().addDays(-30))
        filters_layout.addWidget(self.profit_from_date, 0, 1)
        
        filters_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        self.profit_to_date = QDateEdit()
        self.profit_to_date.setDate(QDate.currentDate())
        filters_layout.addWidget(self.profit_to_date, 0, 3)
        
        generate_profit_btn = QPushButton("حساب الأرباح والخسائر")
        generate_profit_btn.clicked.connect(self.calculate_profit_loss)
        filters_layout.addWidget(generate_profit_btn, 0, 4)
        
        filters_group.setLayout(filters_layout)
        layout.addWidget(filters_group)
        
        # عرض الأرباح والخسائر
        self.profit_loss_display = QTextEdit()
        self.profit_loss_display.setReadOnly(True)
        self.profit_loss_display.setStyleSheet("""
            QTextEdit {
                font-family: 'Courier New';
                font-size: 12px;
                background-color: #f8f9fa;
                border: 1px solid #ddd;
            }
        """)
        layout.addWidget(self.profit_loss_display)
        
        tab.setLayout(layout)
        
    def setup_inventory_tab(self, tab):
        """إعداد تبويب تقرير المخزون"""
        layout = QVBoxLayout()
        
        # أدوات التحكم
        controls_layout = QHBoxLayout()
        
        refresh_btn = QPushButton("تحديث التقرير")
        refresh_btn.clicked.connect(self.generate_inventory_report)
        controls_layout.addWidget(refresh_btn)
        
        export_inventory_btn = QPushButton("تصدير Excel")
        export_inventory_btn.clicked.connect(self.export_inventory_report)
        controls_layout.addWidget(export_inventory_btn)
        
        controls_layout.addStretch()
        
        # فلتر المنتجات منخفضة المخزون
        low_stock_check = QPushButton("المنتجات منخفضة المخزون فقط")
        low_stock_check.clicked.connect(self.show_low_stock_only)
        controls_layout.addWidget(low_stock_check)
        
        layout.addLayout(controls_layout)
        
        # جدول تقرير المخزون
        self.inventory_report_table = QTableWidget()
        self.inventory_report_table.setColumnCount(7)
        self.inventory_report_table.setHorizontalHeaderLabels([
            "اسم المنتج", "الكمية الحالية", "الحد الأدنى", "سعر الشراء", 
            "سعر البيع", "قيمة المخزون", "الحالة"
        ])
        
        header = self.inventory_report_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.inventory_report_table.setAlternatingRowColors(True)
        layout.addWidget(self.inventory_report_table)
        
        # إحصائيات المخزون
        self.inventory_stats_label = QLabel()
        self.inventory_stats_label.setStyleSheet("font-weight: bold; color: blue; padding: 10px;")
        layout.addWidget(self.inventory_stats_label)
        
        tab.setLayout(layout)
        
        # تحميل تقرير المخزون عند البداية
        self.generate_inventory_report()
        
    def generate_sales_report(self):
        """إنشاء تقرير المبيعات"""
        from_date = self.sales_from_date.date().toString('yyyy-MM-dd')
        to_date = self.sales_to_date.date().toString('yyyy-MM-dd')
        
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT i.invoice_date, i.invoice_number, c.name as customer_name,
                   i.final_amount, i.paid_amount, i.remaining_amount
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE i.invoice_type = 'sale' AND i.is_active = 1
            AND i.invoice_date BETWEEN ? AND ?
            ORDER BY i.invoice_date DESC
        ''', (from_date, to_date))
        
        sales = cursor.fetchall()
        conn.close()
        
        # تحديث الجدول
        self.sales_report_table.setRowCount(len(sales))
        
        total_amount = 0
        total_paid = 0
        total_remaining = 0
        
        for row, sale in enumerate(sales):
            self.sales_report_table.setItem(row, 0, QTableWidgetItem(sale['invoice_date']))
            self.sales_report_table.setItem(row, 1, QTableWidgetItem(sale['invoice_number']))
            self.sales_report_table.setItem(row, 2, QTableWidgetItem(sale['customer_name'] or 'عميل نقدي'))
            self.sales_report_table.setItem(row, 3, QTableWidgetItem(f"{sale['final_amount']:.2f}"))
            self.sales_report_table.setItem(row, 4, QTableWidgetItem(f"{sale['paid_amount']:.2f}"))
            self.sales_report_table.setItem(row, 5, QTableWidgetItem(f"{sale['remaining_amount']:.2f}"))
            
            total_amount += sale['final_amount']
            total_paid += sale['paid_amount']
            total_remaining += sale['remaining_amount']
            
        # تحديث الإحصائيات
        stats_text = f"""
        إجمالي الفواتير: {len(sales)} فاتورة
        إجمالي المبيعات: {total_amount:.2f} جنيه
        إجمالي المدفوع: {total_paid:.2f} جنيه
        إجمالي المتبقي: {total_remaining:.2f} جنيه
        """
        self.sales_stats_label.setText(stats_text)
        
    def generate_purchases_report(self):
        """إنشاء تقرير المشتريات"""
        from_date = self.purchases_from_date.date().toString('yyyy-MM-dd')
        to_date = self.purchases_to_date.date().toString('yyyy-MM-dd')
        
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT i.invoice_date, i.invoice_number, s.name as supplier_name,
                   i.final_amount, i.paid_amount, i.remaining_amount
            FROM invoices i
            LEFT JOIN suppliers s ON i.supplier_id = s.id
            WHERE i.invoice_type = 'purchase' AND i.is_active = 1
            AND i.invoice_date BETWEEN ? AND ?
            ORDER BY i.invoice_date DESC
        ''', (from_date, to_date))
        
        purchases = cursor.fetchall()
        conn.close()
        
        # تحديث الجدول
        self.purchases_report_table.setRowCount(len(purchases))
        
        total_amount = 0
        total_paid = 0
        total_remaining = 0
        
        for row, purchase in enumerate(purchases):
            self.purchases_report_table.setItem(row, 0, QTableWidgetItem(purchase['invoice_date']))
            self.purchases_report_table.setItem(row, 1, QTableWidgetItem(purchase['invoice_number']))
            self.purchases_report_table.setItem(row, 2, QTableWidgetItem(purchase['supplier_name'] or 'مورد نقدي'))
            self.purchases_report_table.setItem(row, 3, QTableWidgetItem(f"{purchase['final_amount']:.2f}"))
            self.purchases_report_table.setItem(row, 4, QTableWidgetItem(f"{purchase['paid_amount']:.2f}"))
            self.purchases_report_table.setItem(row, 5, QTableWidgetItem(f"{purchase['remaining_amount']:.2f}"))
            
            total_amount += purchase['final_amount']
            total_paid += purchase['paid_amount']
            total_remaining += purchase['remaining_amount']
            
        # تحديث الإحصائيات
        stats_text = f"""
        إجمالي الفواتير: {len(purchases)} فاتورة
        إجمالي المشتريات: {total_amount:.2f} جنيه
        إجمالي المدفوع: {total_paid:.2f} جنيه
        إجمالي المتبقي: {total_remaining:.2f} جنيه
        """
        self.purchases_stats_label.setText(stats_text)

    def calculate_profit_loss(self):
        """حساب الأرباح والخسائر"""
        from_date = self.profit_from_date.date().toString('yyyy-MM-dd')
        to_date = self.profit_to_date.date().toString('yyyy-MM-dd')

        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        # حساب إجمالي المبيعات
        cursor.execute('''
            SELECT SUM(final_amount) as total_sales
            FROM invoices
            WHERE invoice_type = 'sale' AND is_active = 1
            AND invoice_date BETWEEN ? AND ?
        ''', (from_date, to_date))

        total_sales = cursor.fetchone()['total_sales'] or 0

        # حساب تكلفة البضاعة المباعة
        cursor.execute('''
            SELECT SUM(ii.quantity * p.purchase_price) as cost_of_goods
            FROM invoice_items ii
            JOIN invoices i ON ii.invoice_id = i.id
            JOIN products p ON ii.product_id = p.id
            WHERE i.invoice_type = 'sale' AND i.is_active = 1
            AND i.invoice_date BETWEEN ? AND ?
        ''', (from_date, to_date))

        cost_of_goods = cursor.fetchone()['cost_of_goods'] or 0

        # حساب إجمالي المشتريات
        cursor.execute('''
            SELECT SUM(final_amount) as total_purchases
            FROM invoices
            WHERE invoice_type = 'purchase' AND is_active = 1
            AND invoice_date BETWEEN ? AND ?
        ''', (from_date, to_date))

        total_purchases = cursor.fetchone()['total_purchases'] or 0

        # حساب المرتجعات
        cursor.execute('''
            SELECT
                SUM(CASE WHEN return_type = 'sale_return' THEN total_amount ELSE 0 END) as sales_returns,
                SUM(CASE WHEN return_type = 'purchase_return' THEN total_amount ELSE 0 END) as purchase_returns
            FROM returns
            WHERE return_date BETWEEN ? AND ?
        ''', (from_date, to_date))

        returns_data = cursor.fetchone()
        sales_returns = returns_data['sales_returns'] or 0
        purchase_returns = returns_data['purchase_returns'] or 0

        # حساب المصاريف
        cursor.execute('''
            SELECT SUM(amount) as total_expenses
            FROM expenses
            WHERE expense_date BETWEEN ? AND ?
        ''', (from_date, to_date))

        total_expenses = cursor.fetchone()['total_expenses'] or 0

        conn.close()

        # حساب الأرباح
        net_sales = total_sales - sales_returns
        net_purchases = total_purchases - purchase_returns
        gross_profit = net_sales - cost_of_goods
        net_profit = gross_profit - total_expenses

        # عرض التقرير
        report_text = f"""
═══════════════════════════════════════════════════════════════
                    تقرير الأرباح والخسائر
                    من {from_date} إلى {to_date}
═══════════════════════════════════════════════════════════════

الإيرادات:
─────────────────────────────────────────────────────────────
إجمالي المبيعات                           {total_sales:>15,.2f} جنيه
ناقص: مرتجع المبيعات                      {sales_returns:>15,.2f} جنيه
                                          ─────────────────────
صافي المبيعات                             {net_sales:>15,.2f} جنيه

تكلفة البضاعة المباعة:
─────────────────────────────────────────────────────────────
تكلفة البضاعة المباعة                      {cost_of_goods:>15,.2f} جنيه
                                          ─────────────────────
إجمالي الربح                              {gross_profit:>15,.2f} جنيه

المشتريات في الفترة:
─────────────────────────────────────────────────────────────
إجمالي المشتريات                          {total_purchases:>15,.2f} جنيه
ناقص: مرتجع المشتريات                     {purchase_returns:>15,.2f} جنيه
                                          ─────────────────────
صافي المشتريات                            {net_purchases:>15,.2f} جنيه

المصاريف التشغيلية:
─────────────────────────────────────────────────────────────
إجمالي المصاريف                           {total_expenses:>15,.2f} جنيه
                                          ─────────────────────
صافي الربح                                {net_profit:>15,.2f} جنيه

═══════════════════════════════════════════════════════════════
النتيجة النهائية:
إجمالي الربح (قبل المصاريف): {gross_profit:,.2f} جنيه
صافي الربح (بعد المصاريف): {net_profit:,.2f} جنيه
نسبة الربح الإجمالي: {(gross_profit/net_sales*100) if net_sales > 0 else 0:.1f}%
نسبة الربح الصافي: {(net_profit/net_sales*100) if net_sales > 0 else 0:.1f}%
═══════════════════════════════════════════════════════════════
        """

        self.profit_loss_display.setText(report_text)

    def generate_inventory_report(self):
        """إنشاء تقرير المخزون"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT name, current_stock, min_stock_alert, purchase_price, retail_price
            FROM products
            WHERE is_active = 1
            ORDER BY name
        ''')

        products = cursor.fetchall()
        conn.close()

        # تحديث الجدول
        self.inventory_report_table.setRowCount(len(products))

        total_value = 0
        low_stock_count = 0

        for row, product in enumerate(products):
            self.inventory_report_table.setItem(row, 0, QTableWidgetItem(product['name']))
            self.inventory_report_table.setItem(row, 1, QTableWidgetItem(str(product['current_stock'])))
            self.inventory_report_table.setItem(row, 2, QTableWidgetItem(str(product['min_stock_alert'])))
            self.inventory_report_table.setItem(row, 3, QTableWidgetItem(f"{product['purchase_price']:.2f}"))
            self.inventory_report_table.setItem(row, 4, QTableWidgetItem(f"{product['retail_price']:.2f}"))

            # قيمة المخزون
            stock_value = product['current_stock'] * product['purchase_price']
            self.inventory_report_table.setItem(row, 5, QTableWidgetItem(f"{stock_value:.2f}"))
            total_value += stock_value

            # حالة المخزون
            if product['current_stock'] <= product['min_stock_alert']:
                status = "منخفض"
                status_item = QTableWidgetItem(status)
                status_item.setBackground(QColor(255, 200, 200))
                low_stock_count += 1
            else:
                status = "طبيعي"
                status_item = QTableWidgetItem(status)
                status_item.setBackground(QColor(200, 255, 200))

            self.inventory_report_table.setItem(row, 6, status_item)

        # تحديث الإحصائيات
        stats_text = f"""
        إجمالي المنتجات: {len(products)} منتج
        قيمة المخزون الإجمالية: {total_value:.2f} جنيه
        المنتجات منخفضة المخزون: {low_stock_count} منتج
        """
        self.inventory_stats_label.setText(stats_text)

    def show_low_stock_only(self):
        """عرض المنتجات منخفضة المخزون فقط"""
        for row in range(self.inventory_report_table.rowCount()):
            status_item = self.inventory_report_table.item(row, 6)
            if status_item and status_item.text() == "منخفض":
                self.inventory_report_table.setRowHidden(row, False)
            else:
                self.inventory_report_table.setRowHidden(row, True)

    def export_sales_report(self):
        """تصدير تقرير المبيعات إلى Excel"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import csv
            from datetime import datetime
            
            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, 
                "حفظ تقرير المبيعات", 
                f"تقرير_المبيعات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv);;All Files (*)"
            )
            
            if file_path:
                # جمع البيانات من الجدول
                table = self.sales_report_table
                data = []
                
                # إضافة العناوين
                headers = []
                for col in range(table.columnCount()):
                    headers.append(table.horizontalHeaderItem(col).text())
                data.append(headers)
                
                # إضافة البيانات
                for row in range(table.rowCount()):
                    row_data = []
                    for col in range(table.columnCount()):
                        item = table.item(row, col)
                        row_data.append(item.text() if item else '')
                    data.append(row_data)
                
                # كتابة الملف
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as file:
                    writer = csv.writer(file)
                    writer.writerows(data)
                
                QMessageBox.information(self, "نجح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{str(e)}")
        
    def export_purchases_report(self):
        """تصدير تقرير المشتريات إلى Excel"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import csv
            from datetime import datetime
            
            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, 
                "حفظ تقرير المشتريات", 
                f"تقرير_المشتريات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv);;All Files (*)"
            )
            
            if file_path:
                # جمع البيانات من الجدول
                table = self.purchases_report_table
                data = []
                
                # إضافة العناوين
                headers = []
                for col in range(table.columnCount()):
                    headers.append(table.horizontalHeaderItem(col).text())
                data.append(headers)
                
                # إضافة البيانات
                for row in range(table.rowCount()):
                    row_data = []
                    for col in range(table.columnCount()):
                        item = table.item(row, col)
                        row_data.append(item.text() if item else '')
                    data.append(row_data)
                
                # كتابة الملف
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as file:
                    writer = csv.writer(file)
                    writer.writerows(data)
                
                QMessageBox.information(self, "نجح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{str(e)}")
        
    def export_inventory_report(self):
        """تصدير تقرير المخزون إلى Excel"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import csv
            from datetime import datetime
            
            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, 
                "حفظ تقرير المخزون", 
                f"تقرير_المخزون_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv);;All Files (*)"
            )
            
            if file_path:
                # جمع البيانات من الجدول
                table = self.inventory_report_table
                data = []
                
                # إضافة العناوين
                headers = []
                for col in range(table.columnCount()):
                    headers.append(table.horizontalHeaderItem(col).text())
                data.append(headers)
                
                # إضافة البيانات
                for row in range(table.rowCount()):
                    row_data = []
                    for col in range(table.columnCount()):
                        item = table.item(row, col)
                        row_data.append(item.text() if item else '')
                    data.append(row_data)
                
                # كتابة الملف
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as file:
                    writer = csv.writer(file)
                    writer.writerows(data)
                
                QMessageBox.information(self, "نجح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{str(e)}")
        
        # عنوان التبويب
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4a148c, stop:1 #7b1fa2);
                border-radius: 10px;
                padding: 10px;
                margin-bottom: 15px;
            }
        """)
        header_layout = QHBoxLayout(header)
        
        title_label = QLabel("🧠 التحاليل الذكية والتوقعات المستقبلية")
        title_label.setStyleSheet("""
            color: white;
            font-size: 18px;
            font-weight: bold;
        """)
        header_layout.addWidget(title_label)
        
        main_layout.addWidget(header)
        
        # منطقة الخيارات والتحليلات
        content_area = QSplitter(Qt.Horizontal)
        
        # منطقة الخيارات (الجانب الأيمن)
        options_frame = QFrame()
        options_frame.setFrameShape(QFrame.StyledPanel)
        options_frame.setStyleSheet("""
            QFrame {
                background-color: #f5f5f5;
                border-radius: 10px;
                border: 1px solid #e0e0e0;
            }
        """)
        options_layout = QVBoxLayout(options_frame)
        
        # عنوان الخيارات
        options_title = QLabel("خيارات التحليل")
        options_title.setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 10px;")
        options_layout.addWidget(options_title)
        
        # فترة التحليل
        period_group = QGroupBox("فترة التحليل")
        period_layout = QVBoxLayout()
        
        # خيارات الفترة
        self.period_month = QRadioButton("الشهر الحالي")
        self.period_quarter = QRadioButton("الربع الحالي")
        self.period_year = QRadioButton("السنة الحالية")
        self.period_custom = QRadioButton("فترة مخصصة")
        
        # تحديد الخيار الافتراضي
        self.period_month.setChecked(True)
        
        # إضافة الخيارات إلى المجموعة
        period_layout.addWidget(self.period_month)
        period_layout.addWidget(self.period_quarter)
        period_layout.addWidget(self.period_year)
        period_layout.addWidget(self.period_custom)
        
        # تواريخ مخصصة
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel("من:"))
        self.analytics_from_date = QDateEdit()
        self.analytics_from_date.setDate(QDate.currentDate().addDays(-30))
        date_layout.addWidget(self.analytics_from_date)
        
        date_layout.addWidget(QLabel("إلى:"))
        self.analytics_to_date = QDateEdit()
        self.analytics_to_date.setDate(QDate.currentDate())
        date_layout.addWidget(self.analytics_to_date)
        
        period_layout.addLayout(date_layout)
        period_group.setLayout(period_layout)
        options_layout.addWidget(period_group)
        
        # نوع التحليل
        analysis_group = QGroupBox("نوع التحليل")
        analysis_layout = QVBoxLayout()
        
        self.analysis_sales_trend = QCheckBox("تحليل اتجاهات المبيعات")
        self.analysis_sales_trend.setChecked(True)
        analysis_layout.addWidget(self.analysis_sales_trend)
        
        self.analysis_product_performance = QCheckBox("أداء المنتجات")
        self.analysis_product_performance.setChecked(True)
        analysis_layout.addWidget(self.analysis_product_performance)
        
        self.analysis_customer_insights = QCheckBox("تحليل العملاء")
        analysis_layout.addWidget(self.analysis_customer_insights)
        
        self.analysis_inventory_prediction = QCheckBox("توقعات المخزون")
        analysis_layout.addWidget(self.analysis_inventory_prediction)
        
        self.analysis_profit_forecast = QCheckBox("توقعات الأرباح")
        analysis_layout.addWidget(self.analysis_profit_forecast)
        
        analysis_group.setLayout(analysis_layout)
        options_layout.addWidget(analysis_group)
        
        # زر تحليل البيانات
        analyze_btn = QPushButton("تحليل البيانات")
        analyze_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4a148c, stop:1 #7b1fa2);
                color: white;
                font-weight: bold;
                padding: 10px;
                border-radius: 5px;
                margin-top: 10px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #7b1fa2, stop:1 #9c27b0);
            }
        """)
        #analyze_btn.clicked.connect(self.generate_smart_analytics)
        options_layout.addWidget(analyze_btn)
        
        # إضافة مساحة فارغة في النهاية
        options_layout.addStretch()
        
        # منطقة عرض التحليلات (الجانب الأيسر)
        results_scroll = QScrollArea()
        results_scroll.setWidgetResizable(True)
        results_scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: white;
            }
        """)
        
        self.analytics_results_widget = QWidget()
        self.analytics_results_layout = QVBoxLayout(self.analytics_results_widget)
        
        # رسالة ترحيبية
        welcome_frame = QFrame()
        welcome_frame.setStyleSheet("""
            QFrame {
                background-color: #e8f5e9;
                border-radius: 10px;
                padding: 20px;
                margin: 10px;
            }
        """)
        welcome_layout = QVBoxLayout(welcome_frame)
        
        welcome_title = QLabel("مرحباً بك في التحاليل الذكية! 🚀")
        welcome_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2e7d32;")
        welcome_layout.addWidget(welcome_title)
        
        welcome_text = QLabel(
            "هذه الميزة تساعدك على تحليل بيانات عملك بطريقة ذكية واستخراج رؤى قيمة "
            "تساعدك على اتخاذ قرارات أفضل لنمو عملك.\n\n"
            "اختر خيارات التحليل من القائمة على اليمين ثم اضغط على زر 'تحليل البيانات' "
            "للحصول على تحليلات مفصلة ورسوم بيانية توضح اتجاهات عملك."
        )
        welcome_text.setWordWrap(True)
        welcome_text.setStyleSheet("font-size: 14px; color: #37474f;")
        welcome_layout.addWidget(welcome_text)
        
        self.analytics_results_layout.addWidget(welcome_frame)
        self.analytics_results_layout.addStretch()
        
        results_scroll.setWidget(self.analytics_results_widget)
        
        # إضافة المناطق إلى السبليتر
        content_area.addWidget(options_frame)
        content_area.addWidget(results_scroll)
        
        # تعيين النسب الافتراضية للسبليتر
        content_area.setSizes([300, 700])
        
        main_layout.addWidget(content_area) # type: ignore # type: ignore
        tab.setLayout(main_layout)
        
    def print_selected_invoice(self):
        """طباعة الفاتورة المحددة"""
        try:
            # تحديد الجدول النشط
            current_tab = self.tabs.currentIndex()

            if current_tab == 0:  # تبويب المبيعات
                table = self.sales_report_table
                invoice_type = 'sale'
            elif current_tab == 1:  # تبويب المشتريات
                table = self.purchases_report_table
                invoice_type = 'purchase'
            else:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار تبويب المبيعات أو المشتريات")
                return

            # التحقق من وجود صف محدد
            current_row = table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للطباعة")
                return

            # الحصول على رقم الفاتورة
            invoice_number = table.item(current_row, 1).text()

            # البحث عن معرف الفاتورة
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id FROM invoices
                WHERE invoice_number = ? AND invoice_type = ?
            ''', (invoice_number, invoice_type))

            result = cursor.fetchone()
            conn.close()

            if not result:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة")
                return

            invoice_id = result['id']

            # فتح نافذة الطباعة
            from ui.professional_printer import ProfessionalPrinter
            printer_dialog = ProfessionalPrinter(self.db_manager, invoice_id, invoice_type, self)
            printer_dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في طباعة الفاتورة:\n{str(e)}")
            
    def get_real_sales_data(self):
        """الحصول على بيانات المبيعات الحقيقية"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # الحصول على المبيعات الشهرية للسنة الحالية
            cursor.execute('''
                SELECT 
                    strftime('%Y-%m', invoice_date) as month,
                    SUM(final_amount) as total_sales
                FROM invoices 
                WHERE invoice_type = 'sale' AND is_active = 1
                AND invoice_date >= date('now', '-12 months')
                GROUP BY strftime('%Y-%m', invoice_date)
                ORDER BY month
            ''')
            
            results = cursor.fetchall()
            conn.close()
            
            if not results:
                return None
                
            # تحويل البيانات إلى قاموس
            sales_data = {}
            month_names = {
                '01': 'يناير', '02': 'فبراير', '03': 'مارس', '04': 'أبريل',
                '05': 'مايو', '06': 'يونيو', '07': 'يوليو', '08': 'أغسطس',
                '09': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
            }
            
            for row in results:
                month_key = row['month']
                month_name = month_names.get(month_key.split('-')[1], month_key)
                sales_data[month_name] = float(row['total_sales'])
            
            return sales_data
            
        except Exception as e:
            print(f"خطأ في الحصول على بيانات المبيعات: {e}")
            return None
    
