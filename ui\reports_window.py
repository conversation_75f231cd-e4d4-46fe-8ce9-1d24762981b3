#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة التقارير المالية - نظام نقاط البيع والمحاسبة
Financial Reports Window - POS and Accounting System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QComboBox, QDialog, QFormLayout, 
                            QMessageBox, QHeaderView, QAbstractItemView,
                            QFrame, QGroupBox, QDateEdit, QTabWidget,
                            QTextEdit, QGridLayout, QSplitter, QCheckBox,
                            QRadioButton, QButtonGroup, QScrollArea)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QColor, <PERSON><PERSON><PERSON><PERSON>, QBrush, QLinearGradient
from utils.config import Config
from datetime import datetime, timedelta
import random  # للبيانات التجريبية

class ReportsWindow(QWidget):
    """نافذة التقارير المالية"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("التقارير المالية")
        self.setGeometry(100, 100, 1200, 700)

        # تطبيق التصميم العصري
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                font-family: 'Segoe UI', Arial, sans-serif;
                color: #2c3e50;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f1f3f4);
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px 15px;
                font-size: 13px;
                font-weight: 500;
                color: #2c3e50;
                min-height: 25px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-color: #667eea;
                color: white;
            }
            QTabWidget::pane {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f1f3f4);
                border: 2px solid #e0e0e0;
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 10px 20px;
                margin-right: 2px;
                font-weight: 500;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border-color: #667eea;
            }
            QTableWidget {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f0f0f0;
                selection-background-color: #667eea;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: black;
                padding: 10px;
                border: none;
                font-weight: 600;
            }
            QDateEdit {
                background-color: white;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
            }
            QDateEdit:focus {
                border-color: #667eea;
                outline: none;
            }
        """)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # شريط الأدوات العلوي (تم حذف التحليلات الذكية)
        toolbar_layout = QHBoxLayout()
        toolbar_layout.addStretch()
        main_layout.addLayout(toolbar_layout)

        # التبويبات
        tabs = QTabWidget()
        
        # تبويب تقرير المبيعات
        sales_tab = QWidget()
        self.setup_sales_report_tab(sales_tab)
        tabs.addTab(sales_tab, "تقرير المبيعات")
        
        # تبويب تقرير المشتريات
        purchases_tab = QWidget()
        self.setup_purchases_report_tab(purchases_tab)
        tabs.addTab(purchases_tab, "تقرير المشتريات")
        
        # تبويب الأرباح والخسائر
        profit_loss_tab = QWidget()
        self.setup_profit_loss_tab(profit_loss_tab)
        tabs.addTab(profit_loss_tab, "الأرباح والخسائر")
        
        # تبويب تقرير المخزون
        inventory_tab = QWidget()
        self.setup_inventory_tab(inventory_tab)
        tabs.addTab(inventory_tab, "تقرير المخزون")
        
        # تبويب التحاليل الذكية (الجديد)
        smart_analytics_tab = QWidget()
        self.setup_smart_analytics_tab(smart_analytics_tab)
        tabs.addTab(smart_analytics_tab, "📊 التحاليل الذكية")
        
        self.tabs = tabs  # حفظ مرجع للتبويبات
        main_layout.addWidget(tabs)
        self.setLayout(main_layout)


        
    def setup_sales_report_tab(self, tab):
        """إعداد تبويب تقرير المبيعات"""
        layout = QVBoxLayout()
        
        # فلاتر التاريخ
        filters_group = QGroupBox("فلاتر التقرير")
        filters_layout = QGridLayout()
        
        filters_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        self.sales_from_date = QDateEdit()
        self.sales_from_date.setDate(QDate.currentDate().addDays(-30))
        filters_layout.addWidget(self.sales_from_date, 0, 1)
        
        filters_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        self.sales_to_date = QDateEdit()
        self.sales_to_date.setDate(QDate.currentDate())
        filters_layout.addWidget(self.sales_to_date, 0, 3)
        
        generate_sales_btn = QPushButton("إنشاء التقرير")
        generate_sales_btn.clicked.connect(self.generate_sales_report)
        filters_layout.addWidget(generate_sales_btn, 0, 4)
        
        print_sales_btn = QPushButton("🖨️ طباعة الفاتورة")
        print_sales_btn.clicked.connect(self.print_selected_invoice)
        filters_layout.addWidget(print_sales_btn, 0, 5)

        export_sales_btn = QPushButton("تصدير Excel")
        export_sales_btn.clicked.connect(self.export_sales_report)
        filters_layout.addWidget(export_sales_btn, 0, 6)
        
        filters_group.setLayout(filters_layout)
        layout.addWidget(filters_group)
        
        # جدول تقرير المبيعات
        self.sales_report_table = QTableWidget()
        self.sales_report_table.setColumnCount(6)
        self.sales_report_table.setHorizontalHeaderLabels([
            "التاريخ", "رقم الفاتورة", "العميل", "المبلغ الإجمالي", "المدفوع", "المتبقي"
        ])
        
        header = self.sales_report_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.sales_report_table.setAlternatingRowColors(True)
        layout.addWidget(self.sales_report_table)
        
        # إحصائيات المبيعات
        self.sales_stats_label = QLabel()
        self.sales_stats_label.setStyleSheet("font-weight: bold; color: blue; padding: 10px;")
        layout.addWidget(self.sales_stats_label)
        
        tab.setLayout(layout)
        
    def setup_purchases_report_tab(self, tab):
        """إعداد تبويب تقرير المشتريات"""
        layout = QVBoxLayout()
        
        # فلاتر التاريخ
        filters_group = QGroupBox("فلاتر التقرير")
        filters_layout = QGridLayout()
        
        filters_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        self.purchases_from_date = QDateEdit()
        self.purchases_from_date.setDate(QDate.currentDate().addDays(-30))
        filters_layout.addWidget(self.purchases_from_date, 0, 1)
        
        filters_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        self.purchases_to_date = QDateEdit()
        self.purchases_to_date.setDate(QDate.currentDate())
        filters_layout.addWidget(self.purchases_to_date, 0, 3)
        
        generate_purchases_btn = QPushButton("إنشاء التقرير")
        generate_purchases_btn.clicked.connect(self.generate_purchases_report)
        filters_layout.addWidget(generate_purchases_btn, 0, 4)
        
        print_purchases_btn = QPushButton("🖨️ طباعة الفاتورة")
        print_purchases_btn.clicked.connect(self.print_selected_invoice)
        filters_layout.addWidget(print_purchases_btn, 0, 5)

        export_purchases_btn = QPushButton("تصدير Excel")
        export_purchases_btn.clicked.connect(self.export_purchases_report)
        filters_layout.addWidget(export_purchases_btn, 0, 6)
        
        filters_group.setLayout(filters_layout)
        layout.addWidget(filters_group)
        
        # جدول تقرير المشتريات
        self.purchases_report_table = QTableWidget()
        self.purchases_report_table.setColumnCount(6)
        self.purchases_report_table.setHorizontalHeaderLabels([
            "التاريخ", "رقم الفاتورة", "المورد", "المبلغ الإجمالي", "المدفوع", "المتبقي"
        ])
        
        header = self.purchases_report_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.purchases_report_table.setAlternatingRowColors(True)
        layout.addWidget(self.purchases_report_table)
        
        # إحصائيات المشتريات
        self.purchases_stats_label = QLabel()
        self.purchases_stats_label.setStyleSheet("font-weight: bold; color: blue; padding: 10px;")
        layout.addWidget(self.purchases_stats_label)
        
        tab.setLayout(layout)
        
    def setup_profit_loss_tab(self, tab):
        """إعداد تبويب الأرباح والخسائر"""
        layout = QVBoxLayout()
        
        # فلاتر التاريخ
        filters_group = QGroupBox("فترة التقرير")
        filters_layout = QGridLayout()
        
        filters_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        self.profit_from_date = QDateEdit()
        self.profit_from_date.setDate(QDate.currentDate().addDays(-30))
        filters_layout.addWidget(self.profit_from_date, 0, 1)
        
        filters_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        self.profit_to_date = QDateEdit()
        self.profit_to_date.setDate(QDate.currentDate())
        filters_layout.addWidget(self.profit_to_date, 0, 3)
        
        generate_profit_btn = QPushButton("حساب الأرباح والخسائر")
        generate_profit_btn.clicked.connect(self.calculate_profit_loss)
        filters_layout.addWidget(generate_profit_btn, 0, 4)
        
        filters_group.setLayout(filters_layout)
        layout.addWidget(filters_group)
        
        # عرض الأرباح والخسائر
        self.profit_loss_display = QTextEdit()
        self.profit_loss_display.setReadOnly(True)
        self.profit_loss_display.setStyleSheet("""
            QTextEdit {
                font-family: 'Courier New';
                font-size: 12px;
                background-color: #f8f9fa;
                border: 1px solid #ddd;
            }
        """)
        layout.addWidget(self.profit_loss_display)
        
        tab.setLayout(layout)
        
    def setup_inventory_tab(self, tab):
        """إعداد تبويب تقرير المخزون"""
        layout = QVBoxLayout()
        
        # أدوات التحكم
        controls_layout = QHBoxLayout()
        
        refresh_btn = QPushButton("تحديث التقرير")
        refresh_btn.clicked.connect(self.generate_inventory_report)
        controls_layout.addWidget(refresh_btn)
        
        export_inventory_btn = QPushButton("تصدير Excel")
        export_inventory_btn.clicked.connect(self.export_inventory_report)
        controls_layout.addWidget(export_inventory_btn)
        
        controls_layout.addStretch()
        
        # فلتر المنتجات منخفضة المخزون
        low_stock_check = QPushButton("المنتجات منخفضة المخزون فقط")
        low_stock_check.clicked.connect(self.show_low_stock_only)
        controls_layout.addWidget(low_stock_check)
        
        layout.addLayout(controls_layout)
        
        # جدول تقرير المخزون
        self.inventory_report_table = QTableWidget()
        self.inventory_report_table.setColumnCount(7)
        self.inventory_report_table.setHorizontalHeaderLabels([
            "اسم المنتج", "الكمية الحالية", "الحد الأدنى", "سعر الشراء", 
            "سعر البيع", "قيمة المخزون", "الحالة"
        ])
        
        header = self.inventory_report_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.inventory_report_table.setAlternatingRowColors(True)
        layout.addWidget(self.inventory_report_table)
        
        # إحصائيات المخزون
        self.inventory_stats_label = QLabel()
        self.inventory_stats_label.setStyleSheet("font-weight: bold; color: blue; padding: 10px;")
        layout.addWidget(self.inventory_stats_label)
        
        tab.setLayout(layout)
        
        # تحميل تقرير المخزون عند البداية
        self.generate_inventory_report()
        
    def generate_sales_report(self):
        """إنشاء تقرير المبيعات"""
        from_date = self.sales_from_date.date().toString('yyyy-MM-dd')
        to_date = self.sales_to_date.date().toString('yyyy-MM-dd')
        
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT i.invoice_date, i.invoice_number, c.name as customer_name,
                   i.final_amount, i.paid_amount, i.remaining_amount
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE i.invoice_type = 'sale' AND i.is_active = 1
            AND i.invoice_date BETWEEN ? AND ?
            ORDER BY i.invoice_date DESC
        ''', (from_date, to_date))
        
        sales = cursor.fetchall()
        conn.close()
        
        # تحديث الجدول
        self.sales_report_table.setRowCount(len(sales))
        
        total_amount = 0
        total_paid = 0
        total_remaining = 0
        
        for row, sale in enumerate(sales):
            self.sales_report_table.setItem(row, 0, QTableWidgetItem(sale['invoice_date']))
            self.sales_report_table.setItem(row, 1, QTableWidgetItem(sale['invoice_number']))
            self.sales_report_table.setItem(row, 2, QTableWidgetItem(sale['customer_name'] or 'عميل نقدي'))
            self.sales_report_table.setItem(row, 3, QTableWidgetItem(f"{sale['final_amount']:.2f}"))
            self.sales_report_table.setItem(row, 4, QTableWidgetItem(f"{sale['paid_amount']:.2f}"))
            self.sales_report_table.setItem(row, 5, QTableWidgetItem(f"{sale['remaining_amount']:.2f}"))
            
            total_amount += sale['final_amount']
            total_paid += sale['paid_amount']
            total_remaining += sale['remaining_amount']
            
        # تحديث الإحصائيات
        stats_text = f"""
        إجمالي الفواتير: {len(sales)} فاتورة
        إجمالي المبيعات: {total_amount:.2f} جنيه
        إجمالي المدفوع: {total_paid:.2f} جنيه
        إجمالي المتبقي: {total_remaining:.2f} جنيه
        """
        self.sales_stats_label.setText(stats_text)
        
    def generate_purchases_report(self):
        """إنشاء تقرير المشتريات"""
        from_date = self.purchases_from_date.date().toString('yyyy-MM-dd')
        to_date = self.purchases_to_date.date().toString('yyyy-MM-dd')
        
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT i.invoice_date, i.invoice_number, s.name as supplier_name,
                   i.final_amount, i.paid_amount, i.remaining_amount
            FROM invoices i
            LEFT JOIN suppliers s ON i.supplier_id = s.id
            WHERE i.invoice_type = 'purchase' AND i.is_active = 1
            AND i.invoice_date BETWEEN ? AND ?
            ORDER BY i.invoice_date DESC
        ''', (from_date, to_date))
        
        purchases = cursor.fetchall()
        conn.close()
        
        # تحديث الجدول
        self.purchases_report_table.setRowCount(len(purchases))
        
        total_amount = 0
        total_paid = 0
        total_remaining = 0
        
        for row, purchase in enumerate(purchases):
            self.purchases_report_table.setItem(row, 0, QTableWidgetItem(purchase['invoice_date']))
            self.purchases_report_table.setItem(row, 1, QTableWidgetItem(purchase['invoice_number']))
            self.purchases_report_table.setItem(row, 2, QTableWidgetItem(purchase['supplier_name'] or 'مورد نقدي'))
            self.purchases_report_table.setItem(row, 3, QTableWidgetItem(f"{purchase['final_amount']:.2f}"))
            self.purchases_report_table.setItem(row, 4, QTableWidgetItem(f"{purchase['paid_amount']:.2f}"))
            self.purchases_report_table.setItem(row, 5, QTableWidgetItem(f"{purchase['remaining_amount']:.2f}"))
            
            total_amount += purchase['final_amount']
            total_paid += purchase['paid_amount']
            total_remaining += purchase['remaining_amount']
            
        # تحديث الإحصائيات
        stats_text = f"""
        إجمالي الفواتير: {len(purchases)} فاتورة
        إجمالي المشتريات: {total_amount:.2f} جنيه
        إجمالي المدفوع: {total_paid:.2f} جنيه
        إجمالي المتبقي: {total_remaining:.2f} جنيه
        """
        self.purchases_stats_label.setText(stats_text)

    def calculate_profit_loss(self):
        """حساب الأرباح والخسائر"""
        from_date = self.profit_from_date.date().toString('yyyy-MM-dd')
        to_date = self.profit_to_date.date().toString('yyyy-MM-dd')

        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        # حساب إجمالي المبيعات
        cursor.execute('''
            SELECT SUM(final_amount) as total_sales
            FROM invoices
            WHERE invoice_type = 'sale' AND is_active = 1
            AND invoice_date BETWEEN ? AND ?
        ''', (from_date, to_date))

        total_sales = cursor.fetchone()['total_sales'] or 0

        # حساب تكلفة البضاعة المباعة
        cursor.execute('''
            SELECT SUM(ii.quantity * p.purchase_price) as cost_of_goods
            FROM invoice_items ii
            JOIN invoices i ON ii.invoice_id = i.id
            JOIN products p ON ii.product_id = p.id
            WHERE i.invoice_type = 'sale' AND i.is_active = 1
            AND i.invoice_date BETWEEN ? AND ?
        ''', (from_date, to_date))

        cost_of_goods = cursor.fetchone()['cost_of_goods'] or 0

        # حساب إجمالي المشتريات
        cursor.execute('''
            SELECT SUM(final_amount) as total_purchases
            FROM invoices
            WHERE invoice_type = 'purchase' AND is_active = 1
            AND invoice_date BETWEEN ? AND ?
        ''', (from_date, to_date))

        total_purchases = cursor.fetchone()['total_purchases'] or 0

        # حساب المرتجعات
        cursor.execute('''
            SELECT
                SUM(CASE WHEN return_type = 'sale_return' THEN total_amount ELSE 0 END) as sales_returns,
                SUM(CASE WHEN return_type = 'purchase_return' THEN total_amount ELSE 0 END) as purchase_returns
            FROM returns
            WHERE return_date BETWEEN ? AND ?
        ''', (from_date, to_date))

        returns_data = cursor.fetchone()
        sales_returns = returns_data['sales_returns'] or 0
        purchase_returns = returns_data['purchase_returns'] or 0

        # حساب المصاريف
        cursor.execute('''
            SELECT SUM(amount) as total_expenses
            FROM expenses
            WHERE expense_date BETWEEN ? AND ?
        ''', (from_date, to_date))

        total_expenses = cursor.fetchone()['total_expenses'] or 0

        conn.close()

        # حساب الأرباح
        net_sales = total_sales - sales_returns
        net_purchases = total_purchases - purchase_returns
        gross_profit = net_sales - cost_of_goods
        net_profit = gross_profit - total_expenses

        # عرض التقرير
        report_text = f"""
═══════════════════════════════════════════════════════════════
                    تقرير الأرباح والخسائر
                    من {from_date} إلى {to_date}
═══════════════════════════════════════════════════════════════

الإيرادات:
─────────────────────────────────────────────────────────────
إجمالي المبيعات                           {total_sales:>15,.2f} جنيه
ناقص: مرتجع المبيعات                      {sales_returns:>15,.2f} جنيه
                                          ─────────────────────
صافي المبيعات                             {net_sales:>15,.2f} جنيه

تكلفة البضاعة المباعة:
─────────────────────────────────────────────────────────────
تكلفة البضاعة المباعة                      {cost_of_goods:>15,.2f} جنيه
                                          ─────────────────────
إجمالي الربح                              {gross_profit:>15,.2f} جنيه

المشتريات في الفترة:
─────────────────────────────────────────────────────────────
إجمالي المشتريات                          {total_purchases:>15,.2f} جنيه
ناقص: مرتجع المشتريات                     {purchase_returns:>15,.2f} جنيه
                                          ─────────────────────
صافي المشتريات                            {net_purchases:>15,.2f} جنيه

المصاريف التشغيلية:
─────────────────────────────────────────────────────────────
إجمالي المصاريف                           {total_expenses:>15,.2f} جنيه
                                          ─────────────────────
صافي الربح                                {net_profit:>15,.2f} جنيه

═══════════════════════════════════════════════════════════════
النتيجة النهائية:
إجمالي الربح (قبل المصاريف): {gross_profit:,.2f} جنيه
صافي الربح (بعد المصاريف): {net_profit:,.2f} جنيه
نسبة الربح الإجمالي: {(gross_profit/net_sales*100) if net_sales > 0 else 0:.1f}%
نسبة الربح الصافي: {(net_profit/net_sales*100) if net_sales > 0 else 0:.1f}%
═══════════════════════════════════════════════════════════════
        """

        self.profit_loss_display.setText(report_text)

    def generate_inventory_report(self):
        """إنشاء تقرير المخزون"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT name, current_stock, min_stock_alert, purchase_price, retail_price
            FROM products
            WHERE is_active = 1
            ORDER BY name
        ''')

        products = cursor.fetchall()
        conn.close()

        # تحديث الجدول
        self.inventory_report_table.setRowCount(len(products))

        total_value = 0
        low_stock_count = 0

        for row, product in enumerate(products):
            self.inventory_report_table.setItem(row, 0, QTableWidgetItem(product['name']))
            self.inventory_report_table.setItem(row, 1, QTableWidgetItem(str(product['current_stock'])))
            self.inventory_report_table.setItem(row, 2, QTableWidgetItem(str(product['min_stock_alert'])))
            self.inventory_report_table.setItem(row, 3, QTableWidgetItem(f"{product['purchase_price']:.2f}"))
            self.inventory_report_table.setItem(row, 4, QTableWidgetItem(f"{product['retail_price']:.2f}"))

            # قيمة المخزون
            stock_value = product['current_stock'] * product['purchase_price']
            self.inventory_report_table.setItem(row, 5, QTableWidgetItem(f"{stock_value:.2f}"))
            total_value += stock_value

            # حالة المخزون
            if product['current_stock'] <= product['min_stock_alert']:
                status = "منخفض"
                status_item = QTableWidgetItem(status)
                status_item.setBackground(QColor(255, 200, 200))
                low_stock_count += 1
            else:
                status = "طبيعي"
                status_item = QTableWidgetItem(status)
                status_item.setBackground(QColor(200, 255, 200))

            self.inventory_report_table.setItem(row, 6, status_item)

        # تحديث الإحصائيات
        stats_text = f"""
        إجمالي المنتجات: {len(products)} منتج
        قيمة المخزون الإجمالية: {total_value:.2f} جنيه
        المنتجات منخفضة المخزون: {low_stock_count} منتج
        """
        self.inventory_stats_label.setText(stats_text)

    def show_low_stock_only(self):
        """عرض المنتجات منخفضة المخزون فقط"""
        for row in range(self.inventory_report_table.rowCount()):
            status_item = self.inventory_report_table.item(row, 6)
            if status_item and status_item.text() == "منخفض":
                self.inventory_report_table.setRowHidden(row, False)
            else:
                self.inventory_report_table.setRowHidden(row, True)

    def export_sales_report(self):
        """تصدير تقرير المبيعات إلى Excel"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import csv
            from datetime import datetime
            
            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, 
                "حفظ تقرير المبيعات", 
                f"تقرير_المبيعات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv);;All Files (*)"
            )
            
            if file_path:
                # جمع البيانات من الجدول
                table = self.sales_report_table
                data = []
                
                # إضافة العناوين
                headers = []
                for col in range(table.columnCount()):
                    headers.append(table.horizontalHeaderItem(col).text())
                data.append(headers)
                
                # إضافة البيانات
                for row in range(table.rowCount()):
                    row_data = []
                    for col in range(table.columnCount()):
                        item = table.item(row, col)
                        row_data.append(item.text() if item else '')
                    data.append(row_data)
                
                # كتابة الملف
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as file:
                    writer = csv.writer(file)
                    writer.writerows(data)
                
                QMessageBox.information(self, "نجح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{str(e)}")
        
    def export_purchases_report(self):
        """تصدير تقرير المشتريات إلى Excel"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import csv
            from datetime import datetime
            
            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, 
                "حفظ تقرير المشتريات", 
                f"تقرير_المشتريات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv);;All Files (*)"
            )
            
            if file_path:
                # جمع البيانات من الجدول
                table = self.purchases_report_table
                data = []
                
                # إضافة العناوين
                headers = []
                for col in range(table.columnCount()):
                    headers.append(table.horizontalHeaderItem(col).text())
                data.append(headers)
                
                # إضافة البيانات
                for row in range(table.rowCount()):
                    row_data = []
                    for col in range(table.columnCount()):
                        item = table.item(row, col)
                        row_data.append(item.text() if item else '')
                    data.append(row_data)
                
                # كتابة الملف
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as file:
                    writer = csv.writer(file)
                    writer.writerows(data)
                
                QMessageBox.information(self, "نجح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{str(e)}")
        
    def export_inventory_report(self):
        """تصدير تقرير المخزون إلى Excel"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import csv
            from datetime import datetime
            
            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, 
                "حفظ تقرير المخزون", 
                f"تقرير_المخزون_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv);;All Files (*)"
            )
            
            if file_path:
                # جمع البيانات من الجدول
                table = self.inventory_report_table
                data = []
                
                # إضافة العناوين
                headers = []
                for col in range(table.columnCount()):
                    headers.append(table.horizontalHeaderItem(col).text())
                data.append(headers)
                
                # إضافة البيانات
                for row in range(table.rowCount()):
                    row_data = []
                    for col in range(table.columnCount()):
                        item = table.item(row, col)
                        row_data.append(item.text() if item else '')
                    data.append(row_data)
                
                # كتابة الملف
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as file:
                    writer = csv.writer(file)
                    writer.writerows(data)
                
                QMessageBox.information(self, "نجح", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{str(e)}")
        
    def setup_smart_analytics_tab(self, tab):
        """إعداد تبويب التحاليل الذكية"""
        main_layout = QVBoxLayout()
        
        # عنوان التبويب
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4a148c, stop:1 #7b1fa2);
                border-radius: 10px;
                padding: 10px;
                margin-bottom: 15px;
            }
        """)
        header_layout = QHBoxLayout(header)
        
        title_label = QLabel("🧠 التحاليل الذكية والتوقعات المستقبلية")
        title_label.setStyleSheet("""
            color: white;
            font-size: 18px;
            font-weight: bold;
        """)
        header_layout.addWidget(title_label)
        
        main_layout.addWidget(header)
        
        # منطقة الخيارات والتحليلات
        content_area = QSplitter(Qt.Horizontal)
        
        # منطقة الخيارات (الجانب الأيمن)
        options_frame = QFrame()
        options_frame.setFrameShape(QFrame.StyledPanel)
        options_frame.setStyleSheet("""
            QFrame {
                background-color: #f5f5f5;
                border-radius: 10px;
                border: 1px solid #e0e0e0;
            }
        """)
        options_layout = QVBoxLayout(options_frame)
        
        # عنوان الخيارات
        options_title = QLabel("خيارات التحليل")
        options_title.setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 10px;")
        options_layout.addWidget(options_title)
        
        # فترة التحليل
        period_group = QGroupBox("فترة التحليل")
        period_layout = QVBoxLayout()
        
        # خيارات الفترة
        self.period_month = QRadioButton("الشهر الحالي")
        self.period_quarter = QRadioButton("الربع الحالي")
        self.period_year = QRadioButton("السنة الحالية")
        self.period_custom = QRadioButton("فترة مخصصة")
        
        # تحديد الخيار الافتراضي
        self.period_month.setChecked(True)
        
        # إضافة الخيارات إلى المجموعة
        period_layout.addWidget(self.period_month)
        period_layout.addWidget(self.period_quarter)
        period_layout.addWidget(self.period_year)
        period_layout.addWidget(self.period_custom)
        
        # تواريخ مخصصة
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel("من:"))
        self.analytics_from_date = QDateEdit()
        self.analytics_from_date.setDate(QDate.currentDate().addDays(-30))
        date_layout.addWidget(self.analytics_from_date)
        
        date_layout.addWidget(QLabel("إلى:"))
        self.analytics_to_date = QDateEdit()
        self.analytics_to_date.setDate(QDate.currentDate())
        date_layout.addWidget(self.analytics_to_date)
        
        period_layout.addLayout(date_layout)
        period_group.setLayout(period_layout)
        options_layout.addWidget(period_group)
        
        # نوع التحليل
        analysis_group = QGroupBox("نوع التحليل")
        analysis_layout = QVBoxLayout()
        
        self.analysis_sales_trend = QCheckBox("تحليل اتجاهات المبيعات")
        self.analysis_sales_trend.setChecked(True)
        analysis_layout.addWidget(self.analysis_sales_trend)
        
        self.analysis_product_performance = QCheckBox("أداء المنتجات")
        self.analysis_product_performance.setChecked(True)
        analysis_layout.addWidget(self.analysis_product_performance)
        
        self.analysis_customer_insights = QCheckBox("تحليل العملاء")
        analysis_layout.addWidget(self.analysis_customer_insights)
        
        self.analysis_inventory_prediction = QCheckBox("توقعات المخزون")
        analysis_layout.addWidget(self.analysis_inventory_prediction)
        
        self.analysis_profit_forecast = QCheckBox("توقعات الأرباح")
        analysis_layout.addWidget(self.analysis_profit_forecast)
        
        analysis_group.setLayout(analysis_layout)
        options_layout.addWidget(analysis_group)
        
        # زر تحليل البيانات
        analyze_btn = QPushButton("تحليل البيانات")
        analyze_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4a148c, stop:1 #7b1fa2);
                color: white;
                font-weight: bold;
                padding: 10px;
                border-radius: 5px;
                margin-top: 10px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #7b1fa2, stop:1 #9c27b0);
            }
        """)
        analyze_btn.clicked.connect(self.generate_smart_analytics)
        options_layout.addWidget(analyze_btn)
        
        # إضافة مساحة فارغة في النهاية
        options_layout.addStretch()
        
        # منطقة عرض التحليلات (الجانب الأيسر)
        results_scroll = QScrollArea()
        results_scroll.setWidgetResizable(True)
        results_scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: white;
            }
        """)
        
        self.analytics_results_widget = QWidget()
        self.analytics_results_layout = QVBoxLayout(self.analytics_results_widget)
        
        # رسالة ترحيبية
        welcome_frame = QFrame()
        welcome_frame.setStyleSheet("""
            QFrame {
                background-color: #e8f5e9;
                border-radius: 10px;
                padding: 20px;
                margin: 10px;
            }
        """)
        welcome_layout = QVBoxLayout(welcome_frame)
        
        welcome_title = QLabel("مرحباً بك في التحاليل الذكية! 🚀")
        welcome_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2e7d32;")
        welcome_layout.addWidget(welcome_title)
        
        welcome_text = QLabel(
            "هذه الميزة تساعدك على تحليل بيانات عملك بطريقة ذكية واستخراج رؤى قيمة "
            "تساعدك على اتخاذ قرارات أفضل لنمو عملك.\n\n"
            "اختر خيارات التحليل من القائمة على اليمين ثم اضغط على زر 'تحليل البيانات' "
            "للحصول على تحليلات مفصلة ورسوم بيانية توضح اتجاهات عملك."
        )
        welcome_text.setWordWrap(True)
        welcome_text.setStyleSheet("font-size: 14px; color: #37474f;")
        welcome_layout.addWidget(welcome_text)
        
        self.analytics_results_layout.addWidget(welcome_frame)
        self.analytics_results_layout.addStretch()
        
        results_scroll.setWidget(self.analytics_results_widget)
        
        # إضافة المناطق إلى السبليتر
        content_area.addWidget(options_frame)
        content_area.addWidget(results_scroll)
        
        # تعيين النسب الافتراضية للسبليتر
        content_area.setSizes([300, 700])
        
        main_layout.addWidget(content_area)
        tab.setLayout(main_layout)
        
    def generate_smart_analytics(self):
        """توليد التحاليل الذكية بناءً على الخيارات المحددة"""
        # مسح النتائج السابقة
        for i in reversed(range(self.analytics_results_layout.count())):
            widget = self.analytics_results_layout.itemAt(i).widget()
            if widget:
                widget.deleteLater()
        
        # تحديد الفترة
        if self.period_month.isChecked():
            period_text = "الشهر الحالي"
            from_date = QDate.currentDate().addDays(-30)
            to_date = QDate.currentDate()
        elif self.period_quarter.isChecked():
            period_text = "الربع الحالي"
            from_date = QDate.currentDate().addDays(-90)
            to_date = QDate.currentDate()
        elif self.period_year.isChecked():
            period_text = "السنة الحالية"
            from_date = QDate.currentDate().addDays(-365)
            to_date = QDate.currentDate()
        else:  # فترة مخصصة
            period_text = "الفترة المخصصة"
            from_date = self.analytics_from_date.date()
            to_date = self.analytics_to_date.date()
        
        # إضافة عنوان التحليل
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #e3f2fd;
                border-radius: 10px;
                padding: 15px;
                margin: 10px;
            }
        """)
        header_layout = QVBoxLayout(header_frame)
        
        header_title = QLabel(f"تحليل البيانات لـ {period_text}")
        header_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #0d47a1;")
        header_layout.addWidget(header_title)
        
        date_range = QLabel(f"الفترة: من {from_date.toString('yyyy-MM-dd')} إلى {to_date.toString('yyyy-MM-dd')}")
        date_range.setStyleSheet("font-size: 12px; color: #546e7a;")
        header_layout.addWidget(date_range)
        
        self.analytics_results_layout.addWidget(header_frame)
        
        # إضافة التحليلات المطلوبة
        if self.analysis_sales_trend.isChecked():
            self.add_sales_trend_analysis()
        
        if self.analysis_product_performance.isChecked():
            self.add_product_performance_analysis()
        
        if self.analysis_customer_insights.isChecked():
            self.add_customer_insights_analysis()
        
        if self.analysis_inventory_prediction.isChecked():
            self.add_inventory_prediction_analysis()
        
        if self.analysis_profit_forecast.isChecked():
            self.add_profit_forecast_analysis()
        
        # إضافة مساحة في النهاية
        self.analytics_results_layout.addStretch()
        
    def add_sales_trend_analysis(self):
        """إضافة تحليل اتجاهات المبيعات المحسن"""
        analysis_frame = self.create_analysis_frame("📈 تحليل اتجاهات المبيعات")
        layout = QVBoxLayout(analysis_frame)
        
        # الحصول على بيانات المبيعات الحقيقية
        sales_data = self.get_real_sales_data()
        
        if not sales_data:
            # في حالة عدم وجود بيانات
            no_data_label = QLabel("⚠️ لا توجد بيانات مبيعات كافية للتحليل")
            no_data_label.setStyleSheet("color: #ffc107; font-size: 14px; font-weight: bold; padding: 20px;")
            layout.addWidget(no_data_label)
            self.analytics_results_layout.addWidget(analysis_frame)
            return
        
        # عرض البيانات في جدول محسن
        table = QTableWidget(2, len(sales_data))
        months = list(sales_data.keys())
        values = list(sales_data.values())
        
        table.setHorizontalHeaderLabels(months)
        table.setVerticalHeaderLabels(["المبيعات (جنيه)", "النمو %"])
        
        # ملء البيانات
        for i, (month, value) in enumerate(sales_data.items()):
            # قيمة المبيعات
            sales_item = QTableWidgetItem(f"{value:,.0f}")
            sales_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(0, i, sales_item)
            
            # حساب معدل النمو
            if i > 0:
                prev_value = values[i-1]
                growth = ((value - prev_value) / prev_value * 100) if prev_value > 0 else 0
                growth_item = QTableWidgetItem(f"{growth:+.1f}%")
                growth_item.setTextAlignment(Qt.AlignCenter)
                
                # تلوين النمو
                if growth > 0:
                    growth_item.setBackground(QColor(200, 255, 200))
                elif growth < 0:
                    growth_item.setBackground(QColor(255, 200, 200))
                else:
                    growth_item.setBackground(QColor(255, 255, 200))
                    
                table.setItem(1, i, growth_item)
            else:
                table.setItem(1, i, QTableWidgetItem("--"))
        
        table.setMaximumHeight(120)
        table.resizeColumnsToContents()
        layout.addWidget(table)
        
        # تحليل الاتجاه المحسن
        trend_analysis = self.analyze_sales_trend(values)
        
        trend_label = QLabel(trend_analysis['description'])
        trend_label.setStyleSheet("font-weight: bold; margin-top: 10px; padding: 10px; border-radius: 5px; background-color: #f8f9fa;")
        layout.addWidget(trend_label)
        
        # إحصائيات مفصلة
        stats_text = f"""
        <b>📊 إحصائيات مفصلة:</b><br>
        • متوسط المبيعات الشهرية: {sum(values)/len(values):,.0f} جنيه<br>
        • أعلى مبيعات: {max(values):,.0f} جنيه في {months[values.index(max(values))]}<br>
        • أقل مبيعات: {min(values):,.0f} جنيه في {months[values.index(min(values))]}<br>
        • إجمالي المبيعات: {sum(values):,.0f} جنيه<br>
        • معدل النمو الإجمالي: {trend_analysis['total_growth']:+.1f}%
        """
        
        stats_label = QLabel(stats_text)
        stats_label.setStyleSheet("margin-top: 10px; padding: 10px; border-radius: 5px; background-color: #e3f2fd;")
        layout.addWidget(stats_label)
        
        # توصيات ذكية
        recommendations = self.generate_smart_recommendations(trend_analysis, sales_data)
        
        recommendations_label = QLabel(recommendations)
        recommendations_label.setStyleSheet("margin-top: 10px; padding: 10px; border-radius: 5px; background-color: #e8f5e9;")
        layout.addWidget(recommendations_label)
        
        self.analytics_results_layout.addWidget(analysis_frame)
        
    def add_product_performance_analysis(self):
        """إضافة تحليل أداء المنتجات"""
        analysis_frame = self.create_analysis_frame("🏆 تحليل أداء المنتجات")
        layout = QVBoxLayout(analysis_frame)
        
        # محاكاة بيانات أداء المنتجات
        products = ["منتج 1", "منتج 2", "منتج 3", "منتج 4", "منتج 5"]
        sales_count = [random.randint(50, 200) for _ in range(5)]
        revenue = [count * random.randint(50, 200) for count in sales_count]
        profit_margin = [random.randint(15, 40) for _ in range(5)]
        
        # عرض البيانات في جدول
        table = QTableWidget(5, 4)
        table.setHorizontalHeaderLabels(["المنتج", "عدد المبيعات", "الإيرادات", "هامش الربح %"])
        
        for i in range(5):
            table.setItem(i, 0, QTableWidgetItem(products[i]))
            table.setItem(i, 1, QTableWidgetItem(str(sales_count[i])))
            table.setItem(i, 2, QTableWidgetItem(f"{revenue[i]:.2f}"))
            table.setItem(i, 3, QTableWidgetItem(f"{profit_margin[i]}%"))
        
        table.setMaximumHeight(200)
        layout.addWidget(table)
        
        # تحليل الأداء
        best_product_index = revenue.index(max(revenue))
        best_margin_index = profit_margin.index(max(profit_margin))
        
        analysis_text = f"""
        <b>أفضل المنتجات أداءً:</b>
        • <span style='color: #2e7d32;'>{products[best_product_index]}</span> - الأعلى من حيث الإيرادات بقيمة {revenue[best_product_index]:.2f}
        • <span style='color: #2e7d32;'>{products[best_margin_index]}</span> - الأعلى من حيث هامش الربح بنسبة {profit_margin[best_margin_index]}%
        
        <b>توصيات:</b>
        • زيادة المخزون من المنتجات الأكثر مبيعاً
        • مراجعة تكاليف المنتجات ذات هامش الربح المنخفض
        • التركيز على الترويج للمنتجات ذات هامش الربح المرتفع
        """
        
        analysis_label = QLabel(analysis_text)
        analysis_label.setStyleSheet("margin-top: 10px;")
        layout.addWidget(analysis_label)
        
        self.analytics_results_layout.addWidget(analysis_frame)
        
    def add_customer_insights_analysis(self):
        """إضافة تحليل العملاء"""
        analysis_frame = self.create_analysis_frame("👥 تحليل العملاء")
        layout = QVBoxLayout(analysis_frame)
        
        # محاكاة بيانات العملاء
        customer_segments = ["عملاء جدد", "عملاء متكررون", "عملاء مميزون"]
        customer_counts = [random.randint(20, 100), random.randint(30, 150), random.randint(10, 50)]
        revenue_per_segment = [count * random.randint(100, 500) for count in customer_counts]
        
        # عرض البيانات في جدول
        table = QTableWidget(3, 3)
        table.setHorizontalHeaderLabels(["شريحة العملاء", "عدد العملاء", "الإيرادات"])
        
        for i in range(3):
            table.setItem(i, 0, QTableWidgetItem(customer_segments[i]))
            table.setItem(i, 1, QTableWidgetItem(str(customer_counts[i])))
            table.setItem(i, 2, QTableWidgetItem(f"{revenue_per_segment[i]:.2f}"))
        
        table.setMaximumHeight(150)
        layout.addWidget(table)
        
        # تحليل العملاء
        total_customers = sum(customer_counts)
        total_revenue = sum(revenue_per_segment)
        
        analysis_text = f"""
        <b>تحليل شرائح العملاء:</b>
        • إجمالي العملاء: {total_customers}
        • إجمالي الإيرادات: {total_revenue:.2f}
        • متوسط الإيراد لكل عميل: {(total_revenue / total_customers):.2f}
        
        <b>توصيات:</b>
        • إطلاق برنامج ولاء للعملاء المتكررين
        • تقديم عروض خاصة للعملاء المميزين
        • حملات تسويقية لاستقطاب عملاء جدد
        """
        
        analysis_label = QLabel(analysis_text)
        analysis_label.setStyleSheet("margin-top: 10px;")
        layout.addWidget(analysis_label)
        
        self.analytics_results_layout.addWidget(analysis_frame)
        
    def add_inventory_prediction_analysis(self):
        """إضافة توقعات المخزون"""
        analysis_frame = self.create_analysis_frame("📦 توقعات المخزون")
        layout = QVBoxLayout(analysis_frame)
        
        # محاكاة بيانات المخزون
        products = ["منتج 1", "منتج 2", "منتج 3", "منتج 4"]
        current_stock = [random.randint(10, 100) for _ in range(4)]
        monthly_consumption = [random.randint(5, 30) for _ in range(4)]
        days_until_empty = [int(current_stock[i] / (monthly_consumption[i] / 30)) for i in range(4)]
        reorder_recommendation = ["نعم" if days <= 30 else "لا" for days in days_until_empty]
        
        # عرض البيانات في جدول
        table = QTableWidget(4, 5)
        table.setHorizontalHeaderLabels(["المنتج", "المخزون الحالي", "الاستهلاك الشهري", "الأيام المتبقية", "إعادة الطلب"])
        
        for i in range(4):
            table.setItem(i, 0, QTableWidgetItem(products[i]))
            table.setItem(i, 1, QTableWidgetItem(str(current_stock[i])))
            table.setItem(i, 2, QTableWidgetItem(str(monthly_consumption[i])))
            table.setItem(i, 3, QTableWidgetItem(str(days_until_empty[i])))
            
            reorder_item = QTableWidgetItem(reorder_recommendation[i])
            if reorder_recommendation[i] == "نعم":
                reorder_item.setBackground(QColor(255, 200, 200))
            table.setItem(i, 4, reorder_item)
        
        table.setMaximumHeight(180)
        layout.addWidget(table)
        
        # تحليل المخزون
        products_to_reorder = reorder_recommendation.count("نعم")
        
        analysis_text = f"""
        <b>تحليل المخزون:</b>
        • عدد المنتجات التي تحتاج إعادة طلب: {products_to_reorder}
        • نسبة المنتجات التي تحتاج إعادة طلب: {(products_to_reorder / len(products) * 100):.1f}%
        
        <b>توصيات:</b>
        • إعداد طلبات شراء للمنتجات التي ستنفد قريباً
        • مراجعة معدلات الاستهلاك الشهري وتعديل كميات الطلب
        • تحديد المنتجات ذات معدل الدوران البطيء وتقليل مخزونها
        """
        
        analysis_label = QLabel(analysis_text)
        analysis_label.setStyleSheet("margin-top: 10px;")
        layout.addWidget(analysis_label)
        
        self.analytics_results_layout.addWidget(analysis_frame)
        
    def add_profit_forecast_analysis(self):
        """إضافة توقعات الأرباح"""
        analysis_frame = self.create_analysis_frame("💰 توقعات الأرباح")
        layout = QVBoxLayout(analysis_frame)
        
        # محاكاة بيانات الأرباح
        months_future = ["الشهر القادم", "بعد شهرين", "بعد 3 أشهر"]
        revenue_forecast = [random.randint(20000, 50000) for _ in range(3)]
        expense_forecast = [random.randint(15000, 30000) for _ in range(3)]
        profit_forecast = [revenue_forecast[i] - expense_forecast[i] for i in range(3)]
        
        # عرض البيانات في جدول
        table = QTableWidget(3, 4)
        table.setHorizontalHeaderLabels(["الفترة", "الإيرادات المتوقعة", "المصاريف المتوقعة", "الربح المتوقع"])
        
        for i in range(3):
            table.setItem(i, 0, QTableWidgetItem(months_future[i]))
            table.setItem(i, 1, QTableWidgetItem(f"{revenue_forecast[i]:.2f}"))
            table.setItem(i, 2, QTableWidgetItem(f"{expense_forecast[i]:.2f}"))
            
            profit_item = QTableWidgetItem(f"{profit_forecast[i]:.2f}")
            if profit_forecast[i] > 0:
                profit_item.setForeground(QColor(0, 128, 0))  # أخضر للربح
            else:
                profit_item.setForeground(QColor(255, 0, 0))  # أحمر للخسارة
            table.setItem(i, 3, profit_item)
        
        table.setMaximumHeight(150)
        layout.addWidget(table)
        
        # تحليل توقعات الأرباح
        avg_profit = sum(profit_forecast) / len(profit_forecast)
        profit_trend = "تصاعدي" if profit_forecast[2] > profit_forecast[0] else "تنازلي"
        
        analysis_text = f"""
        <b>تحليل توقعات الأرباح:</b>
        • متوسط الربح المتوقع: {avg_profit:.2f}
        • اتجاه الأرباح: {profit_trend}
        
        <b>توصيات:</b>
        • التركيز على زيادة المبيعات في المنتجات ذات هامش الربح المرتفع
        • مراجعة المصاريف وتقليلها حيثما أمكن
        • وضع خطة لتحسين الأرباح في الفترة القادمة
        """
        
        analysis_label = QLabel(analysis_text)
        analysis_label.setStyleSheet("margin-top: 10px;")
        layout.addWidget(analysis_label)
        
        self.analytics_results_layout.addWidget(analysis_frame)
        
    def create_analysis_frame(self, title):
        """إنشاء إطار التحليل المحسن"""
        frame = QFrame()
        frame.setFrameShape(QFrame.StyledPanel)
        frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 2px solid #007bff;
                border-radius: 12px;
                margin: 8px;
                padding: 15px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # إضافة عنوان محسن
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #007bff, stop:1 #0056b3);
                border-radius: 8px;
                padding: 10px;
                margin-bottom: 15px;
            }
        """)
        
        title_layout = QHBoxLayout(title_frame)
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: white;
                margin: 0;
            }
        """)
        title_layout.addWidget(title_label)
        
        # إضافة رمز معلومات
        info_label = QLabel("ℹ️")
        info_label.setStyleSheet("color: white; font-size: 16px;")
        title_layout.addWidget(info_label)
        
        layout.addWidget(title_frame)
        
        return frame


    def print_selected_invoice(self):
        """طباعة الفاتورة المحددة"""
        try:
            # تحديد الجدول النشط
            current_tab = self.tabs.currentIndex()

            if current_tab == 0:  # تبويب المبيعات
                table = self.sales_report_table
                invoice_type = 'sale'
            elif current_tab == 1:  # تبويب المشتريات
                table = self.purchases_report_table
                invoice_type = 'purchase'
            else:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار تبويب المبيعات أو المشتريات")
                return

            # التحقق من وجود صف محدد
            current_row = table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للطباعة")
                return

            # الحصول على رقم الفاتورة
            invoice_number = table.item(current_row, 1).text()

            # البحث عن معرف الفاتورة
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id FROM invoices
                WHERE invoice_number = ? AND invoice_type = ?
            ''', (invoice_number, invoice_type))

            result = cursor.fetchone()
            conn.close()

            if not result:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة")
                return

            invoice_id = result['id']

            # فتح نافذة الطباعة
            from ui.professional_printer import ProfessionalPrinter
            printer_dialog = ProfessionalPrinter(self.db_manager, invoice_id, invoice_type, self)
            printer_dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في طباعة الفاتورة:\n{str(e)}")
            
    def get_real_sales_data(self):
        """الحصول على بيانات المبيعات الحقيقية"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # الحصول على المبيعات الشهرية للسنة الحالية
            cursor.execute('''
                SELECT 
                    strftime('%Y-%m', invoice_date) as month,
                    SUM(final_amount) as total_sales
                FROM invoices 
                WHERE invoice_type = 'sale' AND is_active = 1
                AND invoice_date >= date('now', '-12 months')
                GROUP BY strftime('%Y-%m', invoice_date)
                ORDER BY month
            ''')
            
            results = cursor.fetchall()
            conn.close()
            
            if not results:
                return None
                
            # تحويل البيانات إلى قاموس
            sales_data = {}
            month_names = {
                '01': 'يناير', '02': 'فبراير', '03': 'مارس', '04': 'أبريل',
                '05': 'مايو', '06': 'يونيو', '07': 'يوليو', '08': 'أغسطس',
                '09': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
            }
            
            for row in results:
                month_key = row['month']
                month_name = month_names.get(month_key.split('-')[1], month_key)
                sales_data[month_name] = float(row['total_sales'])
            
            return sales_data
            
        except Exception as e:
            print(f"خطأ في الحصول على بيانات المبيعات: {e}")
            return None
    
    def analyze_sales_trend(self, values):
        """تحليل اتجاه المبيعات"""
        if len(values) < 2:
            return {'description': 'لا توجد بيانات كافية للتحليل', 'total_growth': 0}
        
        # حساب النمو الإجمالي
        total_growth = ((values[-1] - values[0]) / values[0] * 100) if values[0] > 0 else 0
        
        # تحديد الاتجاه
        if total_growth > 10:
            trend_icon = "🚀"
            trend_desc = "نمو ممتاز"
        elif total_growth > 5:
            trend_icon = "🟢"
            trend_desc = "نمو جيد"
        elif total_growth > 0:
            trend_icon = "📈"
            trend_desc = "نمو طفيف"
        elif total_growth > -5:
            trend_icon = "📉"
            trend_desc = "انخفاض طفيف"
        else:
            trend_icon = "🔴"
            trend_desc = "انخفاض كبير"
        
        # تحليل آخر شهرين
        if len(values) >= 2:
            last_month_growth = ((values[-1] - values[-2]) / values[-2] * 100) if values[-2] > 0 else 0
            if last_month_growth > 0:
                recent_trend = f" مع نمو {last_month_growth:.1f}% في الشهر الأخير"
            else:
                recent_trend = f" مع انخفاض {abs(last_month_growth):.1f}% في الشهر الأخير"
        else:
            recent_trend = ""
        
        description = f"{trend_icon} {trend_desc}: {total_growth:+.1f}%{recent_trend}"
        
        return {
            'description': description,
            'total_growth': total_growth,
            'trend_type': trend_desc
        }
    
    def generate_smart_recommendations(self, trend_analysis, sales_data):
        """توليد توصيات ذكية"""
        recommendations = "<b>💡 توصيات ذكية:</b><br>"
        
        if trend_analysis['total_growth'] > 5:
            recommendations += "• استمر في الاستراتيجية الحالية - النتائج ممتازة!<br>"
            recommendations += "• فكر في توسيع العمليات أو إضافة منتجات جديدة<br>"
            recommendations += "• استثمر في التسويق لزيادة الوعي بالعلامة التجارية<br>"
        elif trend_analysis['total_growth'] > 0:
            recommendations += "• راجع استراتيجية التسعير لتحسين الأرباح<br>"
            recommendations += "• حسن خدمة العملاء لزيادة الولاء<br>"
            recommendations += "• ادرس السوق لفهم احتياجات العملاء بشكل أفضل<br>"
        else:
            recommendations += "• 🚨 تحتاج إلى مراجعة عاجلة للاستراتيجية<br>"
            recommendations += "• ادرس أسباب انخفاض المبيعات<br>"
            recommendations += "• فكر في عروض ترويجية أو خصومات مؤقتة<br>"
            recommendations += "• راجع المنافسين وتحليل السوق<br>"
        
        # توصيات إضافية حسب الموسم
        from datetime import datetime
        current_month = datetime.now().month
        
        if current_month in [11, 12, 1]:  # فصل الشتاء
            recommendations += "• استغل موسم الشتاء بعروض خاصة<br>"
        elif current_month in [6, 7, 8]:  # فصل الصيف
            recommendations += "• ركز على المنتجات الموسمية الصيفية<br>"
        
        return recommendations
