#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل التطبيق مع معالجة شاملة للأخطاء
"""

import sys
import os

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    try:
        import PyQt5
        print("✅ PyQt5 موجود")
    except ImportError:
        print("❌ PyQt5 غير موجود - يرجى تثبيته: pip install PyQt5")
        return False
    
    try:
        import sqlite3
        print("✅ SQLite3 موجود")
    except ImportError:
        print("❌ SQLite3 غير موجود")
        return False
    
    return True

def run_application():
    """تشغيل التطبيق"""
    try:
        print("🚀 بدء تشغيل التطبيق...")
        
        # استيراد المكتبات المطلوبة
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import Qt
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("نظام الطيب للتجارة والتوزيع")
        
        try:
            # استيراد المكونات
            from database.db_manager import DatabaseManager
            from ui.login_window import LoginWindow
            from ui.main_dashboard import MainDashboard
            from utils.config import Config
            
            print("✅ تم استيراد جميع المكونات")
            
            # إنشاء قاعدة البيانات
            db_manager = DatabaseManager()
            print("✅ تم إنشاء قاعدة البيانات")
            
            # إنشاء الإعدادات
            config = Config()
            print("✅ تم تحميل الإعدادات")
            
            # إنشاء نافذة تسجيل الدخول
            login_window = LoginWindow(db_manager)
            
            def on_login_success(user_data):
                """عند نجاح تسجيل الدخول"""
                try:
                    print(f"✅ تم تسجيل الدخول: {user_data.get('username', 'مجهول')}")
                    login_window.close()
                    
                    # فتح النافذة الرئيسية
                    main_window = MainDashboard(db_manager, user_data)
                    main_window.show()
                    
                except Exception as e:
                    print(f"❌ خطأ في فتح النافذة الرئيسية: {str(e)}")
                    QMessageBox.critical(None, "خطأ", f"خطأ في فتح النافذة الرئيسية:\n{str(e)}")
            
            # ربط إشارة نجاح تسجيل الدخول
            login_window.login_successful.connect(on_login_success)
            
            # عرض نافذة تسجيل الدخول
            login_window.show()
            print("✅ تم عرض نافذة تسجيل الدخول")
            
            # تشغيل التطبيق
            print("🎯 التطبيق جاهز للاستخدام!")
            sys.exit(app.exec_())
            
        except ImportError as e:
            print(f"❌ خطأ في الاستيراد: {str(e)}")
            QMessageBox.critical(None, "خطأ في الاستيراد", 
                               f"تعذر تحميل مكونات التطبيق:\n{str(e)}\n\nتأكد من وجود جميع الملفات")
            
        except Exception as e:
            print(f"❌ خطأ عام: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(None, "خطأ", f"حدث خطأ في التطبيق:\n{str(e)}")
            
    except ImportError as e:
        print(f"❌ خطأ في استيراد PyQt5: {str(e)}")
        print("يرجى تثبيت PyQt5: pip install PyQt5")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🏪 نظام الطيب للتجارة والتوزيع")
    print("=" * 60)
    
    # فحص المتطلبات
    if not check_requirements():
        print("❌ فشل في فحص المتطلبات")
        input("اضغط Enter للخروج...")
        return
    
    # تشغيل التطبيق
    run_application()

if __name__ == "__main__":
    main()
