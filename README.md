# 🏪 نظام الطيب للتجارة والتوزيع

نظام إدارة شامل للمبيعات والمشتريات والمخزون

## 🚀 طرق تشغيل التطبيق

### الطريقة الأولى: مل<PERSON> (الأسهل)
```
انقر مرتين على: start_app.bat
```

### الطريقة الثانية: النسخة الحديثة (مُوصى بها)
```bash
python modern_app.py
```

### الطريقة الثالثة: النسخة البسيطة
```bash
python simple_app.py
```

### الطريقة الرابعة: النسخة الأصلية
```bash
python main.py
```

## 📋 المتطلبات

- Python 3.7 أو أحدث
- PyQt5
- SQLite3 (مدمج مع Python)

## 🔧 تثبيت المتطلبات

```bash
pip install PyQt5
```

## 🏗️ هيكل التطبيق

```
📁 ZERO/
├── 📄 main.py              # الملف الرئيسي
├── 📄 run_app.py           # ملف تشغيل بديل
├── 📄 start_app.bat        # ملف تشغيل Windows
├── 📁 database/            # قاعدة البيانات
├── 📁 ui/                  # واجهات المستخدم
├── 📁 utils/               # أدوات مساعدة
└── 📁 dist/                # ملفات التوزيع
```

## ✨ المميزات الجديدة - النسخة الحديثة

### 🎨 **الواجهة الحديثة:**
- 🔐 **نظام تسجيل دخول آمن** مع إدارة المستخدمين
- 🎨 **تصميم حديث ومريح للعين** مع ألوان هادئة
- 📱 **واجهة متجاوبة** تتكيف مع أحجام الشاشات المختلفة
- 🌐 **دعم كامل للغة العربية** مع اتجاه RTL
- 📊 **إحصائيات فورية** على الشاشة الرئيسية

### 💼 **الوظائف التجارية المتكاملة:**

#### 📊 إدارة المبيعات
- 💰 **فواتير البيع** مع طباعة احترافية ومعاينة فورية
- 🔥 **فواتير متعددة** للعمل على عدة فواتير في نفس الوقت
- 👥 **إدارة العملاء** مع سجل كامل للمعاملات
- 🎯 **وصول سريع** لإنشاء فاتورة بيع جديدة

#### 🛒 إدارة المشتريات
- 🛒 **فواتير الشراء** مع ربط المخزون تلقائياً
- 🏭 **إدارة الموردين** مع تتبع المدفوعات
- 🔄 **مرتجعات المشتريات** مع تحديث المخزون

#### 📦 إدارة المخزون
- 📦 **إدارة المنتجات الكاملة** مع تتبع الكميات
- ⚠️ **تنبيهات النفاد** عند انخفاض المخزون
- 🔍 **بحث متقدم** في المنتجات
- 📈 **تقارير المخزون** التفصيلية

#### 💸 الإدارة المالية المتطورة
- 💸 **إدارة المصاريف** مع تصنيفات مختلفة
- 💼 **الإدارة المالية المتكاملة** لرأس المال والأرباح
- 🏦 **إدارة البنك والتحويلات** مع تتبع الحسابات
- 📊 **تقارير مالية شاملة** مع رسوم بيانية

#### 🔄 نظام المرتجعات
- 🔄 **مرتجعات المبيعات والمشتريات**
- 📋 **تتبع أسباب الإرجاع**
- 💰 **تحديث الحسابات تلقائياً**

### 🔧 **المميزات التقنية:**
- 💾 **نسخ احتياطي تلقائي** لحماية البيانات
- ⚙️ **إعدادات متقدمة** لتخصيص النظام
- 🔍 **بحث متقدم** في جميع الوحدات
- 📈 **إحصائيات حية** من قاعدة البيانات
- 🎨 **ألوان مريحة للعين** قابلة للتخصيص

## 🎛️ الوظائف المتاحة في الشريط الجانبي

| الوظيفة | الوصف | الأيقونة |
|---------|--------|---------|
| **المبيعات** | إدارة فواتير البيع والعملاء | 💰 |
| **فواتير متعددة** | العمل على عدة فواتير في نفس الوقت | 🔥 |
| **المشتريات** | إدارة فواتير الشراء والموردين | 🛒 |
| **المخزون** | إدارة المنتجات والكميات | 📦 |
| **المرتجعات** | إدارة مرتجعات البيع والشراء | 🔄 |
| **العملاء** | إدارة بيانات العملاء والموردين | 👥 |
| **المصاريف** | تسجيل وتتبع المصاريف | 💸 |
| **الإدارة المالية** | إدارة رأس المال والأرباح | 💼 |
| **البنك والتحويلات** | إدارة الحسابات البنكية | 🏦 |
| **التقارير** | تقارير شاملة ورسوم بيانية | 📊 |
| **الإعدادات** | إعدادات النظام والمستخدمين | ⚙️ |

## 🎯 أزرار الوصول السريع

| الزر | الوظيفة | اللون |
|-----|---------|-------|
| **فاتورة بيع جديدة** | فتح نافذة بيع جديدة مباشرة | 🟢 أخضر |
| **فاتورة شراء جديدة** | فتح نافذة شراء جديدة مباشرة | 🟣 بنفسجي |
| **إضافة منتج جديد** | فتح نافذة المنتجات لإضافة منتج | 🔵 أزرق |
| **إضافة عميل جديد** | فتح نافذة العملاء لإضافة عميل | 🟢 أخضر فاتح |

## 📊 البطاقات الإحصائية

تعرض الشاشة الرئيسية إحصائيات حية من قاعدة البيانات:

- 📈 **إجمالي المبيعات** - مجموع قيم جميع فواتير البيع
- 📦 **عدد المنتجات** - إجمالي المنتجات في المخزون
- ⚠️ **تنبيهات المخزون** - المنتجات التي كميتها أقل من 10
- 👥 **إجمالي العملاء** - عدد العملاء المسجلين

## 🔐 تسجيل الدخول الافتراضي

- **اسم المستخدم:** admin
- **كلمة المرور:** admin

## 🆘 حل المشاكل الشائعة

### المشكلة: التطبيق لا يفتح
**الحل:**
1. تأكد من تثبيت Python
2. تثبيت PyQt5: `pip install PyQt5`
3. تشغيل من سطر الأوامر لرؤية الأخطاء

### المشكلة: خطأ في قاعدة البيانات
**الحل:**
1. احذف ملف `business_system.db`
2. أعد تشغيل التطبيق

### المشكلة: واجهة غير واضحة
**الحل:**
1. تأكد من دعم النظام للغة العربية
2. تحديث برامج تشغيل الشاشة

## 📞 الدعم الفني

شركة الطيب للتجارة والتوزيع
- 📱 ***********
- 📱 ***********

## 📝 ملاحظات

- التطبيق يدعم اللغة العربية بالكامل
- يتم حفظ البيانات محلياً في قاعدة بيانات SQLite
- يمكن إنشاء نسخ احتياطية من البيانات
- التطبيق يعمل على Windows بشكل أساسي
