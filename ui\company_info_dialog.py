#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة بيانات الشركة - نظام نقاط البيع والمحاسبة
Company Information Dialog - POS and Accounting System
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                           QPushButton, QLineEdit, QTextEdit, QFormLayout,
                           QMessageBox, QGroupBox, QFileDialog, QTabWidget,
                           QWidget, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap
import os

class CompanyInfoDialog(QDialog):
    """نافذة حوار بيانات الشركة"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.company_data = None
        self.init_ui()
        self.load_company_data()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("بيانات الشركة")
        self.setFixedSize(650, 750)
        
        # تطبيق التصميم العصري
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 14px;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #2c3e50;
                font-size: 14px;
                font-weight: bold;
            }
            QLabel {
                color: #2c3e50;
                font-size: 13px;
                font-weight: normal;
            }
            QFormLayout QLabel {
                color: #2c3e50;
                font-size: 13px;
                font-weight: 600;
                padding-right: 5px;
            }
            QLineEdit, QTextEdit {
                background-color: white;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                padding: 10px;
                font-size: 14px;
                font-weight: normal;
                color: #2c3e50;
                min-height: 25px;
                selection-background-color: #007bff;
                selection-color: white;
            }
            QLineEdit:focus, QTextEdit:focus {
                border-color: #007bff;
                outline: none;
                background-color: #f8f9fa;
            }
            QLineEdit::placeholder, QTextEdit::placeholder {
                color: #6c757d;
                font-style: italic;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007bff, stop:1 #0056b3);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 13px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0056b3, stop:1 #004085);
            }
            QPushButton:pressed {
                background: #004085;
            }
            QPushButton#selectLogoBtn {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #1e7e34);
            }
            QPushButton#selectLogoBtn:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1e7e34, stop:1 #155724);
            }
            QPushButton#cancelBtn {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6c757d, stop:1 #495057);
            }
            QPushButton#cancelBtn:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #495057, stop:1 #343a40);
            }
        """)
        
        layout = QVBoxLayout()
        
        # عنوان النافذة
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #007bff, stop:1 #0056b3);
                border-radius: 8px;
                padding: 10px;
                margin-bottom: 10px;
            }
        """)
        title_layout = QHBoxLayout(title_frame)
        
        title_label = QLabel("🏢 بيانات الشركة")
        title_label.setStyleSheet("""
            color: white;
            font-size: 18px;
            font-weight: bold;
            margin: 0;
        """)
        title_layout.addWidget(title_label)
        
        layout.addWidget(title_frame)
        
        # التبويبات
        tabs = QTabWidget()
        tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 1px solid #dee2e6;
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 8px 16px;
                margin-right: 2px;
                font-weight: bold;
                color: #495057;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007bff, stop:1 #0056b3);
                color: white;
            }
        """)
        
        # تبويب المعلومات الأساسية
        basic_tab = QWidget()
        self.setup_basic_info_tab(basic_tab)
        tabs.addTab(basic_tab, "المعلومات الأساسية")
        
        # تبويب معلومات الاتصال
        contact_tab = QWidget()
        self.setup_contact_info_tab(contact_tab)
        tabs.addTab(contact_tab, "معلومات الاتصال")
        
        # تبويب المعلومات البنكية
        banking_tab = QWidget()
        self.setup_banking_info_tab(banking_tab)
        tabs.addTab(banking_tab, "المعلومات البنكية")
        
        layout.addWidget(tabs)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("حفظ البيانات")
        save_btn.clicked.connect(self.save_data)
        buttons_layout.addWidget(save_btn)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setObjectName("cancelBtn")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
        
    def setup_basic_info_tab(self, tab):
        """إعداد تبويب المعلومات الأساسية"""
        layout = QVBoxLayout()
        
        # معلومات الشركة
        company_group = QGroupBox("معلومات الشركة")
        company_layout = QFormLayout()
        
        self.company_name_input = QLineEdit()
        self.company_name_input.setPlaceholderText("أدخل اسم الشركة بالعربية")
        self.company_name_input.setFont(self.company_name_input.font())
        company_layout.addRow("اسم الشركة:", self.company_name_input)
        
        self.company_arabic_name_input = QLineEdit()
        self.company_arabic_name_input.setPlaceholderText("أدخل اسم الشركة بالإنجليزية")
        self.company_arabic_name_input.setFont(self.company_arabic_name_input.font())
        company_layout.addRow("الاسم الإنجليزي:", self.company_arabic_name_input)
        
        self.commercial_registration_input = QLineEdit()
        self.commercial_registration_input.setPlaceholderText("رقم السجل التجاري")
        company_layout.addRow("السجل التجاري:", self.commercial_registration_input)
        
        self.tax_id_input = QLineEdit()
        self.tax_id_input.setPlaceholderText("الرقم الضريبي")
        company_layout.addRow("الرقم الضريبي:", self.tax_id_input)
        
        self.currency_input = QLineEdit()
        self.currency_input.setPlaceholderText("العملة الافتراضية")
        self.currency_input.setText("جنيه")
        company_layout.addRow("العملة:", self.currency_input)
        
        company_group.setLayout(company_layout)
        layout.addWidget(company_group)
        
        # العنوان
        address_group = QGroupBox("العنوان")
        address_layout = QVBoxLayout()
        
        self.address_input = QTextEdit()
        self.address_input.setPlaceholderText("العنوان التفصيلي للشركة")
        self.address_input.setMaximumHeight(100)
        self.address_input.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                padding: 8px;
                font-size: 14px;
                font-weight: normal;
                color: #2c3e50;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border-color: #007bff;
                background-color: #f8f9fa;
            }
        """)
        address_layout.addWidget(self.address_input)
        
        address_group.setLayout(address_layout)
        layout.addWidget(address_group)
        
        # الشعار
        logo_group = QGroupBox("شعار الشركة")
        logo_layout = QVBoxLayout()
        
        # منطقة عرض الشعار
        self.logo_label = QLabel()
        self.logo_label.setAlignment(Qt.AlignCenter)
        self.logo_label.setStyleSheet("""
            QLabel {
                border: 2px dashed #dee2e6;
                border-radius: 8px;
                padding: 20px;
                background-color: #f8f9fa;
                color: #6c757d;
                font-size: 14px;
            }
        """)
        self.logo_label.setText("لا يوجد شعار مُحدد\nاضغط على الزر أدناه لاختيار الشعار")
        self.logo_label.setMinimumHeight(120)
        logo_layout.addWidget(self.logo_label)
        
        # زر اختيار الشعار
        select_logo_btn = QPushButton("اختيار الشعار")
        select_logo_btn.setObjectName("selectLogoBtn")
        select_logo_btn.clicked.connect(self.select_logo)
        logo_layout.addWidget(select_logo_btn)
        
        logo_group.setLayout(logo_layout)
        layout.addWidget(logo_group)
        
        layout.addStretch()
        tab.setLayout(layout)
        
    def setup_contact_info_tab(self, tab):
        """إعداد تبويب معلومات الاتصال"""
        layout = QVBoxLayout()
        
        # معلومات الاتصال
        contact_group = QGroupBox("معلومات الاتصال")
        contact_layout = QFormLayout()
        
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("رقم الهاتف الثابت")
        contact_layout.addRow("الهاتف:", self.phone_input)
        
        self.mobile_input = QLineEdit()
        self.mobile_input.setPlaceholderText("رقم الجوال")
        contact_layout.addRow("الجوال:", self.mobile_input)
        
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("البريد الإلكتروني")
        contact_layout.addRow("البريد الإلكتروني:", self.email_input)
        
        self.website_input = QLineEdit()
        self.website_input.setPlaceholderText("الموقع الإلكتروني")
        contact_layout.addRow("الموقع الإلكتروني:", self.website_input)
        
        contact_group.setLayout(contact_layout)
        layout.addWidget(contact_group)
        
        # معلومات المدير
        manager_group = QGroupBox("معلومات المدير")
        manager_layout = QFormLayout()
        
        self.manager_name_input = QLineEdit()
        self.manager_name_input.setPlaceholderText("اسم المدير")
        manager_layout.addRow("اسم المدير:", self.manager_name_input)
        
        self.manager_phone_input = QLineEdit()
        self.manager_phone_input.setPlaceholderText("هاتف المدير")
        manager_layout.addRow("هاتف المدير:", self.manager_phone_input)
        
        self.manager_email_input = QLineEdit()
        self.manager_email_input.setPlaceholderText("بريد المدير الإلكتروني")
        manager_layout.addRow("بريد المدير:", self.manager_email_input)
        
        manager_group.setLayout(manager_layout)
        layout.addWidget(manager_group)
        
        layout.addStretch()
        tab.setLayout(layout)
        
    def setup_banking_info_tab(self, tab):
        """إعداد تبويب المعلومات البنكية"""
        layout = QVBoxLayout()
        
        # معلومات البنك
        banking_group = QGroupBox("المعلومات البنكية")
        banking_layout = QFormLayout()
        
        self.bank_name_input = QLineEdit()
        self.bank_name_input.setPlaceholderText("اسم البنك")
        banking_layout.addRow("اسم البنك:", self.bank_name_input)
        
        self.bank_account_input = QLineEdit()
        self.bank_account_input.setPlaceholderText("رقم الحساب")
        banking_layout.addRow("رقم الحساب:", self.bank_account_input)
        
        self.iban_input = QLineEdit()
        self.iban_input.setPlaceholderText("رقم الآيبان IBAN")
        banking_layout.addRow("رقم الآيبان:", self.iban_input)
        
        self.swift_code_input = QLineEdit()
        self.swift_code_input.setPlaceholderText("رمز السويفت SWIFT")
        banking_layout.addRow("رمز السويفت:", self.swift_code_input)
        
        banking_group.setLayout(banking_layout)
        layout.addWidget(banking_group)
        
        # ملاحظة
        note_label = QLabel("""
        <div style='background-color: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 10px;'>
            <b style='color: #0d47a1;'>💡 ملاحظة:</b><br>
            <span style='color: #1565c0;'>
            هذه المعلومات ستظهر في الفواتير والتقارير المالية.
            تأكد من صحة البيانات قبل الحفظ.
            </span>
        </div>
        """)
        note_label.setWordWrap(True)
        layout.addWidget(note_label)
        
        layout.addStretch()
        tab.setLayout(layout)
        
    def select_logo(self):
        """اختيار شعار الشركة"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختيار شعار الشركة", "",
            "Image files (*.png *.jpg *.jpeg *.gif *.bmp);;All files (*.*)"
        )
        
        if file_path:
            # نسخ الملف إلى مجلد الأصول
            try:
                import shutil
                assets_dir = "assets"
                if not os.path.exists(assets_dir):
                    os.makedirs(assets_dir)
                
                file_name = os.path.basename(file_path)
                new_path = os.path.join(assets_dir, f"company_logo_{file_name}")
                shutil.copy(file_path, new_path)
                
                # عرض الشعار
                pixmap = QPixmap(new_path)
                if not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(200, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    self.logo_label.setPixmap(scaled_pixmap)
                    self.logo_label.setText("")
                    self.logo_path = new_path
                else:
                    QMessageBox.warning(self, "خطأ", "لا يمكن قراءة ملف الصورة")
                    
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الشعار:\n{str(e)}")
                
    def load_company_data(self):
        """تحميل بيانات الشركة"""
        try:
            self.company_data = self.db_manager.get_company_info()
            print(f"البيانات المسترجعة: {self.company_data}")
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل البيانات:\n{str(e)}")
            self.company_data = None
        
        if self.company_data:
            # المعلومات الأساسية
            self.company_name_input.setText(self.company_data['company_name'] or '')
            self.company_arabic_name_input.setText(self.company_data['company_arabic_name'] or '')
            self.commercial_registration_input.setText(self.company_data['commercial_registration'] or '')
            self.tax_id_input.setText(self.company_data['tax_id'] or '')
            self.currency_input.setText(self.company_data['currency'] or 'جنيه')
            self.address_input.setText(self.company_data['address'] or '')
            
            # معلومات الاتصال
            self.phone_input.setText(self.company_data['phone'] or '')
            self.mobile_input.setText(self.company_data['mobile'] or '')
            self.email_input.setText(self.company_data['email'] or '')
            self.website_input.setText(self.company_data['website'] or '')
            
            # معلومات المدير
            self.manager_name_input.setText(self.company_data['manager_name'] or '')
            self.manager_phone_input.setText(self.company_data['manager_phone'] or '')
            self.manager_email_input.setText(self.company_data['manager_email'] or '')
            
            # المعلومات البنكية
            self.bank_name_input.setText(self.company_data['bank_name'] or '')
            self.bank_account_input.setText(self.company_data['bank_account'] or '')
            self.iban_input.setText(self.company_data['iban'] or '')
            self.swift_code_input.setText(self.company_data['swift_code'] or '')
            
            # الشعار
            if self.company_data['logo_path'] and os.path.exists(self.company_data['logo_path']):
                pixmap = QPixmap(self.company_data['logo_path'])
                if not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(200, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    self.logo_label.setPixmap(scaled_pixmap)
                    self.logo_label.setText("")
                    self.logo_path = self.company_data['logo_path']
                    
    def save_data(self):
        """حفظ البيانات"""
        if not self.company_name_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم الشركة")
            return
            
        # جمع البيانات
        company_data = {
            'company_name': self.company_name_input.text().strip(),
            'company_arabic_name': self.company_arabic_name_input.text().strip(),
            'commercial_registration': self.commercial_registration_input.text().strip(),
            'tax_id': self.tax_id_input.text().strip(),
            'address': self.address_input.toPlainText().strip(),
            'phone': self.phone_input.text().strip(),
            'mobile': self.mobile_input.text().strip(),
            'email': self.email_input.text().strip(),
            'website': self.website_input.text().strip(),
            'logo_path': getattr(self, 'logo_path', ''),
            'manager_name': self.manager_name_input.text().strip(),
            'manager_phone': self.manager_phone_input.text().strip(),
            'manager_email': self.manager_email_input.text().strip(),
            'bank_name': self.bank_name_input.text().strip(),
            'bank_account': self.bank_account_input.text().strip(),
            'iban': self.iban_input.text().strip(),
            'swift_code': self.swift_code_input.text().strip(),
            'currency': self.currency_input.text().strip() or 'جنيه'
        }
        
        # حفظ البيانات
        try:
            print(f"البيانات المراد حفظها: {company_data}")
            result = self.db_manager.save_company_info(company_data)
            print(f"نتيجة الحفظ: {result}")
            
            if result:
                QMessageBox.information(self, "نجح", "تم حفظ بيانات الشركة بنجاح")
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حفظ بيانات الشركة")
        except Exception as e:
            print(f"خطأ في حفظ البيانات: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ البيانات:\n{str(e)}")
