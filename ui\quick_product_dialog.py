#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة حوار سريعة لإضافة منتج جديد - نظام نقاط البيع والمحاسبة
Quick Product Dialog - POS and Accounting System
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QLineEdit, QComboBox, QFormLayout, 
                            QMessageBox, QGroupBox, QDoubleSpinBox, QSpinBox,
                            QTextEdit, QCheckBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor

class QuickProductDialog(QDialog):
    """نافذة حوار سريعة لإضافة منتج جديد من الفاتورة"""
    
    def __init__(self, db_manager, suggested_name="", parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.suggested_name = suggested_name
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إضافة منتج جديد - سريع")
        self.setFixedSize(450, 500)
        
        layout = QVBoxLayout()
        
        # رسالة توضيحية
        info_label = QLabel("إضافة منتج جديد بسرعة للفاتورة")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("font-size: 14px; font-weight: bold; color: blue; margin: 10px;")
        layout.addWidget(info_label)
        
        # معلومات أساسية
        basic_group = QGroupBox("المعلومات الأساسية")
        basic_layout = QFormLayout()
        
        self.name_input = QLineEdit()
        self.name_input.setText(self.suggested_name)
        self.name_input.setPlaceholderText("اسم المنتج")
        basic_layout.addRow("اسم المنتج *:", self.name_input)
        
        self.barcode_input = QLineEdit()
        self.barcode_input.setPlaceholderText("الباركود (اختياري)")
        basic_layout.addRow("الباركود:", self.barcode_input)
        
        self.unit_input = QLineEdit()
        self.unit_input.setText("قطعة")
        basic_layout.addRow("الوحدة:", self.unit_input)
        
        basic_group.setLayout(basic_layout)
        layout.addWidget(basic_group)
        
        # الأسعار
        prices_group = QGroupBox("الأسعار")
        prices_layout = QFormLayout()
        
        self.purchase_price_input = QDoubleSpinBox()
        self.purchase_price_input.setMaximum(999999.99)
        self.purchase_price_input.setDecimals(2)
        self.purchase_price_input.setSuffix(" جنيه")
        self.purchase_price_input.valueChanged.connect(self.calculate_suggested_prices)
        prices_layout.addRow("سعر الشراء *:", self.purchase_price_input)
        
        self.wholesale_price_input = QDoubleSpinBox()
        self.wholesale_price_input.setMaximum(999999.99)
        self.wholesale_price_input.setDecimals(2)
        self.wholesale_price_input.setSuffix(" جنيه")
        prices_layout.addRow("سعر الجملة *:", self.wholesale_price_input)
        
        self.semi_wholesale_price_input = QDoubleSpinBox()
        self.semi_wholesale_price_input.setMaximum(999999.99)
        self.semi_wholesale_price_input.setDecimals(2)
        self.semi_wholesale_price_input.setSuffix(" جنيه")
        prices_layout.addRow("سعر نص الجملة *:", self.semi_wholesale_price_input)
        
        self.retail_price_input = QDoubleSpinBox()
        self.retail_price_input.setMaximum(999999.99)
        self.retail_price_input.setDecimals(2)
        self.retail_price_input.setSuffix(" جنيه")
        prices_layout.addRow("سعر القطاعي *:", self.retail_price_input)
        
        # خانة اختيار لحساب الأسعار تلقائياً
        self.auto_calculate_check = QCheckBox("حساب الأسعار تلقائياً (هامش ربح 20%)")
        self.auto_calculate_check.setChecked(True)
        self.auto_calculate_check.toggled.connect(self.toggle_auto_calculate)
        prices_layout.addRow("", self.auto_calculate_check)
        
        prices_group.setLayout(prices_layout)
        layout.addWidget(prices_group)
        
        # المخزون الأولي
        stock_group = QGroupBox("المخزون الأولي")
        stock_layout = QFormLayout()
        
        self.initial_stock_input = QSpinBox()
        self.initial_stock_input.setMaximum(999999)
        self.initial_stock_input.setValue(0)
        stock_layout.addRow("الكمية الأولية:", self.initial_stock_input)
        
        self.min_stock_input = QSpinBox()
        self.min_stock_input.setMaximum(999999)
        self.min_stock_input.setValue(5)
        stock_layout.addRow("الحد الأدنى للتنبيه:", self.min_stock_input)
        
        stock_group.setLayout(stock_layout)
        layout.addWidget(stock_group)
        
        # ملاحظة
        note_label = QLabel("💡 نصيحة: يمكنك تعديل تفاصيل المنتج لاحقاً من إدارة المنتجات")
        note_label.setStyleSheet("color: #666; font-style: italic; margin: 5px;")
        note_label.setWordWrap(True)
        layout.addWidget(note_label)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("حفظ وإضافة للفاتورة")
        save_btn.setStyleSheet("background-color: #28a745; color: white; font-weight: bold; padding: 8px;")
        save_btn.clicked.connect(self.save_product)
        buttons_layout.addWidget(save_btn)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
        
        # تركيز على حقل الاسم
        self.name_input.setFocus()
        
    def calculate_suggested_prices(self):
        """حساب الأسعار المقترحة تلقائياً"""
        if not self.auto_calculate_check.isChecked():
            return
            
        purchase_price = self.purchase_price_input.value()
        if purchase_price > 0:
            # حساب الأسعار بهامش ربح متدرج
            wholesale_price = purchase_price * 1.15  # هامش ربح 15% للجملة
            semi_wholesale_price = purchase_price * 1.20  # هامش ربح 20% لنص الجملة
            retail_price = purchase_price * 1.25  # هامش ربح 25% للقطاعي
            
            self.wholesale_price_input.setValue(wholesale_price)
            self.semi_wholesale_price_input.setValue(semi_wholesale_price)
            self.retail_price_input.setValue(retail_price)
            
    def toggle_auto_calculate(self, checked):
        """تفعيل/إلغاء تفعيل الحساب التلقائي للأسعار"""
        if checked:
            self.calculate_suggested_prices()
            
    def save_product(self):
        """حفظ المنتج الجديد"""
        # التحقق من البيانات المطلوبة
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المنتج")
            self.name_input.setFocus()
            return
            
        if self.purchase_price_input.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال سعر شراء صحيح")
            self.purchase_price_input.setFocus()
            return
            
        if self.retail_price_input.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال سعر بيع صحيح")
            self.retail_price_input.setFocus()
            return
            
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            # إدراج المنتج الجديد
            cursor.execute('''
                INSERT INTO products 
                (name, barcode, unit, purchase_price, wholesale_price, 
                 semi_wholesale_price, retail_price, current_stock, min_stock_alert)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                self.name_input.text().strip(),
                self.barcode_input.text().strip() or None,
                self.unit_input.text().strip(),
                self.purchase_price_input.value(),
                self.wholesale_price_input.value(),
                self.semi_wholesale_price_input.value(),
                self.retail_price_input.value(),
                self.initial_stock_input.value(),
                self.min_stock_input.value()
            ))
            
            product_id = cursor.lastrowid
            
            # إذا كان هناك مخزون أولي، سجل حركة مخزون
            if self.initial_stock_input.value() > 0:
                cursor.execute('''
                    INSERT INTO stock_movements 
                    (product_id, movement_type, quantity, reference_type, notes, user_id)
                    VALUES (?, 'in', ?, 'adjustment', 'مخزون أولي', 1)
                ''', (product_id, self.initial_stock_input.value()))
                
            conn.commit()
            conn.close()
            
            # حفظ معرف المنتج الجديد للاستخدام في الفاتورة
            self.new_product_id = product_id
            
            QMessageBox.information(self, "نجح", 
                                  f"تم إضافة المنتج '{self.name_input.text()}' بنجاح!\n"
                                  "سيتم إضافته للفاتورة تلقائياً.")
            self.accept()
            
        except Exception as e:
            conn.close()
            if "UNIQUE constraint failed" in str(e):
                QMessageBox.critical(self, "خطأ", "الباركود موجود بالفعل، يرجى استخدام باركود مختلف")
            else:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ المنتج:\n{str(e)}")
                
    def get_new_product_id(self):
        """الحصول على معرف المنتج الجديد"""
        return getattr(self, 'new_product_id', None)
