"""
نافذة فاتورة الشراء
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QComboBox, QMessageBox, QHeaderView,
                            QGroupBox, QSpinBox, QDoubleSpinBox, QDateEdit,
                            QGridLayout, QFrame, QAbstractItemView)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont
from datetime import datetime
import uuid

class PurchaseInvoiceWindow(QWidget):
    def __init__(self, db_manager, user_data, invoice_id=None):
        super().__init__()
        print("🔄 بدء إنشاء نافذة فاتورة الشراء...")

        self.db_manager = db_manager
        self.user_data = user_data
        self.invoice_id = invoice_id
        self.invoice_items = []

        try:
            print("🔄 إعداد واجهة المستخدم...")
            self.init_ui()
            print("✅ تم إعداد واجهة المستخدم بنجاح")

            if self.invoice_id:
                print("🔄 تحميل بيانات الفاتورة...")
                self.load_invoice_data()
            else:
                print("🔄 توليد رقم فاتورة جديد...")
                self.generate_invoice_number()

            print("✅ تم إنشاء نافذة فاتورة الشراء بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء نافذة فاتورة الشراء: {str(e)}")
            import traceback
            traceback.print_exc()
            raise e
            
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        try:
            print("🔄 إعداد النافذة الأساسية...")
            title = "عرض فاتورة الشراء" if self.invoice_id else "فاتورة شراء جديدة"
            self.setWindowTitle(title)
            self.setGeometry(150, 150, 1200, 800)

            # تطبيق نفس التصميم المستخدم في فاتورة البيع
            self.setStyleSheet("""
                QWidget {
                    background-color: #c8e6c9;
                    font-family: 'Arial';
                    font-size: 11px;
                }
                QGroupBox {
                    font-weight: bold;
                    border: 1px solid #81c784;
                    border-radius: 3px;
                    margin: 2px;
                    padding-top: 8px;
                    background-color: #e8f5e8;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 8px;
                    padding: 0 3px 0 3px;
                    color: #2e7d32;
                    font-size: 12px;
                    font-weight: bold;
                }
                QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
                    border: 1px solid #a5d6a7;
                    border-radius: 2px;
                    padding: 3px;
                    background-color: white;
                    font-size: 11px;
                }
                QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
                    border: 2px solid #66bb6a;
                }
                QPushButton {
                    background-color: #66bb6a;
                    color: white;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 3px;
                    font-weight: bold;
                    font-size: 11px;
                }
                QPushButton:hover {
                    background-color: #4caf50;
                }
                QPushButton:pressed {
                    background-color: #388e3c;
                }
                QTableWidget {
                    gridline-color: #a5d6a7;
                    background-color: white;
                    alternate-background-color: #f1f8e9;
                    border: 1px solid #81c784;
                    border-radius: 3px;
                    font-size: 11px;
                }
                QTableWidget::item {
                    padding: 6px;
                    border-bottom: 1px solid #c8e6c9;
                }
                QHeaderView::section {
                    background-color: #81c784;
                    color: white;
                    padding: 8px;
                    border: none;
                    font-weight: bold;
                    font-size: 11px;
                }
                QLabel {
                    color: #2e7d32;
                    font-size: 11px;
                }
            """)

            main_layout = QVBoxLayout()
            main_layout.setSpacing(10)
            main_layout.setContentsMargins(15, 15, 15, 15)

            # معلومات الفاتورة - تصميم مطابق لفاتورة البيع
            header_group = QGroupBox("معلومات الفاتورة")
            header_layout = QGridLayout()
            header_layout.setSpacing(8)
            header_layout.setContentsMargins(10, 15, 10, 10)

            # الصف الأول - رقم الفاتورة والتاريخ
            invoice_num_label = QLabel("رقم الفاتورة:")
            invoice_num_label.setStyleSheet("font-weight: bold;")
            header_layout.addWidget(invoice_num_label, 0, 0)

            self.invoice_number_label = QLabel("سيتم إنشاؤه تلقائياً")
            self.invoice_number_label.setStyleSheet("""
                background-color: #fff3e0;
                padding: 4px 8px;
                border: 1px solid #ffcc02;
                border-radius: 2px;
                font-weight: bold;
                color: #e65100;
            """)
            header_layout.addWidget(self.invoice_number_label, 0, 1)

            date_label = QLabel("التاريخ:")
            date_label.setStyleSheet("font-weight: bold;")
            header_layout.addWidget(date_label, 0, 2)

            self.invoice_date = QDateEdit()
            self.invoice_date.setDate(QDate.currentDate())
            self.invoice_date.setEnabled(not self.invoice_id)
            self.invoice_date.setStyleSheet("min-width: 100px;")
            header_layout.addWidget(self.invoice_date, 0, 3)

            # الصف الثاني - المورد ونوع السعر
            supplier_label = QLabel("المورد:")
            supplier_label.setStyleSheet("font-weight: bold;")
            header_layout.addWidget(supplier_label, 1, 0)

            self.supplier_combo = QComboBox()
            self.supplier_combo.setEnabled(not self.invoice_id)
            self.supplier_combo.setStyleSheet("""
                QComboBox {
                    min-width: 180px;
                    min-height: 30px;
                    font-size: 12px;
                    padding: 5px;
                    padding-left: 25px;
                    border: 1px solid #ccc;
                    border-radius: 3px;
                }
                QComboBox::drop-down {
                    subcontrol-origin: padding;
                    subcontrol-position: left center;
                    width: 25px;
                    border: none;
                    background: transparent;
                }
                QComboBox::down-arrow {
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzMzMzMzMyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
                    width: 12px;
                    height: 8px;
                }
                QComboBox::down-arrow:hover {
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzRDQUY1MCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
                }
            """)
            print("🔄 تحميل الموردين...")
            self.load_suppliers()
            header_layout.addWidget(self.supplier_combo, 1, 1)

            price_type_label = QLabel("نوع السعر:")
            price_type_label.setStyleSheet("font-weight: bold;")
            header_layout.addWidget(price_type_label, 1, 2)

            self.price_type_combo = QComboBox()
            self.price_type_combo.addItems(["سعر شراء", "سعر خاص", "سعر الجملة"])
            self.price_type_combo.setEnabled(not self.invoice_id)
            self.price_type_combo.setStyleSheet("""
                QComboBox {
                    min-width: 100px;
                    min-height: 30px;
                    font-size: 12px;
                    padding: 5px;
                    padding-left: 25px;
                    border: 1px solid #ccc;
                    border-radius: 3px;
                }
                QComboBox::drop-down {
                    subcontrol-origin: padding;
                    subcontrol-position: left center;
                    width: 25px;
                    border: none;
                    background: transparent;
                }
                QComboBox::down-arrow {
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzMzMzMzMyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
                    width: 12px;
                    height: 8px;
                }
                QComboBox::down-arrow:hover {
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzRDQUY1MCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
                }
            """)
            header_layout.addWidget(self.price_type_combo, 1, 3)

            # إضافة مساحة فارغة
            header_layout.setColumnStretch(4, 1)

            header_group.setLayout(header_layout)
            main_layout.addWidget(header_group)

            # أصناف الفاتورة - تصميم مطابق لفاتورة البيع
            items_group = QGroupBox("أصناف الفاتورة")
            items_layout = QVBoxLayout()

            # شريط إضافة صنف - تصميم مبسط مثل فاتورة البيع
            if not self.invoice_id:
                add_item_layout = QHBoxLayout()
                add_item_layout.setSpacing(5)

                # المنتج
                product_label = QLabel("المنتج:")
                product_label.setStyleSheet("font-weight: bold; min-width: 50px;")
                add_item_layout.addWidget(product_label)

                self.product_combo = QComboBox()
                self.product_combo.setEnabled(not self.invoice_id)
                self.product_combo.setEditable(True)
                self.product_combo.setInsertPolicy(QComboBox.NoInsert)
                self.product_combo.setStyleSheet("""
                    QComboBox {
                        min-width: 200px;
                        min-height: 30px;
                        font-size: 12px;
                        padding: 5px;
                        padding-left: 25px;
                        border: 1px solid #ccc;
                        border-radius: 3px;
                    }
                    QComboBox::drop-down {
                        subcontrol-origin: padding;
                        subcontrol-position: left center;
                        width: 25px;
                        border: none;
                        background: transparent;
                    }
                    QComboBox::down-arrow {
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzMzMzMzMyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
                        width: 12px;
                        height: 8px;
                    }
                    QComboBox::down-arrow:hover {
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzRDQUY1MCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
                    }
                """)
                print("🔄 تحميل المنتجات...")
                self.load_products()
                add_item_layout.addWidget(self.product_combo)

                # زر منتج جديد
                add_product_btn = QPushButton("منتج جديد")
                add_product_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #ff9800;
                        color: white;
                        font-weight: bold;
                        padding: 4px 8px;
                        min-width: 80px;
                    }
                    QPushButton:hover {
                        background-color: #f57c00;
                    }
                """)
                add_product_btn.clicked.connect(self.add_new_product)
                add_item_layout.addWidget(add_product_btn)

                # الكمية
                quantity_label = QLabel("الكمية:")
                quantity_label.setStyleSheet("font-weight: bold; min-width: 40px;")
                add_item_layout.addWidget(quantity_label)

                self.quantity_input = QSpinBox()
                self.quantity_input.setMinimum(1)
                self.quantity_input.setMaximum(9999)
                self.quantity_input.setValue(1)
                self.quantity_input.setEnabled(not self.invoice_id)
                self.quantity_input.setStyleSheet("min-width: 60px;")
                add_item_layout.addWidget(self.quantity_input)

                # السعر
                price_label = QLabel("السعر:")
                price_label.setStyleSheet("font-weight: bold; min-width: 40px;")
                add_item_layout.addWidget(price_label)

                self.price_input = QDoubleSpinBox()
                self.price_input.setMaximum(999999.99)
                self.price_input.setDecimals(2)
                self.price_input.setEnabled(not self.invoice_id)
                self.price_input.setStyleSheet("min-width: 80px;")
                add_item_layout.addWidget(self.price_input)

                # زر الإضافة
                add_item_btn = QPushButton("إضافة")
                add_item_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #4caf50;
                        color: white;
                        font-weight: bold;
                        padding: 4px 12px;
                        min-width: 60px;
                    }
                    QPushButton:hover {
                        background-color: #45a049;
                    }
                """)
                add_item_btn.clicked.connect(self.add_item)
                add_item_layout.addWidget(add_item_btn)

                add_item_layout.addStretch()
                items_layout.addLayout(add_item_layout)

            # جدول الأصناف - تصميم مطابق لفاتورة البيع
            self.items_table = QTableWidget()
            self.items_table.setColumnCount(5)
            self.items_table.setHorizontalHeaderLabels([
                "اسم الصنف", "الكمية", "السعر", "الإجمالي", "حذف"
            ])

            # تنسيق الجدول مطابق لفاتورة البيع
            header = self.items_table.horizontalHeader()
            header.setStretchLastSection(False)
            header.setSectionResizeMode(0, QHeaderView.Stretch)  # عمود المنتج قابل للتمدد
            header.setSectionResizeMode(1, QHeaderView.Fixed)    # عمود الكمية ثابت
            header.setSectionResizeMode(2, QHeaderView.Fixed)    # عمود السعر ثابت
            header.setSectionResizeMode(3, QHeaderView.Fixed)    # عمود الإجمالي ثابت
            header.setSectionResizeMode(4, QHeaderView.Fixed)    # عمود الحذف ثابت

            # تحديد عرض الأعمدة مطابق لفاتورة البيع
            self.items_table.setColumnWidth(1, 70)   # الكمية
            self.items_table.setColumnWidth(2, 90)   # السعر
            self.items_table.setColumnWidth(3, 100)  # الإجمالي
            self.items_table.setColumnWidth(4, 60)   # حذف

            self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
            self.items_table.setAlternatingRowColors(True)

            # تحديد ارتفاع الصفوف
            self.items_table.verticalHeader().setDefaultSectionSize(25)
            self.items_table.verticalHeader().setVisible(False)

            items_layout.addWidget(self.items_table)
            items_group.setLayout(items_layout)
            main_layout.addWidget(items_group)

            print("✅ تم إنشاء قسم الأصناف")

            # إجماليات الفاتورة - تصميم مطابق لفاتورة البيع
            totals_frame = QFrame()
            totals_frame.setStyleSheet("""
                QFrame {
                    background-color: #e8f5e8;
                    border: 1px solid #81c784;
                    border-radius: 3px;
                    margin: 2px;
                }
            """)
            totals_layout = QHBoxLayout()
            totals_layout.setSpacing(15)
            totals_layout.setContentsMargins(10, 8, 10, 8)

            # المجموع الفرعي
            subtotal_label = QLabel("المجموع الفرعي:")
            subtotal_label.setStyleSheet("font-weight: bold;")
            totals_layout.addWidget(subtotal_label)

            self.subtotal_label = QLabel("0.00")
            self.subtotal_label.setStyleSheet("""
                background-color: white;
                padding: 3px 8px;
                border: 1px solid #a5d6a7;
                border-radius: 2px;
                font-weight: bold;
                min-width: 80px;
            """)
            totals_layout.addWidget(self.subtotal_label)

            # الخصم
            discount_label = QLabel("الخصم:")
            discount_label.setStyleSheet("font-weight: bold;")
            totals_layout.addWidget(discount_label)

            self.discount_input = QDoubleSpinBox()
            self.discount_input.setMaximum(999999.99)
            self.discount_input.setDecimals(2)
            self.discount_input.setEnabled(not self.invoice_id)
            self.discount_input.valueChanged.connect(self.calculate_totals)
            self.discount_input.setStyleSheet("min-width: 80px;")
            totals_layout.addWidget(self.discount_input)

            # الإجمالي النهائي
            total_label = QLabel("الإجمالي النهائي:")
            total_label.setStyleSheet("font-weight: bold;")
            totals_layout.addWidget(total_label)

            self.total_label = QLabel("0.00")
            self.total_label.setStyleSheet("""
                background-color: #fff3e0;
                padding: 3px 8px;
                border: 1px solid #ffcc02;
                border-radius: 2px;
                font-weight: bold;
                color: #e65100;
                min-width: 80px;
            """)
            totals_layout.addWidget(self.total_label)

            totals_layout.addStretch()
            totals_frame.setLayout(totals_layout)
            main_layout.addWidget(totals_frame)

            # طريقة الدفع - تصميم مطابق لفاتورة البيع
            if not self.invoice_id:
                payment_group = QGroupBox("طريقة الدفع")
                payment_layout = QHBoxLayout()
                payment_layout.setSpacing(10)
                payment_layout.setContentsMargins(10, 10, 10, 10)

                # طريقة الدفع
                payment_method_label = QLabel("طريقة الدفع:")
                payment_method_label.setStyleSheet("font-weight: bold; min-width: 80px;")
                payment_layout.addWidget(payment_method_label)

                self.payment_method_combo = QComboBox()
                payment_methods = {"cash": "نقدي", "bank": "بنكي", "credit": "آجل"}
                for key, value in payment_methods.items():
                    self.payment_method_combo.addItem(value, key)
                self.payment_method_combo.setStyleSheet("""
                    QComboBox {
                        min-width: 120px;
                        min-height: 30px;
                        font-size: 12px;
                        padding: 5px;
                        padding-left: 25px;
                        border: 1px solid #ccc;
                        border-radius: 3px;
                    }
                    QComboBox::drop-down {
                        subcontrol-origin: padding;
                        subcontrol-position: left center;
                        width: 25px;
                        border: none;
                        background: transparent;
                    }
                    QComboBox::down-arrow {
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzMzMzMzMyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
                        width: 12px;
                        height: 8px;
                    }
                    QComboBox::down-arrow:hover {
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzRDQUY1MCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
                    }
                """)
                payment_layout.addWidget(self.payment_method_combo)

                # المبلغ المدفوع
                paid_amount_label = QLabel("المبلغ المدفوع:")
                paid_amount_label.setStyleSheet("font-weight: bold; min-width: 80px;")
                payment_layout.addWidget(paid_amount_label)

                self.paid_amount_input = QDoubleSpinBox()
                self.paid_amount_input.setMaximum(999999.99)
                self.paid_amount_input.setDecimals(2)
                self.paid_amount_input.setStyleSheet("min-width: 100px;")
                self.paid_amount_input.valueChanged.connect(self.calculate_remaining)
                payment_layout.addWidget(self.paid_amount_input)

                # المتبقي
                remaining_label = QLabel("المتبقي:")
                remaining_label.setStyleSheet("font-weight: bold; min-width: 50px;")
                payment_layout.addWidget(remaining_label)

                self.remaining_label = QLabel("0.00")
                self.remaining_label.setStyleSheet("""
                    background-color: #ffebee;
                    padding: 3px 8px;
                    border: 1px solid #f44336;
                    border-radius: 2px;
                    font-weight: bold;
                    color: #c62828;
                    min-width: 80px;
                """)
                payment_layout.addWidget(self.remaining_label)

                payment_layout.addStretch()
                payment_group.setLayout(payment_layout)
                main_layout.addWidget(payment_group)

            # الأزرار - تصميم مطابق لفاتورة البيع
            buttons_layout = QHBoxLayout()
            buttons_layout.setSpacing(8)
            buttons_layout.setContentsMargins(10, 10, 10, 10)

            if not self.invoice_id:
                save_btn = QPushButton("حفظ الفاتورة")
                save_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #4caf50;
                        color: white;
                        font-weight: bold;
                        padding: 8px 16px;
                        border-radius: 3px;
                        min-width: 100px;
                    }
                    QPushButton:hover {
                        background-color: #45a049;
                    }
                """)
                save_btn.clicked.connect(self.save_invoice)
                buttons_layout.addWidget(save_btn)
            else:
                # زر المعاينة
                preview_btn = QPushButton("👁️ معاينة")
                preview_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #9c27b0;
                        color: white;
                        font-weight: bold;
                        padding: 8px 16px;
                        border-radius: 3px;
                        min-width: 80px;
                    }
                    QPushButton:hover {
                        background-color: #7b1fa2;
                    }
                """)
                preview_btn.clicked.connect(self.preview_invoice)
                buttons_layout.addWidget(preview_btn)

                print_btn = QPushButton("طباعة")
                print_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #2196f3;
                        color: white;
                        font-weight: bold;
                        padding: 8px 16px;
                        border-radius: 5px;
                        border: none;
                    }
                    QPushButton:hover {
                        background-color: #1976d2;
                    }
                """)
                print_btn.clicked.connect(self.print_invoice)
                buttons_layout.addWidget(print_btn)

            cancel_btn = QPushButton("إغلاق")
            cancel_btn.setStyleSheet("""
                QPushButton {
                    background-color: #f44336;
                    color: white;
                    font-weight: bold;
                    padding: 8px 16px;
                    border-radius: 3px;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #d32f2f;
                }
            """)
            cancel_btn.clicked.connect(self.close)
            buttons_layout.addWidget(cancel_btn)

            buttons_layout.addStretch()
            main_layout.addWidget(QWidget())  # فاصل
            
            buttons_widget = QWidget()
            buttons_widget.setLayout(buttons_layout)
            main_layout.addWidget(buttons_widget)

            print("🔄 تطبيق التخطيط النهائي...")
            print("✅ تم إعداد واجهة المستخدم بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إعداد واجهة المستخدم: {str(e)}")
            import traceback
            traceback.print_exc()
            raise e
        
    def generate_invoice_number(self):
        """توليد رقم فاتورة جديد"""
        try:
            print("🔄 توليد رقم فاتورة جديد...")
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # الحصول على آخر رقم فاتورة شراء
            cursor.execute('''
                SELECT MAX(CAST(SUBSTR(invoice_number, 3) AS INTEGER))
                FROM invoices
                WHERE invoice_type = 'purchase' AND invoice_number LIKE 'P-%'
            ''')

            result = cursor.fetchone()
            # Handle case when no invoices exist
            last_number = result[0] if result[0] is not None else 0
            new_number = last_number + 1

            invoice_number = f"P-{new_number:06d}"
            self.invoice_number_label.setText(invoice_number)
            print(f"✅ تم توليد رقم الفاتورة: {invoice_number}")

            conn.close()

        except Exception as e:
            print(f"❌ خطأ في توليد رقم الفاتورة: {str(e)}")
            # رقم احتياطي
            backup_number = f"P-{datetime.now().strftime('%Y%m%d%H%M%S')}"
            self.invoice_number_label.setText(backup_number)
            print(f"✅ تم استخدام رقم احتياطي: {backup_number}")
            
    def load_suppliers(self):
        """تحميل الموردين"""
        try:
            print("🔄 تحميل قائمة الموردين...")
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('SELECT id, name FROM suppliers WHERE is_active = 1 ORDER BY name')
            suppliers = cursor.fetchall()

            self.supplier_combo.addItem("اختر مورد...", None)
            
            if not suppliers:
                print("⚠️ لم يتم العثور على موردين نشطين")
                QMessageBox.warning(
                    self,
                    "لا توجد موردين",
                    "لم يتم العثور على موردين مسجلين في النظام.\n\nيرجى إضافة موردين أولاً من شاشة إدارة الموردين."
                )
            else:
                for supplier in suppliers:
                    self.supplier_combo.addItem(supplier['name'], supplier['id'])
                print(f"✅ تم تحميل {len(suppliers)} مورد")

            conn.close()

        except Exception as e:
            print(f"❌ خطأ في تحميل الموردين: {str(e)}")
            # إضافة عنصر افتراضي حتى لو فشل التحميل
            self.supplier_combo.addItem("اختر مورد...", None)
            QMessageBox.warning(
                self,
                "خطأ في التحميل",
                f"تعذر تحميل قائمة الموردين:\n\n{str(e)}\n\nيرجى التحقق من اتصال قاعدة البيانات."
            )
            
    def load_products(self):
        """تحميل المنتجات"""
        try:
            print("🔄 تحميل قائمة المنتجات...")
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, name, purchase_price
                FROM products
                WHERE is_active = 1
                ORDER BY name
            ''')
            products = cursor.fetchall()

            self.product_combo.addItem("اختر منتج...", None)
            for product in products:
                self.product_combo.addItem(product['name'], product['id'])

            print(f"✅ تم تحميل {len(products)} منتج")
            conn.close()

        except Exception as e:
            print(f"❌ خطأ في تحميل المنتجات: {str(e)}")
            # إضافة عنصر افتراضي حتى لو فشل التحميل
            self.product_combo.addItem("اختر منتج...", None)
            QMessageBox.warning(self, "تحذير", f"تعذر تحميل قائمة المنتجات:\n{str(e)}")
            
    def add_item(self):
        """إضافة منتج للفاتورة"""
        product_id = self.product_combo.currentData()
        product_text = self.product_combo.currentText().strip()
        
        if not product_id:
            if product_text and product_text != "اختر منتج...":
                # منتج جديد
                reply = QMessageBox.question(self, "منتج غير موجود",
                                           f"المنتج '{product_text}' غير موجود.\n"
                                           "هل تريد إضافته كمنتج جديد؟",
                                           QMessageBox.Yes | QMessageBox.No)
                if reply == QMessageBox.Yes:
                    self.add_new_product_with_name(product_text)
                return
            else:
                QMessageBox.warning(self, "خطأ", "يرجى اختيار منتج من القائمة")
                return
                
        quantity = self.quantity_input.value()
        price = self.price_input.value()
        
        if price <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال سعر صحيح")
            return
            
        # الحصول على اسم المنتج
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute('SELECT name FROM products WHERE id = ?', (product_id,))
            result = cursor.fetchone()
            conn.close()
            
            if not result:
                QMessageBox.warning(self, "خطأ", "المنتج غير موجود")
                return
                
            product_name = result[0]
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في قاعدة البيانات:\n{str(e)}")
            return
            
        # إضافة المنتج للفاتورة
        item = {
            'product_id': product_id,
            'product_name': product_name,
            'quantity': quantity,
            'price': price,
            'total': quantity * price
        }
        
        self.invoice_items.append(item)
        self.update_items_table()
        self.calculate_totals()
        
        # إعادة تعيين الحقول
        self.product_combo.setCurrentIndex(0)
        self.quantity_input.setValue(1)
        self.price_input.setValue(0)
        
    def add_new_product(self):
        """إضافة منتج جديد"""
        from ui.products_window import ProductsWindow
        self.products_window = ProductsWindow(self.db_manager, self.user_data)
        self.products_window.show()
        self.products_window.product_added.connect(self.handle_new_product_added)
        
    def handle_new_product_added(self, product_id, product_name):
        """معالجة إضافة منتج جديد"""
        # إعادة تحميل قائمة المنتجات
        self.load_products()
        
        # تحديد المنتج المضاف حديثاً
        index = self.product_combo.findText(product_name, Qt.MatchFixedString)
        if index >= 0:
            self.product_combo.setCurrentIndex(index)
        
    def add_new_product_with_name(self, name):
        """إضافة منتج جديد بالاسم"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # إضافة المنتج الجديد بقيم افتراضية
            cursor.execute('''
                INSERT INTO products (name, barcode, purchase_price, sale_price, category_id, stock, is_active)
                VALUES (?, ?, 0, 0, 1, 0, 1)
            ''', (name,))
            
            conn.commit()
            conn.close()
            
            # إعادة تحميل قائمة المنتجات
            self.load_products()
            
            # تحديد المنتج المضاف حديثاً
            index = self.product_combo.findText(name, Qt.MatchFixedString)
            if index >= 0:
                self.product_combo.setCurrentIndex(index)
                
            QMessageBox.information(self, "تمت الإضافة", f"تمت إضافة المنتج '{name}' بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"تعذر إضافة المنتج:\n{str(e)}")
        
    def update_items_table(self):
        """تحديث جدول المنتجات"""
        self.items_table.setRowCount(len(self.invoice_items))
        
        for row, item in enumerate(self.invoice_items):
            self.items_table.setItem(row, 0, QTableWidgetItem(item['product_name']))
            self.items_table.setItem(row, 1, QTableWidgetItem(str(item['quantity'])))
            self.items_table.setItem(row, 2, QTableWidgetItem(f"{item['price']:.2f}"))
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{item['total']:.2f}"))
            
            # زر الحذف
            delete_btn = QPushButton("حذف")
            delete_btn.clicked.connect(lambda checked, r=row: self.remove_item(r))
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            self.items_table.setCellWidget(row, 4, delete_btn)
            
    def remove_item(self, row):
        """حذف منتج من الفاتورة"""
        if 0 <= row < len(self.invoice_items):
            del self.invoice_items[row]
            self.update_items_table()
            self.calculate_totals()
            
    def calculate_totals(self):
        """حساب الإجماليات"""
        subtotal = sum(item['total'] for item in self.invoice_items)
        discount = self.discount_input.value()
        total = subtotal - discount
        
        self.subtotal_label.setText(f"{subtotal:.2f}")
        self.total_label.setText(f"{total:.2f}")
        
    def save_invoice(self):
        """حفظ الفاتورة"""
        try:
            # التحقق من البيانات الأساسية
            if self.supplier_combo.currentIndex() == 0:  # لم يتم اختيار مورد
                QMessageBox.warning(self, "خطأ", "يرجى اختيار مورد")
                return
                
            if not self.invoice_items:
                QMessageBox.warning(self, "خطأ", "يرجى إضافة منتجات على الأقل")
                return
                
            # جمع بيانات الفاتورة
            invoice_number = self.invoice_number_label.text()
            invoice_date = self.invoice_date.date().toString("yyyy-MM-dd")
            supplier_id = self.supplier_combo.currentData()
            discount = self.discount_input.value()
            # حساب الإجماليات مباشرة من البيانات
            subtotal = sum(item['total'] for item in self.invoice_items)
            total = subtotal - discount
            user_id = self.user_data.get('id', 1)  # استخدام قيمة افتراضية
            
            # بدء المعاملة
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            try:
                # إدخال الفاتورة مع جميع الحقول المطلوبة
                cursor.execute('''
                    INSERT INTO invoices (invoice_number, invoice_type, invoice_date, supplier_id,
                                         discount_amount, total_amount, final_amount, user_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (invoice_number, 'purchase', invoice_date, supplier_id,
                      discount, subtotal, total, user_id))
                
                # الحصول على معرف الفاتورة
                invoice_id = cursor.lastrowid
                
                # إدخال الأصناف
                for item in self.invoice_items:
                    cursor.execute('''
                        INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, total_price)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (invoice_id, item['product_id'], item['quantity'], item['price'], item['total']))
                    
                    # تحديث المخزون (الشراء يزيد المخزون)
                    cursor.execute('''
                        UPDATE products
                        SET stock = stock + ?
                        WHERE id = ?
                    ''', (item['quantity'], item['product_id']))
                
                # تأكيد المعاملة
                conn.commit()
                QMessageBox.information(self, "تم الحفظ", "تم حفظ الفاتورة بنجاح")
                self.close()
                
            except Exception as e:
                conn.rollback()
                raise e
                
            finally:
                conn.close()
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الفاتورة:\n{str(e)}")
        
    def load_invoice_data(self):
        """تحميل بيانات الفاتورة للتعديل"""
        # سيتم تنفيذ هذه الوظيفة لاحقًا
        QMessageBox.information(self, "قريباً", "ميزة تحميل الفاتورة للتعديل ستكون متاحة قريباً")
    
    def calculate_remaining(self):
        """حساب المبلغ المتبقي"""
        try:
            total = float(self.total_label.text())
            paid = self.paid_amount_input.value()
            remaining = total - paid
            self.remaining_label.setText(f"{remaining:.2f}")
        except:
            self.remaining_label.setText("0.00")
    
    def preview_invoice(self):
        """معاينة الفاتورة"""
        try:
            from ui.invoice_preview import InvoicePreview
            preview_dialog = InvoicePreview(self.db_manager, self.invoice_id, 'purchase', self)
            preview_dialog.exec_()
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"تعذر فتح معاينة الفاتورة:\n{str(e)}")
    
    def print_invoice(self):
        """طباعة الفاتورة"""
        try:
            from ui.professional_printer import ProfessionalPrinter
            printer_dialog = ProfessionalPrinter(self.db_manager, self.invoice_id, 'purchase', self)
            printer_dialog.exec_()
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"تعذر طباعة الفاتورة:\n{str(e)}")
    
    def load_customers(self):
        """تحميل العملاء (مضافة للتوافق مع فاتورة البيع)"""
        # هذه الدالة للتوافق مع فاتورة البيع - لا نحتاجها في فاتورة الشراء
        pass
