#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة المرتجعات المحسنة - نظام نقاط البيع والمحاسبة
Enhanced Returns Management - POS and Accounting System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QComboBox, QDialog, QFormLayout,
                            QMessageBox, QHeaderView, QGroupBox, QSpinBox,
                            QDateEdit, QTextEdit, QGridLayout, QFrame)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QIcon
from datetime import datetime
import sqlite3

class ReturnsManagementWindow(QWidget):
    """نافذة إدارة المرتجعات المحسنة"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة المرتجعات")
        self.setGeometry(100, 100, 1000, 700)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # العنوان الرئيسي
        title_label = QLabel("إدارة المرتجعات")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 15px;
                border-radius: 10px;
                margin: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # منطقة الأزرار الرئيسية
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-radius: 10px;
                padding: 20px;
                margin: 10px;
            }
        """)
        buttons_layout = QHBoxLayout()
        
        # زر مرتجع المبيعات
        sales_return_btn = QPushButton("🔄 مرتجع مبيعات")
        sales_return_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                font-size: 16px;
                padding: 20px 40px;
                border-radius: 10px;
                border: none;
                min-height: 60px;
            }
            QPushButton:hover {
                background-color: #2980b9;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        sales_return_btn.clicked.connect(self.open_sales_return)
        buttons_layout.addWidget(sales_return_btn)
        
        # زر مرتجع المشتريات
        purchase_return_btn = QPushButton("🔄 مرتجع مشتريات")
        purchase_return_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                font-size: 16px;
                padding: 20px 40px;
                border-radius: 10px;
                border: none;
                min-height: 60px;
            }
            QPushButton:hover {
                background-color: #c0392b;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        purchase_return_btn.clicked.connect(self.open_purchase_return)
        buttons_layout.addWidget(purchase_return_btn)
        
        buttons_frame.setLayout(buttons_layout)
        main_layout.addWidget(buttons_frame)
        
        # منطقة عرض المرتجعات الحديثة
        recent_frame = QFrame()
        recent_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
                margin: 10px;
            }
        """)
        recent_layout = QVBoxLayout()
        
        recent_title = QLabel("المرتجعات الحديثة")
        recent_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #495057;
                margin-bottom: 10px;
            }
        """)
        recent_layout.addWidget(recent_title)
        
        # جدول المرتجعات الحديثة
        self.recent_returns_table = QTableWidget()
        self.recent_returns_table.setColumnCount(6)
        self.recent_returns_table.setHorizontalHeaderLabels([
            "رقم المرتجع", "النوع", "التاريخ", "العميل/المورد", "المبلغ", "الحالة"
        ])
        self.recent_returns_table.horizontalHeader().setStretchLastSection(True)
        self.recent_returns_table.setAlternatingRowColors(True)
        self.recent_returns_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QHeaderView::section {
                background-color: #6c757d;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: none;
            }
        """)
        recent_layout.addWidget(self.recent_returns_table)
        
        recent_frame.setLayout(recent_layout)
        main_layout.addWidget(recent_frame)
        
        self.setLayout(main_layout)
        
        # تحميل البيانات
        self.load_recent_returns()
        
    def open_sales_return(self):
        """فتح نافذة مرتجع المبيعات"""
        try:
            dialog = SalesReturnDialog(self.db_manager, self.user_data, self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_recent_returns()
                QMessageBox.information(self, "نجح", "تم إنشاء مرتجع المبيعات بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في فتح مرتجع المبيعات:\n{str(e)}")
            
    def open_purchase_return(self):
        """فتح نافذة مرتجع المشتريات"""
        try:
            dialog = PurchaseReturnDialog(self.db_manager, self.user_data, self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_recent_returns()
                QMessageBox.information(self, "نجح", "تم إنشاء مرتجع المشتريات بنجاح!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في فتح مرتجع المشتريات:\n{str(e)}")
            
    def load_recent_returns(self):
        """تحميل المرتجعات الحديثة"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # التأكد من وجود جداول المرتجعات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sales_returns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    return_number TEXT UNIQUE NOT NULL,
                    return_date TEXT NOT NULL,
                    original_invoice_id INTEGER NOT NULL,
                    customer_id INTEGER,
                    total_amount REAL NOT NULL DEFAULT 0,
                    user_id INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # تحميل مرتجعات المبيعات
            cursor.execute("""
                SELECT sr.return_number, 'مرتجع مبيعات' as type, sr.return_date,
                       COALESCE(c.name, 'عميل نقدي') as client_name,
                       sr.total_amount, 'مكتمل' as status
                FROM sales_returns sr
                LEFT JOIN customers c ON sr.customer_id = c.id
                ORDER BY sr.created_at DESC
                LIMIT 10
            """)

            returns_data = cursor.fetchall()
            conn.close()

            self.recent_returns_table.setRowCount(len(returns_data))

            for row, return_data in enumerate(returns_data):
                for col, value in enumerate(return_data):
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين حسب النوع
                    if col == 1:  # عمود النوع
                        if "مبيعات" in str(value):
                            item.setBackground(Qt.lightBlue)
                        else:
                            item.setBackground(Qt.lightCoral)

                    self.recent_returns_table.setItem(row, col, item)

        except Exception as e:
            print(f"خطأ في تحميل المرتجعات الحديثة: {str(e)}")
            # في حالة الخطأ، عرض جدول فارغ
            self.recent_returns_table.setRowCount(0)


class SalesReturnDialog(QDialog):
    """نافذة مرتجع المبيعات"""
    
    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        self.selected_invoice = None
        self.return_items = []
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("مرتجع مبيعات جديد")
        self.setFixedSize(900, 700)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("إنشاء مرتجع مبيعات جديد")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                background-color: #3498db;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # معلومات المرتجع
        info_group = QGroupBox("معلومات المرتجع")
        info_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        info_layout = QFormLayout()
        
        # رقم المرتجع
        self.return_number_label = QLabel(self.generate_return_number())
        self.return_number_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        info_layout.addRow("رقم المرتجع:", self.return_number_label)
        
        # التاريخ
        self.return_date = QDateEdit()
        self.return_date.setDate(QDate.currentDate())
        self.return_date.setCalendarPopup(True)
        info_layout.addRow("تاريخ المرتجع:", self.return_date)
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # اختيار الفاتورة
        invoice_group = QGroupBox("اختيار الفاتورة الأصلية")
        invoice_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin: 10px;
                padding-top: 10px;
            }
        """)
        invoice_layout = QVBoxLayout()
        
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("البحث بـ:"))
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("رقم الفاتورة أو اسم العميل...")
        self.search_input.textChanged.connect(self.search_invoices)
        search_layout.addWidget(self.search_input)
        
        search_btn = QPushButton("بحث")
        search_btn.clicked.connect(self.search_invoices)
        search_layout.addWidget(search_btn)
        
        invoice_layout.addLayout(search_layout)
        
        # قائمة الفواتير
        self.invoices_combo = QComboBox()
        self.invoices_combo.currentTextChanged.connect(self.on_invoice_selected)
        invoice_layout.addWidget(self.invoices_combo)
        
        invoice_group.setLayout(invoice_layout)
        layout.addWidget(invoice_group)

        # منطقة المنتجات
        self.products_group = QGroupBox("منتجات الفاتورة")
        self.products_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin: 10px;
                padding-top: 10px;
            }
        """)
        products_layout = QVBoxLayout()

        # جدول المنتجات
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(6)
        self.products_table.setHorizontalHeaderLabels([
            "المنتج", "الكمية الأصلية", "كمية المرتجع", "السعر", "الإجمالي", "إجراء"
        ])
        self.products_table.horizontalHeader().setStretchLastSection(True)
        self.products_table.setAlternatingRowColors(True)
        self.products_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QHeaderView::section {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: none;
            }
        """)
        products_layout.addWidget(self.products_table)

        # إجمالي المرتجع
        total_layout = QHBoxLayout()
        total_layout.addStretch()
        total_layout.addWidget(QLabel("إجمالي المرتجع:"))
        self.total_label = QLabel("0.00 جنيه")
        self.total_label.setStyleSheet("font-weight: bold; font-size: 16px; color: #e74c3c;")
        total_layout.addWidget(self.total_label)
        products_layout.addLayout(total_layout)

        self.products_group.setLayout(products_layout)
        self.products_group.setVisible(False)  # مخفي في البداية
        layout.addWidget(self.products_group)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("حفظ المرتجع")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 12px 24px;
                border-radius: 6px;
                border: none;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_btn.clicked.connect(self.save_return)
        buttons_layout.addWidget(save_btn)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                padding: 12px 24px;
                border-radius: 6px;
                border: none;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
        
        # تحميل الفواتير
        self.load_invoices()
        
    def generate_return_number(self):
        """توليد رقم مرتجع جديد"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            today = datetime.now().strftime("%Y%m%d")

            # التأكد من وجود جدول المرتجعات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sales_returns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    return_number TEXT UNIQUE NOT NULL,
                    return_date TEXT NOT NULL,
                    original_invoice_id INTEGER NOT NULL,
                    customer_id INTEGER,
                    total_amount REAL NOT NULL DEFAULT 0,
                    user_id INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # البحث عن آخر رقم مرتجع لهذا اليوم
            cursor.execute("""
                SELECT COUNT(*) FROM sales_returns
                WHERE return_number LIKE ?
            """, (f"SR{today}%",))

            count = cursor.fetchone()[0] + 1
            conn.close()

            return f"SR{today}{count:03d}"

        except Exception as e:
            print(f"خطأ في توليد رقم المرتجع: {str(e)}")
            today = datetime.now().strftime("%Y%m%d")
            return f"SR{today}001"
        
    def load_invoices(self):
        """تحميل فواتير البيع"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT i.id, i.invoice_number, c.name as customer_name, 
                       i.invoice_date, i.final_amount
                FROM invoices i
                LEFT JOIN customers c ON i.customer_id = c.id
                WHERE i.invoice_type = 'sale'
                ORDER BY i.invoice_date DESC
                LIMIT 50
            """)
            
            invoices = cursor.fetchall()
            conn.close()
            
            self.invoices_combo.clear()
            self.invoices_combo.addItem("اختر فاتورة...", None)
            
            for invoice in invoices:
                customer_name = invoice['customer_name'] or 'عميل نقدي'
                display_text = f"{invoice['invoice_number']} - {customer_name} - {invoice['final_amount']:.2f} جنيه"
                self.invoices_combo.addItem(display_text, invoice)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الفواتير:\n{str(e)}")
            
    def search_invoices(self):
        """البحث في الفواتير"""
        search_text = self.search_input.text().strip()
        if not search_text:
            self.load_invoices()
            return
            
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT i.id, i.invoice_number, c.name as customer_name, 
                       i.invoice_date, i.final_amount
                FROM invoices i
                LEFT JOIN customers c ON i.customer_id = c.id
                WHERE i.invoice_type = 'sale' 
                AND (i.invoice_number LIKE ? OR c.name LIKE ?)
                ORDER BY i.invoice_date DESC
                LIMIT 50
            """, (f"%{search_text}%", f"%{search_text}%"))
            
            invoices = cursor.fetchall()
            conn.close()
            
            self.invoices_combo.clear()
            self.invoices_combo.addItem("اختر فاتورة...", None)
            
            for invoice in invoices:
                customer_name = invoice['customer_name'] or 'عميل نقدي'
                display_text = f"{invoice['invoice_number']} - {customer_name} - {invoice['final_amount']:.2f} جنيه"
                self.invoices_combo.addItem(display_text, invoice)
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في البحث:\n{str(e)}")
            
    def on_invoice_selected(self):
        """عند اختيار فاتورة"""
        self.selected_invoice = self.invoices_combo.currentData()

        if self.selected_invoice:
            self.load_invoice_products()
            self.products_group.setVisible(True)
        else:
            self.products_group.setVisible(False)

    def load_invoice_products(self):
        """تحميل منتجات الفاتورة"""
        if not self.selected_invoice:
            return

        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT ii.*, p.name as product_name
                FROM invoice_items ii
                JOIN products p ON ii.product_id = p.id
                WHERE ii.invoice_id = ?
            """, (self.selected_invoice['id'],))

            products = cursor.fetchall()
            conn.close()

            self.products_table.setRowCount(len(products))
            self.return_items = []

            for row, product in enumerate(products):
                # اسم المنتج
                self.products_table.setItem(row, 0, QTableWidgetItem(product['product_name']))

                # الكمية الأصلية
                self.products_table.setItem(row, 1, QTableWidgetItem(str(product['quantity'])))

                # كمية المرتجع (SpinBox)
                return_qty_spin = QSpinBox()
                return_qty_spin.setMinimum(0)
                return_qty_spin.setMaximum(int(product['quantity']))
                return_qty_spin.valueChanged.connect(lambda value, r=row: self.update_return_total())
                self.products_table.setCellWidget(row, 2, return_qty_spin)

                # السعر
                self.products_table.setItem(row, 3, QTableWidgetItem(f"{product['unit_price']:.2f}"))

                # الإجمالي (سيتم تحديثه تلقائياً)
                self.products_table.setItem(row, 4, QTableWidgetItem("0.00"))

                # زر إضافة/حذف
                action_btn = QPushButton("إضافة للمرتجع")
                action_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #28a745;
                        color: white;
                        border: none;
                        padding: 5px 10px;
                        border-radius: 3px;
                    }
                    QPushButton:hover {
                        background-color: #218838;
                    }
                """)
                action_btn.clicked.connect(lambda checked, r=row, p=product: self.toggle_product_return(r, p))
                self.products_table.setCellWidget(row, 5, action_btn)

                # حفظ بيانات المنتج
                self.return_items.append({
                    'product_id': product['product_id'],
                    'product_name': product['product_name'],
                    'original_quantity': product['quantity'],
                    'unit_price': product['unit_price'],
                    'return_quantity': 0,
                    'included': False
                })

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل منتجات الفاتورة:\n{str(e)}")

    def toggle_product_return(self, row, product):
        """تبديل حالة إضافة المنتج للمرتجع"""
        return_qty_spin = self.products_table.cellWidget(row, 2)
        action_btn = self.products_table.cellWidget(row, 5)

        if self.return_items[row]['included']:
            # إزالة من المرتجع
            self.return_items[row]['included'] = False
            self.return_items[row]['return_quantity'] = 0
            return_qty_spin.setValue(0)
            return_qty_spin.setEnabled(False)
            action_btn.setText("إضافة للمرتجع")
            action_btn.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
            """)
        else:
            # إضافة للمرتجع
            self.return_items[row]['included'] = True
            return_qty_spin.setEnabled(True)
            return_qty_spin.setValue(1)  # قيمة افتراضية
            action_btn.setText("إزالة من المرتجع")
            action_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)

        self.update_return_total()

    def update_return_total(self):
        """تحديث إجمالي المرتجع"""
        total = 0.0

        for row in range(self.products_table.rowCount()):
            if self.return_items[row]['included']:
                return_qty_spin = self.products_table.cellWidget(row, 2)
                return_qty = return_qty_spin.value()
                unit_price = self.return_items[row]['unit_price']
                item_total = return_qty * unit_price

                # تحديث الكمية في البيانات
                self.return_items[row]['return_quantity'] = return_qty

                # تحديث إجمالي الصف
                self.products_table.setItem(row, 4, QTableWidgetItem(f"{item_total:.2f}"))

                total += item_total
            else:
                self.products_table.setItem(row, 4, QTableWidgetItem("0.00"))

        self.total_label.setText(f"{total:.2f} جنيه")
        
    def save_return(self):
        """حفظ المرتجع"""
        if not self.selected_invoice:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة أولاً")
            return

        # التحقق من وجود منتجات للمرتجع
        if not hasattr(self, 'return_items') or not self.return_items:
            QMessageBox.warning(self, "تحذير", "لا توجد منتجات في الفاتورة")
            return

        return_products = [item for item in self.return_items if item.get('included', False) and item.get('return_quantity', 0) > 0]
        if not return_products:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتجات للمرتجع وتحديد الكميات")
            return

        # تأكيد من المستخدم
        total_amount = sum(item.get('return_quantity', 0) * item.get('unit_price', 0) for item in return_products)

        reply = QMessageBox.question(self, "تأكيد المرتجع",
            f"هل أنت متأكد من إنشاء مرتجع المبيعات؟\n\n"
            f"📋 رقم المرتجع: {self.return_number_label.text()}\n"
            f"💰 إجمالي المبلغ: {total_amount:.2f} جنيه\n"
            f"📦 عدد المنتجات: {len(return_products)}\n\n"
            f"سيتم تحديث المخزون تلقائياً",
            QMessageBox.Yes | QMessageBox.No)

        if reply != QMessageBox.Yes:
            return

        try:
            print("🔄 بدء عملية حفظ المرتجع...")
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # إنشاء جداول المرتجعات إذا لم تكن موجودة
            print("🔄 التحقق من جداول قاعدة البيانات...")
            self.create_returns_tables(cursor)

            # حساب إجمالي المرتجع
            print(f"💰 إجمالي المرتجع: {total_amount:.2f} جنيه")

            # إدراج المرتجع الرئيسي
            print("🔄 إدراج بيانات المرتجع الرئيسية...")
            cursor.execute("""
                INSERT INTO sales_returns
                (return_number, return_date, original_invoice_id, customer_id,
                 total_amount, user_id, created_at)
                VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
            """, (
                self.return_number_label.text(),
                self.return_date.date().toString('yyyy-MM-dd'),
                self.selected_invoice['id'],
                self.selected_invoice.get('customer_id'),
                total_amount,
                self.user_data['id']
            ))

            return_id = cursor.lastrowid
            print(f"✅ تم إنشاء المرتجع برقم: {return_id}")

            # إدراج تفاصيل المرتجع
            print("🔄 إدراج تفاصيل المنتجات...")
            for i, item in enumerate(return_products, 1):
                product_name = item.get('product_name', 'منتج غير محدد')
                return_qty = item.get('return_quantity', 0)
                unit_price = item.get('unit_price', 0)
                product_id = item.get('product_id')

                print(f"🔄 ({i}/{len(return_products)}) معالجة منتج: {product_name} - كمية: {return_qty}")

                if not product_id:
                    print(f"⚠️ تخطي منتج بدون معرف: {product_name}")
                    continue

                try:
                    # إدراج تفاصيل المرتجع
                    cursor.execute("""
                        INSERT INTO sales_return_items
                        (return_id, product_id, quantity, unit_price, total_price, created_at)
                        VALUES (?, ?, ?, ?, ?, datetime('now'))
                    """, (
                        return_id,
                        product_id,
                        return_qty,
                        unit_price,
                        return_qty * unit_price
                    ))
                    print(f"✅ تم إدراج تفاصيل المنتج")

                    # تحديث المخزون (إضافة الكمية المرتجعة)
                    cursor.execute("""
                        UPDATE products
                        SET current_stock = current_stock + ?
                        WHERE id = ?
                    """, (return_qty, product_id))

                    # التحقق من تأثير التحديث
                    if cursor.rowcount > 0:
                        print(f"✅ تم تحديث المخزون للمنتج {product_name}")
                    else:
                        print(f"⚠️ لم يتم العثور على المنتج في قاعدة البيانات: {product_name}")

                    # إضافة حركة مخزون (اختيارية)
                    try:
                        cursor.execute("""
                            INSERT INTO stock_movements
                            (product_id, movement_type, quantity, reference_type, reference_id,
                             notes, movement_date, user_id)
                            VALUES (?, 'in', ?, 'return', ?, ?, datetime('now'), ?)
                        """, (
                            product_id,
                            return_qty,
                            return_id,
                            f"مرتجع مبيعات رقم {self.return_number_label.text()}",
                            self.user_data['id']
                        ))
                        print(f"✅ تم إضافة حركة المخزون للمنتج {product_name}")
                    except Exception as stock_error:
                        print(f"⚠️ تحذير: لم يتم إضافة حركة المخزون للمنتج {product_name}: {str(stock_error)}")

                except Exception as item_error:
                    print(f"❌ خطأ في معالجة المنتج {product_name}: {str(item_error)}")
                    raise item_error

            # تحديث النظام المالي (اختياري)
            print("🔄 محاولة تحديث النظام المالي...")
            try:
                from database.financial_manager import FinancialManager
                import os

                db_path = os.path.join('database', 'business_system.db')
                financial_manager = FinancialManager(db_path)

                # خصم من الخزينة (المرتجعات تقلل الأرباح)
                financial_manager.add_cash_transaction(
                    "مصروف",
                    "مرتجع مبيعات",
                    total_amount,
                    f"مرتجع مبيعات رقم {self.return_number_label.text()}"
                )
                print(f"✅ تم خصم {total_amount:,.2f} جنيه من الخزينة للمرتجع")

            except ImportError:
                print("⚠️ تحذير: لم يتم العثور على FinancialManager - سيتم تخطي التحديث المالي")
            except Exception as e:
                print(f"⚠️ تحذير: خطأ في تحديث النظام المالي: {str(e)}")
                # لا نوقف العملية بسبب خطأ في النظام المالي

            # حفظ التغييرات
            print("🔄 حفظ التغييرات في قاعدة البيانات...")
            conn.commit()
            print("✅ تم حفظ جميع التغييرات في قاعدة البيانات بنجاح")
            conn.close()

            # رسالة النجاح
            success_message = (
                f"🎉 تم حفظ مرتجع المبيعات بنجاح!\n\n"
                f"📋 رقم المرتجع: {self.return_number_label.text()}\n"
                f"💰 إجمالي المبلغ: {total_amount:.2f} جنيه\n"
                f"📦 عدد المنتجات: {len(return_products)}\n"
                f"📅 التاريخ: {self.return_date.date().toString('yyyy-MM-dd')}\n"
                f"🏪 الفاتورة الأصلية: {self.selected_invoice['invoice_number']}\n\n"
                f"✅ تم تحديث المخزون تلقائياً\n"
                f"✅ تم تسجيل حركات المخزون"
            )

            QMessageBox.information(self, "نجح العملية ✅", success_message)
            print("🎉 تمت العملية بنجاح!")
            self.accept()

        except Exception as e:
            # في حالة الخطأ، التراجع عن التغييرات
            print(f"❌ حدث خطأ أثناء حفظ المرتجع: {str(e)}")

            if 'conn' in locals():
                try:
                    conn.rollback()
                    conn.close()
                    print("🔄 تم التراجع عن التغييرات في قاعدة البيانات")
                except Exception as rollback_error:
                    print(f"⚠️ خطأ في التراجع: {str(rollback_error)}")

            # عرض تفاصيل الخطأ للتشخيص
            import traceback
            error_details = traceback.format_exc()
            print("=" * 50)
            print("تفاصيل الخطأ الكاملة:")
            print(error_details)
            print("=" * 50)

            # رسالة خطأ مبسطة للمستخدم
            error_message = str(e)
            if "FOREIGN KEY constraint failed" in error_message:
                user_message = "خطأ في ربط البيانات. تأكد من صحة الفاتورة المختارة."
            elif "UNIQUE constraint failed" in error_message:
                user_message = "رقم المرتجع موجود مسبقاً. سيتم توليد رقم جديد."
            elif "no such table" in error_message:
                user_message = "خطأ في هيكل قاعدة البيانات. سيتم إنشاء الجداول المطلوبة."
            elif "no such column" in error_message:
                user_message = "خطأ في هيكل قاعدة البيانات. تحقق من تحديث النظام."
            else:
                user_message = f"خطأ غير متوقع: {str(e)}"

            QMessageBox.critical(self, "خطأ في حفظ المرتجع ❌",
                f"حدث خطأ أثناء حفظ المرتجع:\n\n"
                f"📋 السبب: {user_message}\n\n"
                f"💡 الحلول المقترحة:\n"
                f"• تأكد من اختيار فاتورة صحيحة\n"
                f"• تأكد من تحديد منتجات وكميات صحيحة\n"
                f"• أعد تشغيل التطبيق إذا استمر الخطأ\n"
                f"• تواصل مع الدعم الفني إذا لزم الأمر\n\n"
                f"🔧 تفاصيل تقنية: {str(e)}")

    def create_returns_tables(self, cursor):
        """إنشاء جداول المرتجعات"""
        # جدول مرتجعات المبيعات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sales_returns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                return_number TEXT UNIQUE NOT NULL,
                return_date TEXT NOT NULL,
                original_invoice_id INTEGER NOT NULL,
                customer_id INTEGER,
                total_amount REAL NOT NULL DEFAULT 0,
                user_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (original_invoice_id) REFERENCES invoices (id),
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)

        # جدول تفاصيل مرتجعات المبيعات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sales_return_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                return_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity REAL NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (return_id) REFERENCES sales_returns (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        """)

        # جدول حركات المخزون (إذا لم يكن موجوداً)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS stock_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                movement_type TEXT NOT NULL CHECK (movement_type IN ('in', 'out', 'adjustment')),
                quantity REAL NOT NULL,
                reference_type TEXT CHECK (reference_type IN ('invoice', 'return', 'adjustment')),
                reference_id INTEGER,
                notes TEXT,
                movement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                user_id INTEGER NOT NULL,
                FOREIGN KEY (product_id) REFERENCES products (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """)


class PurchaseReturnDialog(QDialog):
    """نافذة مرتجع المشتريات"""

    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        self.selected_invoice = None
        self.return_items = []
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("مرتجع مشتريات جديد")
        self.setFixedSize(800, 600)

        layout = QVBoxLayout()

        # العنوان
        title_label = QLabel("إنشاء مرتجع مشتريات جديد")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: white;
                background-color: #e74c3c;
                padding: 15px;
                border-radius: 8px;
                margin: 10px;
            }
        """)
        layout.addWidget(title_label)

        # معلومات المرتجع
        info_group = QGroupBox("معلومات المرتجع")
        info_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin: 10px;
                padding-top: 10px;
            }
        """)
        info_layout = QFormLayout()

        # رقم المرتجع
        self.return_number_label = QLabel(self.generate_return_number())
        self.return_number_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        info_layout.addRow("رقم المرتجع:", self.return_number_label)

        # التاريخ
        self.return_date = QDateEdit()
        self.return_date.setDate(QDate.currentDate())
        self.return_date.setCalendarPopup(True)
        info_layout.addRow("تاريخ المرتجع:", self.return_date)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        # اختيار فاتورة الشراء
        invoice_group = QGroupBox("اختيار فاتورة الشراء الأصلية")
        invoice_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin: 10px;
                padding-top: 10px;
            }
        """)
        invoice_layout = QVBoxLayout()

        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("البحث بـ:"))

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("رقم الفاتورة أو اسم المورد...")
        self.search_input.textChanged.connect(self.search_invoices)
        search_layout.addWidget(self.search_input)

        search_btn = QPushButton("بحث")
        search_btn.clicked.connect(self.search_invoices)
        search_layout.addWidget(search_btn)

        invoice_layout.addLayout(search_layout)

        # قائمة فواتير الشراء
        self.invoices_combo = QComboBox()
        self.invoices_combo.currentTextChanged.connect(self.on_invoice_selected)
        invoice_layout.addWidget(self.invoices_combo)

        invoice_group.setLayout(invoice_layout)
        layout.addWidget(invoice_group)

        # رسالة مؤقتة
        message_label = QLabel("⚠️ ميزة مرتجع المشتريات قيد التطوير\nسيتم إضافة الوظائف الكاملة قريباً...")
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #f39c12;
                background-color: #fef9e7;
                border: 2px solid #f39c12;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
            }
        """)
        layout.addWidget(message_label)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("حفظ (قريباً)")
        save_btn.setEnabled(False)
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                font-weight: bold;
                padding: 12px 24px;
                border-radius: 6px;
                border: none;
                font-size: 14px;
            }
        """)
        buttons_layout.addWidget(save_btn)

        cancel_btn = QPushButton("إغلاق")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                padding: 12px 24px;
                border-radius: 6px;
                border: none;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

        # تحميل فواتير الشراء
        self.load_purchase_invoices()

    def generate_return_number(self):
        """توليد رقم مرتجع مشتريات جديد"""
        today = datetime.now().strftime("%Y%m%d")
        return f"PR{today}001"

    def load_purchase_invoices(self):
        """تحميل فواتير الشراء"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT i.id, i.invoice_number, s.name as supplier_name,
                       i.invoice_date, i.final_amount
                FROM invoices i
                LEFT JOIN suppliers s ON i.supplier_id = s.id
                WHERE i.invoice_type = 'purchase'
                ORDER BY i.invoice_date DESC
                LIMIT 50
            """)

            invoices = cursor.fetchall()
            conn.close()

            self.invoices_combo.clear()
            self.invoices_combo.addItem("اختر فاتورة شراء...", None)

            for invoice in invoices:
                supplier_name = invoice['supplier_name'] or 'مورد غير محدد'
                display_text = f"{invoice['invoice_number']} - {supplier_name} - {invoice['final_amount']:.2f} جنيه"
                self.invoices_combo.addItem(display_text, invoice)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل فواتير الشراء:\n{str(e)}")

    def search_invoices(self):
        """البحث في فواتير الشراء"""
        search_text = self.search_input.text().strip()
        if not search_text:
            self.load_purchase_invoices()
            return

        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT i.id, i.invoice_number, s.name as supplier_name,
                       i.invoice_date, i.final_amount
                FROM invoices i
                LEFT JOIN suppliers s ON i.supplier_id = s.id
                WHERE i.invoice_type = 'purchase'
                AND (i.invoice_number LIKE ? OR s.name LIKE ?)
                ORDER BY i.invoice_date DESC
                LIMIT 50
            """, (f"%{search_text}%", f"%{search_text}%"))

            invoices = cursor.fetchall()
            conn.close()

            self.invoices_combo.clear()
            self.invoices_combo.addItem("اختر فاتورة شراء...", None)

            for invoice in invoices:
                supplier_name = invoice['supplier_name'] or 'مورد غير محدد'
                display_text = f"{invoice['invoice_number']} - {supplier_name} - {invoice['final_amount']:.2f} جنيه"
                self.invoices_combo.addItem(display_text, invoice)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في البحث:\n{str(e)}")

    def on_invoice_selected(self):
        """عند اختيار فاتورة شراء"""
        self.selected_invoice = self.invoices_combo.currentData()
        if self.selected_invoice:
            QMessageBox.information(self, "تم الاختيار",
                f"تم اختيار فاتورة: {self.selected_invoice['invoice_number']}\n"
                f"سيتم تطوير باقي الوظائف قريباً...")
