"""
نافذة المشتريات الرئيسية
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QFrame, QGridLayout, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon

class PurchasesMainWindow(QWidget):
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة المشتريات")
        self.setGeometry(100, 100, 800, 600)
        
        # تطبيق نفس التصميم المستخدم في فاتورة البيع
        self.setStyleSheet("""
            QWidget {
                background-color: #c8e6c9;
                font-family: 'Arial';
                font-size: 12px;
            }
            QFrame {
                background-color: #e8f5e8;
                border: 1px solid #a5d6a7;
                border-radius: 8px;
                margin: 5px;
            }
            QPushButton {
                background-color: #66bb6a;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #4caf50;
            }
            QPushButton:pressed {
                background-color: #388e3c;
            }
            QLabel {
                color: #2e7d32;
                font-size: 12px;
            }
        """)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # العنوان الرئيسي
        title_label = QLabel("🛒 إدارة المشتريات")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 24, QFont.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: #2e7d32;
                background-color: #e8f5e8;
                padding: 20px;
                border-radius: 10px;
                margin: 10px;
                border: 2px solid #66bb6a;
            }
        """)
        main_layout.addWidget(title_label)
        
        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #e8f5e8;
                border-radius: 15px;
                padding: 20px;
                margin: 10px;
                border: 2px solid #a5d6a7;
            }
        """)
        
        # تخطيط الأزرار
        buttons_layout = QGridLayout()
        
        # قائمة الأزرار
        buttons_data = [
            {
                'text': '📝 تسجيل فاتورة شراء',
                'icon': '📝',
                'action': self.open_new_purchase_invoice,
                'color': '#66bb6a',
                'description': 'إنشاء فاتورة شراء جديدة'
            },
            {
                'text': '📋 عرض فواتير الشراء',
                'icon': '📋',
                'action': self.view_purchase_invoices,
                'color': '#66bb6a',
                'description': 'عرض وإدارة فواتير الشراء'
            },
            {
                'text': '🔄 مرتجع مشتريات',
                'icon': '🔄',
                'action': self.purchase_returns,
                'color': '#66bb6a',
                'description': 'إدارة مرتجعات المشتريات'
            },
            {
                'text': '📊 تقرير المشتريات',
                'icon': '📊',
                'action': self.purchase_reports,
                'color': '#66bb6a',
                'description': 'تقارير وإحصائيات المشتريات'
            }
        ]
        
        # إنشاء الأزرار
        row = 0
        col = 0
        for button_data in buttons_data:
            btn = self.create_main_button(
                button_data['text'],
                button_data['action'],
                button_data['color'],
                button_data['description']
            )
            buttons_layout.addWidget(btn, row, col)
            
            col += 1
            if col > 1:  # عمودين في كل صف
                col = 0
                row += 1
        
        buttons_frame.setLayout(buttons_layout)
        main_layout.addWidget(buttons_frame)
        
        # إضافة مساحة فارغة
        main_layout.addStretch()
        
        # معلومات المستخدم
        user_info = QLabel(f"المستخدم: {self.user_data.get('username', 'غير محدد')}")
        user_info.setAlignment(Qt.AlignCenter)
        user_info.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 12px;
                padding: 10px;
            }
        """)
        main_layout.addWidget(user_info)
        
        self.setLayout(main_layout)
        
    def create_main_button(self, text, action, color, description):
        """إنشاء زر رئيسي"""
        btn = QPushButton(text)
        btn.clicked.connect(action)
        btn.setMinimumSize(300, 100)
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 15px;
                font-size: 16px;
                font-weight: bold;
                padding: 20px;
                margin: 10px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.3)};
            }}
        """)
        btn.setToolTip(description)
        return btn
        
    def darken_color(self, color, factor=0.2):
        """تغميق اللون"""
        # تحويل بسيط للألوان
        color_map = {
            '#66bb6a': '#4caf50',
            '#4caf50': '#388e3c',
            '#388e3c': '#2e7d32'
        }
        return color_map.get(color, '#4caf50')
        
    def open_new_purchase_invoice(self):
        """فتح نافذة فاتورة شراء جديدة"""
        try:
            print("🔄 محاولة فتح نافذة فاتورة الشراء...")
            from ui.purchase_invoice_new import PurchaseInvoiceNewWindow
            print("✅ تم استيراد PurchaseInvoiceNewWindow بنجاح")

            self.purchase_invoice_window = PurchaseInvoiceNewWindow(self.db_manager, self.user_data)
            print("✅ تم إنشاء نافذة فاتورة الشراء بنجاح")

            self.purchase_invoice_window.show()
            print("✅ تم عرض نافذة فاتورة الشراء بنجاح")

        except ImportError as e:
            print(f"❌ خطأ في الاستيراد: {str(e)}")
            QMessageBox.critical(self, "خطأ في الاستيراد", f"خطأ في استيراد نافذة فاتورة الشراء:\n{str(e)}")
        except Exception as e:
            print(f"❌ خطأ عام: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة فاتورة الشراء:\n{str(e)}\n\nتفاصيل الخطأ في وحدة التحكم")
            
    def view_purchase_invoices(self):
        """عرض فواتير الشراء"""
        try:
            from ui.purchase_window_new import PurchaseWindow
            self.invoices_list_window = PurchaseWindow(self.db_manager, self.user_data)
            self.invoices_list_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح قائمة فواتير الشراء:\n{str(e)}")
            
    def purchase_returns(self):
        """مرتجعات المشتريات"""
        try:
            from ui.purchase_returns import PurchaseReturnsWindow
            self.returns_window = PurchaseReturnsWindow(self.db_manager, self.user_data)
            self.returns_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح نافذة مرتجعات المشتريات:\n{str(e)}")
            
    def purchase_reports(self):
        """تقارير المشتريات"""
        try:
            from ui.purchase_reports import PurchaseReportsWindow
            self.reports_window = PurchaseReportsWindow(self.db_manager, self.user_data)
            self.reports_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح تقارير المشتريات:\n{str(e)}")
