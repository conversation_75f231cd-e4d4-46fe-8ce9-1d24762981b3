"""
نافذة الفواتير المتعددة - إمكانية فتح عدة فواتير بيع في نفس الوقت
Multi Sales Window - Multiple invoices at the same time
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QTabWidget, QLabel, QPushButton, QMessageBox,
                             QToolBar, QAction, QStatusBar, QSplitter)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon
from ui.sales_window import SalesInvoiceDialog
import uuid


class MultiSalesWindow(QMainWindow):
    """نافذة الفواتير المتعددة"""
    
    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        self.invoice_counter = 1
        self.active_invoices = {}  # قاموس لحفظ الفواتير النشطة
        
        self.init_ui()
        self.create_first_invoice()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("فواتير البيع المتعددة - Multi Sales")
        self.setGeometry(50, 50, 1400, 900)
        
        # تطبيق التصميم
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabWidget::tab-bar {
                alignment: right;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                color: black;
                padding: 8px 15px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #4CAF50;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #d0d0d0;
            }
            QTabBar::close-button {
                image: url(close.png);
                subcontrol-position: right;
            }
        """)
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء التبويبات
        self.tabs = QTabWidget()
        self.tabs.setTabsClosable(True)
        self.tabs.setMovable(True)
        self.tabs.tabCloseRequested.connect(self.close_invoice_tab)
        
        self.setCentralWidget(self.tabs)
        
        # إنشاء شريط الحالة
        self.create_status_bar()
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = QToolBar()
        toolbar.setStyleSheet("""
            QToolBar {
                background-color: #2c3e50;
                color: white;
                border: none;
                spacing: 10px;
                padding: 5px;
            }
            QToolBar QToolButton {
                background-color: #34495e;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QToolBar QToolButton:hover {
                background-color: #4CAF50;
            }
        """)
        
        # إضافة فاتورة جديدة
        new_invoice_action = QAction("➕ فاتورة جديدة", self)
        new_invoice_action.triggered.connect(self.create_new_invoice)
        toolbar.addAction(new_invoice_action)
        
        toolbar.addSeparator()
        
        # حفظ الفاتورة الحالية
        save_action = QAction("💾 حفظ", self)
        save_action.triggered.connect(self.save_current_invoice)
        toolbar.addAction(save_action)
        
        # طباعة الفاتورة الحالية
        print_action = QAction("🖨️ طباعة", self)
        print_action.triggered.connect(self.print_current_invoice)
        toolbar.addAction(print_action)
        
        toolbar.addSeparator()
        
        # إغلاق الفاتورة الحالية
        close_action = QAction("❌ إغلاق الفاتورة", self)
        close_action.triggered.connect(self.close_current_invoice)
        toolbar.addAction(close_action)
        
        # إغلاق جميع الفواتير
        close_all_action = QAction("🚫 إغلاق الكل", self)
        close_all_action.triggered.connect(self.close_all_invoices)
        toolbar.addAction(close_all_action)
        
        self.addToolBar(toolbar)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = QStatusBar()
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #34495e;
                color: white;
                font-weight: bold;
                padding: 5px;
            }
        """)
        
        # عرض معلومات المستخدم
        user_label = QLabel(f"المستخدم: {self.user_data['full_name']}")
        user_label.setStyleSheet("color: white; margin-right: 20px;")
        self.status_bar.addPermanentWidget(user_label)
        
        # عرض عدد الفواتير المفتوحة
        self.invoices_count_label = QLabel("الفواتير المفتوحة: 0")
        self.invoices_count_label.setStyleSheet("color: white; margin-right: 20px;")
        self.status_bar.addPermanentWidget(self.invoices_count_label)
        
        self.setStatusBar(self.status_bar)
    
    def create_first_invoice(self):
        """إنشاء أول فاتورة"""
        self.create_new_invoice()
    
    def create_new_invoice(self):
        """إنشاء فاتورة جديدة"""
        try:
            # إنشاء معرف فريد للفاتورة
            invoice_id = str(uuid.uuid4())
            
            # إنشاء نافذة فاتورة جديدة
            invoice_dialog = SalesInvoiceDialog(
                self.db_manager, 
                self.user_data, 
                parent=self,
                multi_mode=True  # وضع الفواتير المتعددة
            )
            
            # تخصيص النافذة للعمل كتبويب
            invoice_widget = QWidget()
            layout = QVBoxLayout()
            
            # إضافة محتوى الفاتورة
            layout.addWidget(invoice_dialog)
            invoice_widget.setLayout(layout)
            
            # إضافة التبويب
            tab_title = f"فاتورة {self.invoice_counter}"
            tab_index = self.tabs.addTab(invoice_widget, tab_title)
            
            # حفظ مرجع الفاتورة
            self.active_invoices[invoice_id] = {
                'dialog': invoice_dialog,
                'widget': invoice_widget,
                'tab_index': tab_index,
                'title': tab_title,
                'counter': self.invoice_counter
            }
            
            # ربط إشارات الفاتورة
            invoice_dialog.invoice_saved.connect(lambda: self.on_invoice_saved(invoice_id))
            invoice_dialog.invoice_printed.connect(lambda: self.on_invoice_printed(invoice_id))
            
            # تحديد التبويب الجديد كنشط
            self.tabs.setCurrentIndex(tab_index)
            
            # تحديث العداد
            self.invoice_counter += 1
            
            # تحديث شريط الحالة
            self.update_status_bar()
            
            self.status_bar.showMessage(f"تم إنشاء {tab_title} جديدة", 3000)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في إنشاء فاتورة جديدة:\n{str(e)}")
    
    def close_invoice_tab(self, tab_index):
        """إغلاق تبويب فاتورة"""
        try:
            # البحث عن الفاتورة المطابقة
            invoice_to_remove = None
            for invoice_id, invoice_data in self.active_invoices.items():
                if invoice_data['tab_index'] == tab_index:
                    invoice_to_remove = invoice_id
                    break
            
            if invoice_to_remove:
                # التحقق من وجود تغييرات غير محفوظة
                invoice_dialog = self.active_invoices[invoice_to_remove]['dialog']
                if hasattr(invoice_dialog, 'invoice_items') and invoice_dialog.invoice_items:
                    reply = QMessageBox.question(
                        self, 
                        "تأكيد الإغلاق",
                        "هناك أصناف في الفاتورة. هل تريد إغلاقها بدون حفظ؟",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    )
                    if reply == QMessageBox.No:
                        return
                
                # إزالة التبويب
                self.tabs.removeTab(tab_index)
                
                # إزالة الفاتورة من القاموس
                del self.active_invoices[invoice_to_remove]
                
                # تحديث فهارس التبويبات
                self.update_tab_indices()
                
                # تحديث شريط الحالة
                self.update_status_bar()
                
                self.status_bar.showMessage("تم إغلاق الفاتورة", 2000)
                
                # إذا لم تعد هناك فواتير، أنشئ فاتورة جديدة
                if len(self.active_invoices) == 0:
                    self.create_new_invoice()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في إغلاق الفاتورة:\n{str(e)}")
    
    def update_tab_indices(self):
        """تحديث فهارس التبويبات"""
        for i in range(self.tabs.count()):
            for invoice_id, invoice_data in self.active_invoices.items():
                if invoice_data['widget'] == self.tabs.widget(i):
                    invoice_data['tab_index'] = i
                    break
    
    def save_current_invoice(self):
        """حفظ الفاتورة الحالية"""
        current_index = self.tabs.currentIndex()
        if current_index >= 0:
            for invoice_data in self.active_invoices.values():
                if invoice_data['tab_index'] == current_index:
                    invoice_data['dialog'].save_invoice()
                    break
    
    def print_current_invoice(self):
        """طباعة الفاتورة الحالية"""
        current_index = self.tabs.currentIndex()
        if current_index >= 0:
            for invoice_data in self.active_invoices.values():
                if invoice_data['tab_index'] == current_index:
                    if hasattr(invoice_data['dialog'], 'print_invoice'):
                        invoice_data['dialog'].print_invoice()
                    break
    
    def close_current_invoice(self):
        """إغلاق الفاتورة الحالية"""
        current_index = self.tabs.currentIndex()
        if current_index >= 0:
            self.close_invoice_tab(current_index)
    
    def close_all_invoices(self):
        """إغلاق جميع الفواتير"""
        reply = QMessageBox.question(
            self,
            "تأكيد الإغلاق",
            "هل تريد إغلاق جميع الفواتير؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # إغلاق جميع التبويبات
            while self.tabs.count() > 0:
                self.tabs.removeTab(0)
            
            # مسح جميع الفواتير
            self.active_invoices.clear()
            
            # إنشاء فاتورة جديدة
            self.create_new_invoice()
    
    def on_invoice_saved(self, invoice_id):
        """عند حفظ فاتورة"""
        if invoice_id in self.active_invoices:
            invoice_data = self.active_invoices[invoice_id]
            # تغيير لون التبويب للإشارة إلى الحفظ
            self.tabs.setTabText(invoice_data['tab_index'], f"✅ {invoice_data['title']}")
            self.status_bar.showMessage("تم حفظ الفاتورة بنجاح", 3000)
    
    def on_invoice_printed(self, invoice_id):
        """عند طباعة فاتورة"""
        if invoice_id in self.active_invoices:
            self.status_bar.showMessage("تم إرسال الفاتورة للطباعة", 3000)
    
    def update_status_bar(self):
        """تحديث شريط الحالة"""
        count = len(self.active_invoices)
        self.invoices_count_label.setText(f"الفواتير المفتوحة: {count}")
    
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        # التحقق من وجود فواتير غير محفوظة
        unsaved_invoices = []
        for invoice_id, invoice_data in self.active_invoices.items():
            dialog = invoice_data['dialog']
            if hasattr(dialog, 'invoice_items') and dialog.invoice_items:
                unsaved_invoices.append(invoice_data['title'])
        
        if unsaved_invoices:
            reply = QMessageBox.question(
                self,
                "تأكيد الإغلاق",
                f"هناك فواتير غير محفوظة:\n{', '.join(unsaved_invoices)}\n\nهل تريد الإغلاق بدون حفظ؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                event.ignore()
                return
        
        event.accept()
