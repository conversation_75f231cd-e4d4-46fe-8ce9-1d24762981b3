"""
نظام البحث المتقدم - Advanced Search System
بحث متقدم قابل للتخصيص لجميع أجزاء النظام
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QLineEdit, QComboBox, QDateEdit,
                             QDoubleSpinBox, QGroupBox, QGridLayout, QFrame,
                             QCheckBox, QButtonGroup, QRadioButton)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QIcon
from datetime import datetime, timedelta


class AdvancedSearchWidget(QWidget):
    """مكون البحث المتقدم"""
    
    # إشارة عند تغيير معايير البحث
    search_criteria_changed = pyqtSignal(dict)
    
    def __init__(self, search_type="customers", parent=None):
        super().__init__(parent)
        self.search_type = search_type
        self.search_criteria = {}
        
        self.init_ui()
        self.setup_search_fields()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin: 5px;
                padding: 10px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #2c3e50;
                font-size: 14px;
            }
            QLineEdit, QComboBox, QDateEdit, QDoubleSpinBox {
                padding: 5px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus, QDoubleSpinBox:focus {
                border-color: #3498db;
                outline: none;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 8px 15px;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
        layout = QVBoxLayout()
        
        # عنوان البحث المتقدم
        title_layout = QHBoxLayout()
        title_label = QLabel("🔍 البحث المتقدم")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        title_layout.addWidget(title_label)
        
        # زر إعادة تعيين
        self.reset_btn = QPushButton("🔄 إعادة تعيين")
        self.reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
                border: none;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        self.reset_btn.clicked.connect(self.reset_search)
        title_layout.addWidget(self.reset_btn)
        
        title_layout.addStretch()
        layout.addLayout(title_layout)
        
        # مجموعة معايير البحث
        self.search_group = QGroupBox("معايير البحث")
        self.search_layout = QGridLayout()
        self.search_group.setLayout(self.search_layout)
        layout.addWidget(self.search_group)
        
        # أزرار التحكم
        controls_layout = QHBoxLayout()
        
        self.search_btn = QPushButton("🔍 بحث")
        self.search_btn.clicked.connect(self.perform_search)
        controls_layout.addWidget(self.search_btn)
        
        self.clear_btn = QPushButton("🗑️ مسح النتائج")
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                padding: 8px 15px;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.clear_btn.clicked.connect(self.clear_search)
        controls_layout.addWidget(self.clear_btn)
        
        controls_layout.addStretch()
        layout.addLayout(controls_layout)
        
        # شريط النتائج
        self.results_label = QLabel("")
        self.results_label.setStyleSheet("color: #7f8c8d; font-style: italic; margin-top: 5px;")
        layout.addWidget(self.results_label)
        
        self.setLayout(layout)
    
    def setup_search_fields(self):
        """إعداد حقول البحث حسب النوع"""
        # مسح الحقول الموجودة
        for i in reversed(range(self.search_layout.count())):
            self.search_layout.itemAt(i).widget().setParent(None)
        
        if self.search_type == "customers":
            self.setup_customers_search()
        elif self.search_type == "invoices":
            self.setup_invoices_search()
        elif self.search_type == "products":
            self.setup_products_search()
        elif self.search_type == "suppliers":
            self.setup_suppliers_search()
    
    def setup_customers_search(self):
        """إعداد بحث العملاء"""
        row = 0
        
        # البحث بالاسم
        self.search_layout.addWidget(QLabel("اسم العميل:"), row, 0)
        self.customer_name_input = QLineEdit()
        self.customer_name_input.setPlaceholderText("ابحث بالاسم...")
        self.customer_name_input.textChanged.connect(self.on_search_changed)
        self.search_layout.addWidget(self.customer_name_input, row, 1)
        
        # البحث برقم الهاتف
        self.search_layout.addWidget(QLabel("رقم الهاتف:"), row, 2)
        self.customer_phone_input = QLineEdit()
        self.customer_phone_input.setPlaceholderText("ابحث برقم الهاتف...")
        self.customer_phone_input.textChanged.connect(self.on_search_changed)
        self.search_layout.addWidget(self.customer_phone_input, row, 3)
        
        row += 1
        
        # نوع العميل
        self.search_layout.addWidget(QLabel("نوع العميل:"), row, 0)
        self.customer_type_combo = QComboBox()
        self.customer_type_combo.addItem("الكل", "")
        self.customer_type_combo.addItem("عميل عادي", "regular")
        self.customer_type_combo.addItem("عميل مميز", "vip")
        self.customer_type_combo.addItem("تاجر جملة", "wholesale")
        self.customer_type_combo.currentTextChanged.connect(self.on_search_changed)
        self.search_layout.addWidget(self.customer_type_combo, row, 1)
        
        # البحث بالرصيد
        self.search_layout.addWidget(QLabel("الرصيد:"), row, 2)
        balance_layout = QHBoxLayout()
        
        self.balance_condition = QComboBox()
        self.balance_condition.addItems(["أي رصيد", "أكبر من", "أقل من", "يساوي", "بين"])
        self.balance_condition.currentTextChanged.connect(self.on_balance_condition_changed)
        balance_layout.addWidget(self.balance_condition)
        
        self.balance_value1 = QDoubleSpinBox()
        self.balance_value1.setRange(-999999, 999999)
        self.balance_value1.setDecimals(2)
        self.balance_value1.setSuffix(" جنيه")
        self.balance_value1.setEnabled(False)
        self.balance_value1.valueChanged.connect(self.on_search_changed)
        balance_layout.addWidget(self.balance_value1)
        
        self.balance_value2 = QDoubleSpinBox()
        self.balance_value2.setRange(-999999, 999999)
        self.balance_value2.setDecimals(2)
        self.balance_value2.setSuffix(" جنيه")
        self.balance_value2.setEnabled(False)
        self.balance_value2.setVisible(False)
        self.balance_value2.valueChanged.connect(self.on_search_changed)
        balance_layout.addWidget(self.balance_value2)
        
        balance_widget = QWidget()
        balance_widget.setLayout(balance_layout)
        self.search_layout.addWidget(balance_widget, row, 3)
    
    def setup_invoices_search(self):
        """إعداد بحث الفواتير"""
        row = 0
        
        # رقم الفاتورة
        self.search_layout.addWidget(QLabel("رقم الفاتورة:"), row, 0)
        self.invoice_number_input = QLineEdit()
        self.invoice_number_input.setPlaceholderText("ابحث برقم الفاتورة...")
        self.invoice_number_input.textChanged.connect(self.on_search_changed)
        self.search_layout.addWidget(self.invoice_number_input, row, 1)
        
        # اسم العميل
        self.search_layout.addWidget(QLabel("اسم العميل:"), row, 2)
        self.invoice_customer_input = QLineEdit()
        self.invoice_customer_input.setPlaceholderText("ابحث باسم العميل...")
        self.invoice_customer_input.textChanged.connect(self.on_search_changed)
        self.search_layout.addWidget(self.invoice_customer_input, row, 3)
        
        row += 1
        
        # فترة التاريخ
        self.search_layout.addWidget(QLabel("من تاريخ:"), row, 0)
        self.invoice_from_date = QDateEdit()
        self.invoice_from_date.setDate(QDate.currentDate().addMonths(-1))
        self.invoice_from_date.setCalendarPopup(True)
        self.invoice_from_date.dateChanged.connect(self.on_search_changed)
        self.search_layout.addWidget(self.invoice_from_date, row, 1)
        
        self.search_layout.addWidget(QLabel("إلى تاريخ:"), row, 2)
        self.invoice_to_date = QDateEdit()
        self.invoice_to_date.setDate(QDate.currentDate())
        self.invoice_to_date.setCalendarPopup(True)
        self.invoice_to_date.dateChanged.connect(self.on_search_changed)
        self.search_layout.addWidget(self.invoice_to_date, row, 3)
        
        row += 1
        
        # طريقة الدفع
        self.search_layout.addWidget(QLabel("طريقة الدفع:"), row, 0)
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItem("الكل", "")
        self.payment_method_combo.addItem("نقداً", "cash")
        self.payment_method_combo.addItem("آجل", "credit")
        self.payment_method_combo.addItem("تحويل بنكي", "bank_transfer")
        self.payment_method_combo.currentTextChanged.connect(self.on_search_changed)
        self.search_layout.addWidget(self.payment_method_combo, row, 1)
        
        # حالة الدفع
        self.search_layout.addWidget(QLabel("حالة الدفع:"), row, 2)
        self.payment_status_combo = QComboBox()
        self.payment_status_combo.addItem("الكل", "")
        self.payment_status_combo.addItem("مدفوعة", "paid")
        self.payment_status_combo.addItem("غير مدفوعة", "unpaid")
        self.payment_status_combo.addItem("مدفوعة جزئياً", "partial")
        self.payment_status_combo.currentTextChanged.connect(self.on_search_changed)
        self.search_layout.addWidget(self.payment_status_combo, row, 3)
        
        row += 1
        
        # نطاق المبلغ
        self.search_layout.addWidget(QLabel("المبلغ من:"), row, 0)
        self.amount_from = QDoubleSpinBox()
        self.amount_from.setRange(0, 999999)
        self.amount_from.setDecimals(2)
        self.amount_from.setSuffix(" جنيه")
        self.amount_from.valueChanged.connect(self.on_search_changed)
        self.search_layout.addWidget(self.amount_from, row, 1)
        
        self.search_layout.addWidget(QLabel("إلى:"), row, 2)
        self.amount_to = QDoubleSpinBox()
        self.amount_to.setRange(0, 999999)
        self.amount_to.setDecimals(2)
        self.amount_to.setSuffix(" جنيه")
        self.amount_to.setValue(999999)
        self.amount_to.valueChanged.connect(self.on_search_changed)
        self.search_layout.addWidget(self.amount_to, row, 3)
    
    def setup_products_search(self):
        """إعداد بحث المنتجات"""
        row = 0
        
        # اسم المنتج
        self.search_layout.addWidget(QLabel("اسم المنتج:"), row, 0)
        self.product_name_input = QLineEdit()
        self.product_name_input.setPlaceholderText("ابحث باسم المنتج...")
        self.product_name_input.textChanged.connect(self.on_search_changed)
        self.search_layout.addWidget(self.product_name_input, row, 1)
        
        # كود المنتج
        self.search_layout.addWidget(QLabel("كود المنتج:"), row, 2)
        self.product_code_input = QLineEdit()
        self.product_code_input.setPlaceholderText("ابحث بالكود...")
        self.product_code_input.textChanged.connect(self.on_search_changed)
        self.search_layout.addWidget(self.product_code_input, row, 3)
        
        row += 1
        
        # التصنيف
        self.search_layout.addWidget(QLabel("التصنيف:"), row, 0)
        self.product_category_input = QLineEdit()
        self.product_category_input.setPlaceholderText("ابحث بالتصنيف...")
        self.product_category_input.textChanged.connect(self.on_search_changed)
        self.search_layout.addWidget(self.product_category_input, row, 1)
        
        # نطاق السعر
        self.search_layout.addWidget(QLabel("السعر من:"), row, 2)
        price_layout = QHBoxLayout()
        
        self.price_from = QDoubleSpinBox()
        self.price_from.setRange(0, 999999)
        self.price_from.setDecimals(2)
        self.price_from.setSuffix(" جنيه")
        self.price_from.valueChanged.connect(self.on_search_changed)
        price_layout.addWidget(self.price_from)
        
        price_layout.addWidget(QLabel("إلى:"))
        
        self.price_to = QDoubleSpinBox()
        self.price_to.setRange(0, 999999)
        self.price_to.setDecimals(2)
        self.price_to.setSuffix(" جنيه")
        self.price_to.setValue(999999)
        self.price_to.valueChanged.connect(self.on_search_changed)
        price_layout.addWidget(self.price_to)
        
        price_widget = QWidget()
        price_widget.setLayout(price_layout)
        self.search_layout.addWidget(price_widget, row, 3)
        
        row += 1
        
        # حالة المخزون
        self.search_layout.addWidget(QLabel("حالة المخزون:"), row, 0)
        self.stock_status_combo = QComboBox()
        self.stock_status_combo.addItem("الكل", "")
        self.stock_status_combo.addItem("متوفر", "available")
        self.stock_status_combo.addItem("نفد المخزون", "out_of_stock")
        self.stock_status_combo.addItem("مخزون منخفض", "low_stock")
        self.stock_status_combo.currentTextChanged.connect(self.on_search_changed)
        self.search_layout.addWidget(self.stock_status_combo, row, 1)
        
        # نطاق الكمية
        self.search_layout.addWidget(QLabel("الكمية من:"), row, 2)
        quantity_layout = QHBoxLayout()
        
        self.quantity_from = QDoubleSpinBox()
        self.quantity_from.setRange(0, 999999)
        self.quantity_from.setDecimals(0)
        self.quantity_from.valueChanged.connect(self.on_search_changed)
        quantity_layout.addWidget(self.quantity_from)
        
        quantity_layout.addWidget(QLabel("إلى:"))
        
        self.quantity_to = QDoubleSpinBox()
        self.quantity_to.setRange(0, 999999)
        self.quantity_to.setDecimals(0)
        self.quantity_to.setValue(999999)
        self.quantity_to.valueChanged.connect(self.on_search_changed)
        quantity_layout.addWidget(self.quantity_to)
        
        quantity_widget = QWidget()
        quantity_widget.setLayout(quantity_layout)
        self.search_layout.addWidget(quantity_widget, row, 3)
    
    def setup_suppliers_search(self):
        """إعداد بحث الموردين"""
        row = 0
        
        # اسم المورد
        self.search_layout.addWidget(QLabel("اسم المورد:"), row, 0)
        self.supplier_name_input = QLineEdit()
        self.supplier_name_input.setPlaceholderText("ابحث باسم المورد...")
        self.supplier_name_input.textChanged.connect(self.on_search_changed)
        self.search_layout.addWidget(self.supplier_name_input, row, 1)
        
        # رقم الهاتف
        self.search_layout.addWidget(QLabel("رقم الهاتف:"), row, 2)
        self.supplier_phone_input = QLineEdit()
        self.supplier_phone_input.setPlaceholderText("ابحث برقم الهاتف...")
        self.supplier_phone_input.textChanged.connect(self.on_search_changed)
        self.search_layout.addWidget(self.supplier_phone_input, row, 3)

    def on_balance_condition_changed(self):
        """معالج تغيير شرط الرصيد"""
        condition = self.balance_condition.currentText()

        if condition == "أي رصيد":
            self.balance_value1.setEnabled(False)
            self.balance_value2.setEnabled(False)
            self.balance_value2.setVisible(False)
        elif condition in ["أكبر من", "أقل من", "يساوي"]:
            self.balance_value1.setEnabled(True)
            self.balance_value2.setEnabled(False)
            self.balance_value2.setVisible(False)
        elif condition == "بين":
            self.balance_value1.setEnabled(True)
            self.balance_value2.setEnabled(True)
            self.balance_value2.setVisible(True)

        self.on_search_changed()

    def on_search_changed(self):
        """معالج تغيير معايير البحث"""
        self.build_search_criteria()

    def build_search_criteria(self):
        """بناء معايير البحث"""
        self.search_criteria = {}

        if self.search_type == "customers":
            self.build_customers_criteria()
        elif self.search_type == "invoices":
            self.build_invoices_criteria()
        elif self.search_type == "products":
            self.build_products_criteria()
        elif self.search_type == "suppliers":
            self.build_suppliers_criteria()

    def build_customers_criteria(self):
        """بناء معايير بحث العملاء"""
        if hasattr(self, 'customer_name_input') and self.customer_name_input.text().strip():
            self.search_criteria['name'] = self.customer_name_input.text().strip()

        if hasattr(self, 'customer_phone_input') and self.customer_phone_input.text().strip():
            self.search_criteria['phone'] = self.customer_phone_input.text().strip()

        if hasattr(self, 'customer_type_combo') and self.customer_type_combo.currentData():
            self.search_criteria['customer_type'] = self.customer_type_combo.currentData()

        # معايير الرصيد
        if hasattr(self, 'balance_condition'):
            condition = self.balance_condition.currentText()
            if condition != "أي رصيد":
                self.search_criteria['balance_condition'] = condition
                self.search_criteria['balance_value1'] = self.balance_value1.value()
                if condition == "بين":
                    self.search_criteria['balance_value2'] = self.balance_value2.value()

    def build_invoices_criteria(self):
        """بناء معايير بحث الفواتير"""
        if hasattr(self, 'invoice_number_input') and self.invoice_number_input.text().strip():
            self.search_criteria['invoice_number'] = self.invoice_number_input.text().strip()

        if hasattr(self, 'invoice_customer_input') and self.invoice_customer_input.text().strip():
            self.search_criteria['customer_name'] = self.invoice_customer_input.text().strip()

        if hasattr(self, 'invoice_from_date'):
            self.search_criteria['from_date'] = self.invoice_from_date.date().toString('yyyy-MM-dd')

        if hasattr(self, 'invoice_to_date'):
            self.search_criteria['to_date'] = self.invoice_to_date.date().toString('yyyy-MM-dd')

        if hasattr(self, 'payment_method_combo') and self.payment_method_combo.currentData():
            self.search_criteria['payment_method'] = self.payment_method_combo.currentData()

        if hasattr(self, 'payment_status_combo') and self.payment_status_combo.currentData():
            self.search_criteria['payment_status'] = self.payment_status_combo.currentData()

        if hasattr(self, 'amount_from') and self.amount_from.value() > 0:
            self.search_criteria['amount_from'] = self.amount_from.value()

        if hasattr(self, 'amount_to') and self.amount_to.value() < 999999:
            self.search_criteria['amount_to'] = self.amount_to.value()

    def build_products_criteria(self):
        """بناء معايير بحث المنتجات"""
        if hasattr(self, 'product_name_input') and self.product_name_input.text().strip():
            self.search_criteria['name'] = self.product_name_input.text().strip()

        if hasattr(self, 'product_code_input') and self.product_code_input.text().strip():
            self.search_criteria['code'] = self.product_code_input.text().strip()

        if hasattr(self, 'product_category_input') and self.product_category_input.text().strip():
            self.search_criteria['category'] = self.product_category_input.text().strip()

        if hasattr(self, 'price_from') and self.price_from.value() > 0:
            self.search_criteria['price_from'] = self.price_from.value()

        if hasattr(self, 'price_to') and self.price_to.value() < 999999:
            self.search_criteria['price_to'] = self.price_to.value()

        if hasattr(self, 'stock_status_combo') and self.stock_status_combo.currentData():
            self.search_criteria['stock_status'] = self.stock_status_combo.currentData()

        if hasattr(self, 'quantity_from') and self.quantity_from.value() > 0:
            self.search_criteria['quantity_from'] = self.quantity_from.value()

        if hasattr(self, 'quantity_to') and self.quantity_to.value() < 999999:
            self.search_criteria['quantity_to'] = self.quantity_to.value()

    def build_suppliers_criteria(self):
        """بناء معايير بحث الموردين"""
        if hasattr(self, 'supplier_name_input') and self.supplier_name_input.text().strip():
            self.search_criteria['name'] = self.supplier_name_input.text().strip()

        if hasattr(self, 'supplier_phone_input') and self.supplier_phone_input.text().strip():
            self.search_criteria['phone'] = self.supplier_phone_input.text().strip()

    def perform_search(self):
        """تنفيذ البحث"""
        self.build_search_criteria()
        self.search_criteria_changed.emit(self.search_criteria)

        # تحديث شريط النتائج
        criteria_count = len([k for k, v in self.search_criteria.items() if v])
        if criteria_count > 0:
            self.results_label.setText(f"🔍 تم تطبيق {criteria_count} معيار بحث")
        else:
            self.results_label.setText("📋 عرض جميع النتائج")

    def clear_search(self):
        """مسح البحث"""
        self.search_criteria = {}
        self.search_criteria_changed.emit({})
        self.results_label.setText("🗑️ تم مسح معايير البحث")

    def reset_search(self):
        """إعادة تعيين البحث"""
        # إعادة تعيين جميع الحقول
        if self.search_type == "customers":
            self.reset_customers_fields()
        elif self.search_type == "invoices":
            self.reset_invoices_fields()
        elif self.search_type == "products":
            self.reset_products_fields()
        elif self.search_type == "suppliers":
            self.reset_suppliers_fields()

        self.clear_search()

    def reset_customers_fields(self):
        """إعادة تعيين حقول العملاء"""
        if hasattr(self, 'customer_name_input'):
            self.customer_name_input.clear()
        if hasattr(self, 'customer_phone_input'):
            self.customer_phone_input.clear()
        if hasattr(self, 'customer_type_combo'):
            self.customer_type_combo.setCurrentIndex(0)
        if hasattr(self, 'balance_condition'):
            self.balance_condition.setCurrentIndex(0)
            self.balance_value1.setValue(0)
            self.balance_value2.setValue(0)

    def reset_invoices_fields(self):
        """إعادة تعيين حقول الفواتير"""
        if hasattr(self, 'invoice_number_input'):
            self.invoice_number_input.clear()
        if hasattr(self, 'invoice_customer_input'):
            self.invoice_customer_input.clear()
        if hasattr(self, 'invoice_from_date'):
            self.invoice_from_date.setDate(QDate.currentDate().addMonths(-1))
        if hasattr(self, 'invoice_to_date'):
            self.invoice_to_date.setDate(QDate.currentDate())
        if hasattr(self, 'payment_method_combo'):
            self.payment_method_combo.setCurrentIndex(0)
        if hasattr(self, 'payment_status_combo'):
            self.payment_status_combo.setCurrentIndex(0)
        if hasattr(self, 'amount_from'):
            self.amount_from.setValue(0)
        if hasattr(self, 'amount_to'):
            self.amount_to.setValue(999999)

    def reset_products_fields(self):
        """إعادة تعيين حقول المنتجات"""
        if hasattr(self, 'product_name_input'):
            self.product_name_input.clear()
        if hasattr(self, 'product_code_input'):
            self.product_code_input.clear()
        if hasattr(self, 'product_category_input'):
            self.product_category_input.clear()
        if hasattr(self, 'price_from'):
            self.price_from.setValue(0)
        if hasattr(self, 'price_to'):
            self.price_to.setValue(999999)
        if hasattr(self, 'stock_status_combo'):
            self.stock_status_combo.setCurrentIndex(0)
        if hasattr(self, 'quantity_from'):
            self.quantity_from.setValue(0)
        if hasattr(self, 'quantity_to'):
            self.quantity_to.setValue(999999)

    def reset_suppliers_fields(self):
        """إعادة تعيين حقول الموردين"""
        if hasattr(self, 'supplier_name_input'):
            self.supplier_name_input.clear()
        if hasattr(self, 'supplier_phone_input'):
            self.supplier_phone_input.clear()

    def get_search_criteria(self):
        """الحصول على معايير البحث الحالية"""
        return self.search_criteria.copy()

    def set_search_type(self, search_type):
        """تغيير نوع البحث"""
        self.search_type = search_type
        self.setup_search_fields()
        self.clear_search()
