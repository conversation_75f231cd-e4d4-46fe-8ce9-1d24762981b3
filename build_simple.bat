@echo off
chcp 65001 > nul
title بناء سريع - Quick Build

echo ════════════════════════════════════════════════════════════════
echo                    بناء سريع للتطبيق
echo                     Quick Build
echo ════════════════════════════════════════════════════════════════
echo.

echo [1/4] تنظيف الملفات السابقة...
echo [1/4] Cleaning previous builds...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

echo.
echo [2/4] تثبيت PyInstaller...
echo [2/4] Installing PyInstaller...
pip install pyinstaller

echo.
echo [3/4] بناء التطبيق...
echo [3/4] Building application...
pyinstaller --clean pos_system.spec

echo.
echo [4/4] التحقق من النتيجة...
echo [4/4] Checking result...

if exist "dist\POS_System.exe" (
    echo.
    echo ✅ تم بناء التطبيق بنجاح!
    echo ✅ Build successful!
    echo.
    echo 📁 الملف: dist\POS_System.exe
    echo 📁 File: dist\POS_System.exe
    echo.
    dir "dist\POS_System.exe"
    echo.
    echo 🚀 تشغيل التطبيق؟ [Y/N]
    set /p choice=Run application? [Y/N]: 
    if /i "%choice%"=="Y" (
        echo تشغيل التطبيق...
        start "POS System" "dist\POS_System.exe"
    )
) else (
    echo.
    echo ❌ فشل في البناء!
    echo ❌ Build failed!
)

echo.
pause