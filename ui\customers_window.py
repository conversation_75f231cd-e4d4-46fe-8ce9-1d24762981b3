#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة إدارة العملاء - نظام نقاط البيع والمحاسبة
Customers Management Window - POS and Accounting System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QComboBox, QDialog, QFormLayout, 
                            QMessageBox, QHeaderView, QAbstractItemView,
                            QFrame, QGroupBox, QTextEdit, QDoubleSpinBox,
                            QTabWidget, QListWidget, QSplitter)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from utils.config import Config
from ui.customer_details_window import CustomerDetailsWindow
# from ui.simple_search import SimpleSearchWidget  # تعطيل مؤقت
from datetime import datetime

class CustomersWindow(QWidget):
    """نافذة إدارة العملاء"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        self.load_customers()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة العملاء")
        self.setGeometry(100, 100, 1200, 700)

        # تطبيق التصميم العصري
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                font-family: 'Segoe UI', Arial, sans-serif;
                color: #2c3e50;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f1f3f4);
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px 15px;
                font-size: 13px;
                font-weight: 500;
                color: #2c3e50;
                min-height: 25px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-color: #667eea;
                color: white;
            }
            QTableWidget {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f0f0f0;
                selection-background-color: #667eea;
            }
            QTableWidget::item:selected {
                background-color: #667eea;
                color: white;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: black;
                padding: 10px;
                border: none;
                font-weight: 600;
            }
            QLineEdit {
                background-color: white;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
            }
            QLineEdit:focus {
                border-color: #667eea;
                outline: none;
            }
        """)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        add_btn = QPushButton("إضافة عميل جديد")
        add_btn.clicked.connect(self.add_customer)
        toolbar_layout.addWidget(add_btn)
        
        edit_btn = QPushButton("تعديل العميل")
        edit_btn.clicked.connect(self.edit_customer)
        toolbar_layout.addWidget(edit_btn)
        
        delete_btn = QPushButton("حذف العميل")
        delete_btn.clicked.connect(self.delete_customer)
        toolbar_layout.addWidget(delete_btn)
        
        view_profile_btn = QPushButton("عرض الملف الشخصي")
        view_profile_btn.clicked.connect(self.view_customer_profile)
        toolbar_layout.addWidget(view_profile_btn)

        # زر تفاصيل العميل الشاملة
        customer_details_btn = QPushButton("📊 تفاصيل العميل الشاملة")
        customer_details_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 8px 15px;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        customer_details_btn.clicked.connect(self.show_customer_details)
        toolbar_layout.addWidget(customer_details_btn)

        toolbar_layout.addStretch()
        
        # البحث
        search_label = QLabel("البحث:")
        toolbar_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث بالاسم أو الهاتف...")
        self.search_input.textChanged.connect(self.search_customers)
        toolbar_layout.addWidget(self.search_input)
        
        # فلتر نوع العميل
        type_label = QLabel("النوع:")
        toolbar_layout.addWidget(type_label)
        
        self.type_filter = QComboBox()
        self.type_filter.addItem("الكل", "")
        for key, value in Config.CUSTOMER_TYPES.items():
            self.type_filter.addItem(value, key)
        self.type_filter.currentTextChanged.connect(self.filter_customers)
        toolbar_layout.addWidget(self.type_filter)

        main_layout.addLayout(toolbar_layout)
        
        # جدول العملاء
        self.customers_table = QTableWidget()
        self.customers_table.setColumnCount(7)
        self.customers_table.setHorizontalHeaderLabels([
            "الرقم", "اسم العميل", "الهاتف", "العنوان", 
            "نوع العميل", "الرصيد الحالي", "حد الائتمان"
        ])
        
        # تنسيق الجدول
        header = self.customers_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.customers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.customers_table.setAlternatingRowColors(True)
        self.customers_table.doubleClicked.connect(self.view_customer_profile)
        
        main_layout.addWidget(self.customers_table)
        
        # شريط الحالة
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("جاري التحميل...")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # إحصائيات سريعة
        self.stats_label = QLabel()
        self.stats_label.setStyleSheet("color: blue; font-weight: bold;")
        status_layout.addWidget(self.stats_label)
        
        main_layout.addLayout(status_layout)
        
        self.setLayout(main_layout)
        
    def load_customers(self):
        """تحميل العملاء من قاعدة البيانات"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM customers 
            WHERE is_active = 1
            ORDER BY name
        ''')
        
        customers = cursor.fetchall()
        conn.close()
        
        # تحديث الجدول
        self.customers_table.setRowCount(len(customers))

        total_balance = 0

        for row, customer in enumerate(customers):
            self.customers_table.setItem(row, 0, QTableWidgetItem(str(customer['id'])))
            self.customers_table.setItem(row, 1, QTableWidgetItem(customer['name']))
            self.customers_table.setItem(row, 2, QTableWidgetItem(customer['phone'] or ''))
            self.customers_table.setItem(row, 3, QTableWidgetItem(customer['address'] or ''))

            customer_type = Config.CUSTOMER_TYPES.get(customer['customer_type'], customer['customer_type'])
            self.customers_table.setItem(row, 4, QTableWidgetItem(customer_type))

            # الحصول على الرصيد من آخر فاتورة
            last_balance = self.db_manager.get_customer_last_invoice_balance(customer['id'])

            # تلوين الرصيد
            balance_item = QTableWidgetItem(f"{last_balance:.2f}")
            if last_balance > 0:
                balance_item.setBackground(QColor(255, 200, 200))  # أحمر للمديونية
            elif last_balance < 0:
                balance_item.setBackground(QColor(200, 255, 200))  # أخضر للرصيد الموجب
            self.customers_table.setItem(row, 5, balance_item)

            self.customers_table.setItem(row, 6, QTableWidgetItem(f"{customer['credit_limit']:.2f}"))

            total_balance += last_balance
            
        # تحديث شريط الحالة
        self.status_label.setText(f"إجمالي العملاء: {len(customers)}")
        self.stats_label.setText(f"إجمالي المديونيات: {total_balance:.2f} جنيه")
        
    def search_customers(self):
        """البحث في العملاء"""
        search_text = self.search_input.text().lower()
        
        for row in range(self.customers_table.rowCount()):
            show_row = False
            
            # البحث في الاسم والهاتف
            name_item = self.customers_table.item(row, 1)
            phone_item = self.customers_table.item(row, 2)
            
            if name_item and search_text in name_item.text().lower():
                show_row = True
            elif phone_item and search_text in phone_item.text().lower():
                show_row = True
                
            self.customers_table.setRowHidden(row, not show_row)
            
    def filter_customers(self):
        """فلترة العملاء حسب النوع"""
        selected_type = self.type_filter.currentData()
        
        for row in range(self.customers_table.rowCount()):
            if not selected_type:  # عرض الكل
                self.customers_table.setRowHidden(row, False)
            else:
                type_item = self.customers_table.item(row, 4)
                customer_type_text = Config.CUSTOMER_TYPES.get(selected_type, selected_type)
                show_row = type_item and type_item.text() == customer_type_text
                self.customers_table.setRowHidden(row, not show_row)
                
    def add_customer(self):
        """إضافة عميل جديد"""
        dialog = CustomerDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_customers()
            
    def edit_customer(self):
        """تعديل عميل"""
        current_row = self.customers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للتعديل")
            return
            
        customer_id = int(self.customers_table.item(current_row, 0).text())
        dialog = CustomerDialog(self.db_manager, customer_id, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_customers()
            
    def delete_customer(self):
        """حذف عميل"""
        current_row = self.customers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للحذف")
            return
            
        customer_name = self.customers_table.item(current_row, 1).text()
        
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   f"هل تريد حذف العميل '{customer_name}'؟\n"
                                   "سيتم إلغاء تفعيل العميل فقط.",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            customer_id = int(self.customers_table.item(current_row, 0).text())
            
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE customers 
                SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (customer_id,))
            
            conn.commit()
            conn.close()
            
            self.load_customers()
            QMessageBox.information(self, "نجح", "تم حذف العميل بنجاح")
            
    def view_customer_profile(self):
        """عرض الملف الشخصي للعميل"""
        current_row = self.customers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل لعرض ملفه")
            return
            
        customer_id = int(self.customers_table.item(current_row, 0).text())
        dialog = CustomerProfileDialog(self.db_manager, customer_id, parent=self)
        dialog.exec_()

    def show_customer_details(self):
        """عرض تفاصيل العميل الشاملة"""
        current_row = self.customers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل لعرض تفاصيله")
            return

        try:
            customer_id = int(self.customers_table.item(current_row, 0).text())
            customer_name = self.customers_table.item(current_row, 1).text()

            # فتح نافذة تفاصيل العميل الشاملة
            self.customer_details_window = CustomerDetailsWindow(
                self.db_manager,
                customer_id,
                customer_name,
                parent=self
            )
            self.customer_details_window.show()

        except Exception as e:
            print(f"خطأ في فتح تفاصيل العميل: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في فتح تفاصيل العميل:\n{str(e)}")

    def apply_advanced_search(self, criteria):
        """تطبيق البحث المتقدم"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # بناء الاستعلام
            query = """
                SELECT id, name, phone, address, customer_type, credit_limit
                FROM customers
                WHERE is_active = 1
            """
            params = []

            # إضافة شروط البحث
            if 'name' in criteria:
                query += " AND name LIKE ?"
                params.append(f"%{criteria['name']}%")

            if 'phone' in criteria:
                query += " AND phone LIKE ?"
                params.append(f"%{criteria['phone']}%")

            query += " ORDER BY name"

            cursor.execute(query, params)
            customers = cursor.fetchall()

            # تطبيق شروط الرصيد (بعد الاستعلام)
            if 'balance_min' in criteria or 'balance_max' in criteria:
                filtered_customers = []
                balance_min = criteria.get('balance_min', -999999)
                balance_max = criteria.get('balance_max', 999999)

                for customer in customers:
                    balance = self.db_manager.get_customer_last_invoice_balance(customer['id'])

                    if balance_min <= balance <= balance_max:
                        filtered_customers.append(customer)

                customers = filtered_customers

            # تحديث الجدول
            self.update_customers_table(customers)

            conn.close()

        except Exception as e:
            print(f"خطأ في البحث المتقدم: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في البحث المتقدم:\n{str(e)}")

    def update_customers_table(self, customers):
        """تحديث جدول العملاء"""
        self.customers_table.setRowCount(len(customers))

        total_balance = 0

        for row, customer in enumerate(customers):
            self.customers_table.setItem(row, 0, QTableWidgetItem(str(customer['id'])))
            self.customers_table.setItem(row, 1, QTableWidgetItem(customer['name']))
            self.customers_table.setItem(row, 2, QTableWidgetItem(customer['phone'] or ''))
            self.customers_table.setItem(row, 3, QTableWidgetItem(customer['address'] or ''))

            customer_type = Config.CUSTOMER_TYPES.get(customer['customer_type'], customer['customer_type'])
            self.customers_table.setItem(row, 4, QTableWidgetItem(customer_type))

            # الحصول على الرصيد من آخر فاتورة
            last_balance = self.db_manager.get_customer_last_invoice_balance(customer['id'])

            # تلوين الرصيد
            balance_item = QTableWidgetItem(f"{last_balance:.2f}")
            if last_balance > 0:
                balance_item.setBackground(QColor(255, 200, 200))  # أحمر للمديونية
            elif last_balance < 0:
                balance_item.setBackground(QColor(200, 255, 200))  # أخضر للرصيد الموجب
            self.customers_table.setItem(row, 5, balance_item)

            self.customers_table.setItem(row, 6, QTableWidgetItem(f"{customer['credit_limit']:.2f}"))

            total_balance += last_balance

        # تحديث شريط الحالة
        self.status_label.setText(f"إجمالي العملاء: {len(customers)}")
        self.balance_label.setText(f"إجمالي الأرصدة: {total_balance:.2f} جنيه")


class CustomerDialog(QDialog):
    """نافذة حوار إضافة/تعديل العملاء"""

    def __init__(self, db_manager, customer_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.customer_id = customer_id
        self.init_ui()

        if customer_id:
            self.load_customer_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        title = "تعديل العميل" if self.customer_id else "إضافة عميل جديد"
        self.setWindowTitle(title)
        self.setFixedSize(500, 400)

        layout = QVBoxLayout()

        # معلومات أساسية
        basic_group = QGroupBox("المعلومات الأساسية")
        basic_layout = QFormLayout()

        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("اسم العميل")
        basic_layout.addRow("اسم العميل *:", self.name_input)

        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("رقم الهاتف")
        basic_layout.addRow("رقم الهاتف:", self.phone_input)

        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("البريد الإلكتروني")
        basic_layout.addRow("البريد الإلكتروني:", self.email_input)

        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(80)
        self.address_input.setPlaceholderText("العنوان")
        basic_layout.addRow("العنوان:", self.address_input)

        basic_group.setLayout(basic_layout)
        layout.addWidget(basic_group)

        # إعدادات التجارة
        trade_group = QGroupBox("إعدادات التجارة")
        trade_layout = QFormLayout()

        self.customer_type_combo = QComboBox()
        for key, value in Config.CUSTOMER_TYPES.items():
            self.customer_type_combo.addItem(value, key)
        trade_layout.addRow("نوع العميل:", self.customer_type_combo)

        self.credit_limit_input = QDoubleSpinBox()
        self.credit_limit_input.setMaximum(999999.99)
        self.credit_limit_input.setDecimals(2)
        self.credit_limit_input.setSuffix(" جنيه")
        trade_layout.addRow("حد الائتمان:", self.credit_limit_input)

        trade_group.setLayout(trade_layout)
        layout.addWidget(trade_group)

        # الأزرار
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("حفظ")
        save_btn.clicked.connect(self.save_customer)
        buttons_layout.addWidget(save_btn)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

        self.setLayout(layout)

    def load_customer_data(self):
        """تحميل بيانات العميل للتعديل"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM customers WHERE id = ?', (self.customer_id,))
        customer = cursor.fetchone()
        conn.close()

        if customer:
            self.name_input.setText(customer['name'])
            self.phone_input.setText(customer['phone'] or '')
            self.email_input.setText(customer['email'] or '')
            self.address_input.setPlainText(customer['address'] or '')

            # تحديد نوع العميل
            for i in range(self.customer_type_combo.count()):
                if self.customer_type_combo.itemData(i) == customer['customer_type']:
                    self.customer_type_combo.setCurrentIndex(i)
                    break

            self.credit_limit_input.setValue(customer['credit_limit'])

    def save_customer(self):
        """حفظ العميل"""
        # التحقق من البيانات المطلوبة
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم العميل")
            return

        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        try:
            if self.customer_id:
                # تحديث عميل موجود
                cursor.execute('''
                    UPDATE customers
                    SET name=?, phone=?, email=?, address=?, customer_type=?,
                        credit_limit=?, updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                ''', (
                    self.name_input.text().strip(),
                    self.phone_input.text().strip() or None,
                    self.email_input.text().strip() or None,
                    self.address_input.toPlainText().strip() or None,
                    self.customer_type_combo.currentData(),
                    self.credit_limit_input.value(),
                    self.customer_id
                ))
            else:
                # إضافة عميل جديد
                cursor.execute('''
                    INSERT INTO customers
                    (name, phone, email, address, customer_type, credit_limit)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    self.name_input.text().strip(),
                    self.phone_input.text().strip() or None,
                    self.email_input.text().strip() or None,
                    self.address_input.toPlainText().strip() or None,
                    self.customer_type_combo.currentData(),
                    self.credit_limit_input.value()
                ))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم حفظ العميل بنجاح")
            self.accept()

        except Exception as e:
            conn.close()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ العميل:\n{str(e)}")


class CustomerProfileDialog(QDialog):
    """نافذة الملف الشخصي للعميل"""

    def __init__(self, db_manager, customer_id, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.customer_id = customer_id
        self.init_ui()
        self.load_customer_profile()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("الملف الشخصي للعميل")
        self.setGeometry(200, 200, 800, 600)

        layout = QVBoxLayout()

        # معلومات العميل الأساسية
        self.customer_info_label = QLabel()
        self.customer_info_label.setStyleSheet("""
            background-color: #f0f0f0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-weight: bold;
        """)
        layout.addWidget(self.customer_info_label)

        # التبويبات
        tabs = QTabWidget()

        # تبويب الفواتير
        invoices_tab = QWidget()
        invoices_layout = QVBoxLayout()

        self.invoices_table = QTableWidget()
        self.invoices_table.setColumnCount(6)
        self.invoices_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "التاريخ", "المبلغ الإجمالي",
            "المدفوع", "المتبقي", "الحالة"
        ])

        invoices_layout.addWidget(self.invoices_table)
        invoices_tab.setLayout(invoices_layout)
        tabs.addTab(invoices_tab, "الفواتير")

        # تبويب المدفوعات
        payments_tab = QWidget()
        payments_layout = QVBoxLayout()

        self.payments_table = QTableWidget()
        self.payments_table.setColumnCount(4)
        self.payments_table.setHorizontalHeaderLabels([
            "التاريخ", "المبلغ", "طريقة الدفع", "ملاحظات"
        ])

        payments_layout.addWidget(self.payments_table)
        payments_tab.setLayout(payments_layout)
        tabs.addTab(payments_tab, "المدفوعات")

        layout.addWidget(tabs)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        export_btn = QPushButton("تصدير PDF")
        export_btn.clicked.connect(self.export_profile)
        buttons_layout.addWidget(export_btn)

        buttons_layout.addStretch()

        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)

        self.setLayout(layout)

    def load_customer_profile(self):
        """تحميل الملف الشخصي للعميل"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        # معلومات العميل
        cursor.execute('SELECT * FROM customers WHERE id = ?', (self.customer_id,))
        customer = cursor.fetchone()

        if customer:
            customer_type = Config.CUSTOMER_TYPES.get(customer['customer_type'], customer['customer_type'])
            # الحصول على الرصيد من آخر فاتورة
            last_balance = self.db_manager.get_customer_last_invoice_balance(self.customer_id)

            info_text = f"""
            اسم العميل: {customer['name']}
            الهاتف: {customer['phone'] or 'غير محدد'}
            العنوان: {customer['address'] or 'غير محدد'}
            نوع العميل: {customer_type}
            الرصيد من آخر فاتورة: {last_balance:.2f} جنيه
            حد الائتمان: {customer['credit_limit']:.2f} جنيه
            """
            self.customer_info_label.setText(info_text)

        # الفواتير
        cursor.execute('''
            SELECT invoice_number, invoice_date, final_amount, paid_amount, remaining_amount
            FROM invoices
            WHERE customer_id = ? AND invoice_type = 'sale' AND is_active = 1
            ORDER BY invoice_date DESC
        ''', (self.customer_id,))

        invoices = cursor.fetchall()
        self.invoices_table.setRowCount(len(invoices))

        for row, invoice in enumerate(invoices):
            self.invoices_table.setItem(row, 0, QTableWidgetItem(invoice['invoice_number']))
            self.invoices_table.setItem(row, 1, QTableWidgetItem(invoice['invoice_date']))
            self.invoices_table.setItem(row, 2, QTableWidgetItem(f"{invoice['final_amount']:.2f}"))
            self.invoices_table.setItem(row, 3, QTableWidgetItem(f"{invoice['paid_amount']:.2f}"))
            self.invoices_table.setItem(row, 4, QTableWidgetItem(f"{invoice['remaining_amount']:.2f}"))

            status = "مدفوعة" if invoice['remaining_amount'] == 0 else "غير مدفوعة"
            self.invoices_table.setItem(row, 5, QTableWidgetItem(status))

        # المدفوعات
        cursor.execute('''
            SELECT payment_date, amount, payment_method, notes
            FROM payments
            WHERE customer_id = ?
            ORDER BY payment_date DESC
        ''', (self.customer_id,))

        payments = cursor.fetchall()
        self.payments_table.setRowCount(len(payments))

        for row, payment in enumerate(payments):
            self.payments_table.setItem(row, 0, QTableWidgetItem(payment['payment_date']))
            self.payments_table.setItem(row, 1, QTableWidgetItem(f"{payment['amount']:.2f}"))

            payment_method = Config.PAYMENT_METHODS.get(payment['payment_method'], payment['payment_method'])
            self.payments_table.setItem(row, 2, QTableWidgetItem(payment_method))
            self.payments_table.setItem(row, 3, QTableWidgetItem(payment['notes'] or ''))

        conn.close()

    def export_profile(self):
        """تصدير قائمة العملاء إلى CSV"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import csv
            from datetime import datetime
            
            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, 
                "حفظ قائمة العملاء", 
                f"قائمة_العملاء_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv);;All Files (*)"
            )
            
            if file_path:
                # جمع البيانات من الجدول
                table = self.customers_table
                data = []
                
                # إضافة العناوين
                headers = []
                for col in range(table.columnCount()):
                    headers.append(table.horizontalHeaderItem(col).text())
                data.append(headers)
                
                # إضافة البيانات
                for row in range(table.rowCount()):
                    if not table.isRowHidden(row):  # فقط الصفوف المرئية
                        row_data = []
                        for col in range(table.columnCount()):
                            item = table.item(row, col)
                            row_data.append(item.text() if item else '')
                        data.append(row_data)
                
                # كتابة الملف
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as file:
                    writer = csv.writer(file)
                    writer.writerows(data)
                
                QMessageBox.information(self, "نجح", f"تم تصدير قائمة العملاء بنجاح إلى:\n{file_path}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير قائمة العملاء:\n{str(e)}")
