#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة إدارة المستخدمين - نظام نقاط البيع والمحاسبة
Users Management Window - POS and Accounting System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QComboBox, QDialog, QFormLayout, 
                            QMessageBox, QHeaderView, QAbstractItemView,
                            QFrame, QGroupBox, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from utils.config import Config
from datetime import datetime
from ui.company_info_dialog import CompanyInfoDialog

class UsersWindow(QWidget):
    """نافذة إدارة المستخدمين"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        self.load_users()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة المستخدمين")
        self.setGeometry(100, 100, 1000, 600)
        
        # تطبيق التصميم العصري
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                font-family: 'Segoe UI', Arial, sans-serif;
                color: #2c3e50;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f1f3f4);
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 8px 16px;
                font-size: 13px;
                font-weight: 500;
                color: #2c3e50;
                min-height: 25px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-color: #667eea;
                color: white;
            }
            QTableWidget {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f0f0f0;
                selection-background-color: #667eea;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                padding: 10px;
                border: none;
                font-weight: 600;
            }
            QLineEdit {
                background-color: white;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
            }
            QLineEdit:focus {
                border-color: #667eea;
                outline: none;
            }
        """)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        add_btn = QPushButton("إضافة مستخدم جديد")
        add_btn.clicked.connect(self.add_user)
        toolbar_layout.addWidget(add_btn)
        
        edit_btn = QPushButton("تعديل المستخدم")
        edit_btn.clicked.connect(self.edit_user)
        toolbar_layout.addWidget(edit_btn)
        
        change_password_btn = QPushButton("تغيير كلمة المرور")
        change_password_btn.clicked.connect(self.change_password)
        toolbar_layout.addWidget(change_password_btn)
        
        toggle_status_btn = QPushButton("تفعيل/إلغاء تفعيل")
        toggle_status_btn.clicked.connect(self.toggle_user_status)
        toolbar_layout.addWidget(toggle_status_btn)
        
        # زر بيانات الشركة
        company_info_btn = QPushButton("🏢 بيانات الشركة")
        company_info_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #1e7e34);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1e7e34, stop:1 #155724);
            }
        """)
        company_info_btn.clicked.connect(self.show_company_info)
        toolbar_layout.addWidget(company_info_btn)
        
        toolbar_layout.addStretch()
        
        # البحث
        search_label = QLabel("البحث:")
        toolbar_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("اسم المستخدم أو الاسم الكامل...")
        self.search_input.textChanged.connect(self.search_users)
        toolbar_layout.addWidget(self.search_input)
        
        main_layout.addLayout(toolbar_layout)
        
        # جدول المستخدمين
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(6)
        self.users_table.setHorizontalHeaderLabels([
            "اسم المستخدم", "الاسم الكامل", "الصلاحية", "الهاتف", "الحالة", "تاريخ الإنشاء"
        ])
        
        # تنسيق الجدول
        header = self.users_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.users_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.users_table.setAlternatingRowColors(True)
        self.users_table.doubleClicked.connect(self.edit_user)
        
        main_layout.addWidget(self.users_table)
        
        # شريط الحالة
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("جاري التحميل...")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # إحصائيات سريعة
        self.stats_label = QLabel()
        self.stats_label.setStyleSheet("color: blue; font-weight: bold;")
        status_layout.addWidget(self.stats_label)
        
        main_layout.addLayout(status_layout)
        
        self.setLayout(main_layout)
        
    def load_users(self):
        """تحميل المستخدمين من قاعدة البيانات"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM users 
            ORDER BY created_at DESC
        ''')
        
        users = cursor.fetchall()
        conn.close()
        
        # تحديث الجدول
        self.users_table.setRowCount(len(users))
        
        active_count = 0
        admin_count = 0
        
        for row, user in enumerate(users):
            self.users_table.setItem(row, 0, QTableWidgetItem(user['username']))
            self.users_table.setItem(row, 1, QTableWidgetItem(user['full_name']))
            
            # الصلاحية
            role_text = "مدير" if user['role'] == 'admin' else "كاشير"
            role_item = QTableWidgetItem(role_text)
            if user['role'] == 'admin':
                role_item.setBackground(QColor(200, 255, 200))
                admin_count += 1
            self.users_table.setItem(row, 2, role_item)
            
            self.users_table.setItem(row, 3, QTableWidgetItem(user['phone'] or ''))
            
            # الحالة
            status_text = "نشط" if user['is_active'] else "غير نشط"
            status_item = QTableWidgetItem(status_text)
            if user['is_active']:
                status_item.setBackground(QColor(200, 255, 200))
                active_count += 1
            else:
                status_item.setBackground(QColor(255, 200, 200))
            self.users_table.setItem(row, 4, status_item)
            
            self.users_table.setItem(row, 5, QTableWidgetItem(user['created_at']))
            
        # تحديث شريط الحالة
        self.status_label.setText(f"إجمالي المستخدمين: {len(users)}")
        self.stats_label.setText(f"النشطين: {active_count} | المديرين: {admin_count}")
        
    def search_users(self):
        """البحث في المستخدمين"""
        search_text = self.search_input.text().lower()
        
        for row in range(self.users_table.rowCount()):
            show_row = False
            
            # البحث في اسم المستخدم والاسم الكامل
            username_item = self.users_table.item(row, 0)
            fullname_item = self.users_table.item(row, 1)
            
            if username_item and search_text in username_item.text().lower():
                show_row = True
            elif fullname_item and search_text in fullname_item.text().lower():
                show_row = True
                
            self.users_table.setRowHidden(row, not show_row)
            
    def add_user(self):
        """إضافة مستخدم جديد"""
        dialog = UserDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_users()
            
    def edit_user(self):
        """تعديل مستخدم"""
        current_row = self.users_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم للتعديل")
            return
            
        username = self.users_table.item(current_row, 0).text()
        
        # البحث عن المستخدم
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
        user = cursor.fetchone()
        conn.close()
        
        if user:
            dialog = UserDialog(self.db_manager, user['id'], parent=self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_users()
                
    def change_password(self):
        """تغيير كلمة المرور"""
        current_row = self.users_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم لتغيير كلمة المرور")
            return
            
        username = self.users_table.item(current_row, 0).text()
        
        # البحث عن المستخدم
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
        user = cursor.fetchone()
        conn.close()
        
        if user:
            dialog = ChangePasswordDialog(self.db_manager, user['id'], parent=self)
            dialog.exec_()
            
    def toggle_user_status(self):
        """تفعيل/إلغاء تفعيل المستخدم"""
        current_row = self.users_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم")
            return
            
        username = self.users_table.item(current_row, 0).text()
        current_status = self.users_table.item(current_row, 4).text()
        
        # منع إلغاء تفعيل المستخدم الحالي
        if username == self.user_data['username']:
            QMessageBox.warning(self, "تحذير", "لا يمكن إلغاء تفعيل المستخدم الحالي")
            return
            
        new_status = 0 if current_status == "نشط" else 1
        action = "إلغاء تفعيل" if current_status == "نشط" else "تفعيل"
        
        reply = QMessageBox.question(self, "تأكيد", 
                                   f"هل تريد {action} المستخدم '{username}'؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE users 
                SET is_active = ?, updated_at = CURRENT_TIMESTAMP
                WHERE username = ?
            ''', (new_status, username))
            
            conn.commit()
            conn.close()
            
            self.load_users()
            QMessageBox.information(self, "نجح", f"تم {action} المستخدم بنجاح")
            
    def show_company_info(self):
        """عرض نافذة بيانات الشركة"""
        dialog = CompanyInfoDialog(self.db_manager, self)
        dialog.exec_()


class UserDialog(QDialog):
    """نافذة حوار إضافة/تعديل المستخدمين"""

    def __init__(self, db_manager, user_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_id = user_id
        self.init_ui()

        if user_id:
            self.load_user_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        title = "تعديل المستخدم" if self.user_id else "إضافة مستخدم جديد"
        self.setWindowTitle(title)
        self.setFixedSize(450, 350)

        layout = QVBoxLayout()

        # معلومات المستخدم
        user_group = QGroupBox("معلومات المستخدم")
        user_layout = QFormLayout()

        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("اسم المستخدم")
        user_layout.addRow("اسم المستخدم *:", self.username_input)

        self.fullname_input = QLineEdit()
        self.fullname_input.setPlaceholderText("الاسم الكامل")
        user_layout.addRow("الاسم الكامل *:", self.fullname_input)

        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("رقم الهاتف")
        user_layout.addRow("رقم الهاتف:", self.phone_input)

        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("البريد الإلكتروني")
        user_layout.addRow("البريد الإلكتروني:", self.email_input)

        user_group.setLayout(user_layout)
        layout.addWidget(user_group)

        # الصلاحيات
        permissions_group = QGroupBox("الصلاحيات")
        permissions_layout = QFormLayout()

        self.role_combo = QComboBox()
        self.role_combo.addItem("كاشير", "cashier")
        self.role_combo.addItem("مدير", "admin")
        permissions_layout.addRow("الصلاحية:", self.role_combo)

        self.is_active_check = QCheckBox("المستخدم نشط")
        self.is_active_check.setChecked(True)
        permissions_layout.addRow("", self.is_active_check)

        permissions_group.setLayout(permissions_layout)
        layout.addWidget(permissions_group)

        # كلمة المرور (للمستخدم الجديد فقط)
        if not self.user_id:
            password_group = QGroupBox("كلمة المرور")
            password_layout = QFormLayout()

            self.password_input = QLineEdit()
            self.password_input.setEchoMode(QLineEdit.Password)
            self.password_input.setPlaceholderText("كلمة المرور")
            password_layout.addRow("كلمة المرور *:", self.password_input)

            self.confirm_password_input = QLineEdit()
            self.confirm_password_input.setEchoMode(QLineEdit.Password)
            self.confirm_password_input.setPlaceholderText("تأكيد كلمة المرور")
            password_layout.addRow("تأكيد كلمة المرور *:", self.confirm_password_input)

            password_group.setLayout(password_layout)
            layout.addWidget(password_group)

        # الأزرار
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("حفظ")
        save_btn.clicked.connect(self.save_user)
        buttons_layout.addWidget(save_btn)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

        self.setLayout(layout)

    def load_user_data(self):
        """تحميل بيانات المستخدم للتعديل"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM users WHERE id = ?', (self.user_id,))
        user = cursor.fetchone()
        conn.close()

        if user:
            self.username_input.setText(user['username'])
            self.fullname_input.setText(user['full_name'])
            self.phone_input.setText(user['phone'] or '')
            self.email_input.setText(user['email'] or '')

            # تحديد الصلاحية
            for i in range(self.role_combo.count()):
                if self.role_combo.itemData(i) == user['role']:
                    self.role_combo.setCurrentIndex(i)
                    break

            self.is_active_check.setChecked(user['is_active'])

    def save_user(self):
        """حفظ المستخدم"""
        # التحقق من البيانات المطلوبة
        if not self.username_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المستخدم")
            return

        if not self.fullname_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال الاسم الكامل")
            return

        # التحقق من كلمة المرور للمستخدم الجديد
        if not self.user_id:
            if not self.password_input.text():
                QMessageBox.warning(self, "خطأ", "يرجى إدخال كلمة المرور")
                return

            if self.password_input.text() != self.confirm_password_input.text():
                QMessageBox.warning(self, "خطأ", "كلمة المرور وتأكيدها غير متطابقين")
                return

        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        try:
            if self.user_id:
                # تحديث مستخدم موجود
                cursor.execute('''
                    UPDATE users
                    SET username=?, full_name=?, phone=?, email=?, role=?,
                        is_active=?, updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                ''', (
                    self.username_input.text().strip(),
                    self.fullname_input.text().strip(),
                    self.phone_input.text().strip() or None,
                    self.email_input.text().strip() or None,
                    self.role_combo.currentData(),
                    self.is_active_check.isChecked(),
                    self.user_id
                ))
            else:
                # إضافة مستخدم جديد
                password_hash = self.db_manager.hash_password(self.password_input.text())
                cursor.execute('''
                    INSERT INTO users
                    (username, password_hash, full_name, phone, email, role, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    self.username_input.text().strip(),
                    password_hash,
                    self.fullname_input.text().strip(),
                    self.phone_input.text().strip() or None,
                    self.email_input.text().strip() or None,
                    self.role_combo.currentData(),
                    self.is_active_check.isChecked()
                ))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم حفظ المستخدم بنجاح")
            self.accept()

        except Exception as e:
            conn.close()
            if "UNIQUE constraint failed" in str(e):
                QMessageBox.critical(self, "خطأ", "اسم المستخدم موجود بالفعل")
            else:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ المستخدم:\n{str(e)}")


class ChangePasswordDialog(QDialog):
    """نافذة حوار تغيير كلمة المرور"""

    def __init__(self, db_manager, user_id, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_id = user_id
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تغيير كلمة المرور")
        self.setFixedSize(350, 200)

        layout = QVBoxLayout()

        # كلمة المرور الجديدة
        password_group = QGroupBox("كلمة المرور الجديدة")
        password_layout = QFormLayout()

        self.new_password_input = QLineEdit()
        self.new_password_input.setEchoMode(QLineEdit.Password)
        self.new_password_input.setPlaceholderText("كلمة المرور الجديدة")
        password_layout.addRow("كلمة المرور الجديدة:", self.new_password_input)

        self.confirm_password_input = QLineEdit()
        self.confirm_password_input.setEchoMode(QLineEdit.Password)
        self.confirm_password_input.setPlaceholderText("تأكيد كلمة المرور")
        password_layout.addRow("تأكيد كلمة المرور:", self.confirm_password_input)

        password_group.setLayout(password_layout)
        layout.addWidget(password_group)

        # الأزرار
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("تغيير كلمة المرور")
        save_btn.clicked.connect(self.change_password)
        buttons_layout.addWidget(save_btn)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

        self.setLayout(layout)

    def change_password(self):
        """تغيير كلمة المرور"""
        if not self.new_password_input.text():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال كلمة المرور الجديدة")
            return

        if self.new_password_input.text() != self.confirm_password_input.text():
            QMessageBox.warning(self, "خطأ", "كلمة المرور وتأكيدها غير متطابقين")
            return

        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        try:
            password_hash = self.db_manager.hash_password(self.new_password_input.text())
            cursor.execute('''
                UPDATE users
                SET password_hash = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (password_hash, self.user_id))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم تغيير كلمة المرور بنجاح")
            self.accept()

        except Exception as e:
            conn.close()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تغيير كلمة المرور:\n{str(e)}")
