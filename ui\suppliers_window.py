#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة إدارة الموردين - نظام نقاط البيع والمحاسبة
Suppliers Management Window - POS and Accounting System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QDialog, QFormLayout, QMessageBox, 
                            QHeaderView, QAbstractItemView, QFrame, QGroupBox, 
                            QTextEdit, QTabWidget)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from utils.config import Config
from datetime import datetime

class SuppliersWindow(QWidget):
    """نافذة إدارة الموردين"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        self.load_suppliers()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة الموردين")
        self.setGeometry(100, 100, 1000, 600)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        add_btn = QPushButton("إضافة مورد جديد")
        add_btn.clicked.connect(self.add_supplier)
        toolbar_layout.addWidget(add_btn)
        
        edit_btn = QPushButton("تعديل المورد")
        edit_btn.clicked.connect(self.edit_supplier)
        toolbar_layout.addWidget(edit_btn)
        
        delete_btn = QPushButton("حذف المورد")
        delete_btn.clicked.connect(self.delete_supplier)
        toolbar_layout.addWidget(delete_btn)
        
        view_profile_btn = QPushButton("عرض الملف الشخصي")
        view_profile_btn.clicked.connect(self.view_supplier_profile)
        toolbar_layout.addWidget(view_profile_btn)
        
        toolbar_layout.addStretch()
        
        # البحث
        search_label = QLabel("البحث:")
        toolbar_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث بالاسم أو الهاتف...")
        self.search_input.textChanged.connect(self.search_suppliers)
        toolbar_layout.addWidget(self.search_input)
        
        main_layout.addLayout(toolbar_layout)
        
        # جدول الموردين
        self.suppliers_table = QTableWidget()
        self.suppliers_table.setColumnCount(5)
        self.suppliers_table.setHorizontalHeaderLabels([
            "الرقم", "اسم المورد", "الهاتف", "العنوان", "الرصيد الحالي"
        ])
        
        # تنسيق الجدول
        header = self.suppliers_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.suppliers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.doubleClicked.connect(self.view_supplier_profile)
        
        main_layout.addWidget(self.suppliers_table)
        
        # شريط الحالة
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("جاري التحميل...")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # إحصائيات سريعة
        self.stats_label = QLabel()
        self.stats_label.setStyleSheet("color: blue; font-weight: bold;")
        status_layout.addWidget(self.stats_label)
        
        main_layout.addLayout(status_layout)
        
        self.setLayout(main_layout)
        
    def load_suppliers(self):
        """تحميل الموردين من قاعدة البيانات"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM suppliers 
            WHERE is_active = 1
            ORDER BY name
        ''')
        
        suppliers = cursor.fetchall()
        conn.close()
        
        # تحديث الجدول
        self.suppliers_table.setRowCount(len(suppliers))
        
        total_balance = 0
        
        for row, supplier in enumerate(suppliers):
            self.suppliers_table.setItem(row, 0, QTableWidgetItem(str(supplier['id'])))
            self.suppliers_table.setItem(row, 1, QTableWidgetItem(supplier['name']))
            self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier['phone'] or ''))
            self.suppliers_table.setItem(row, 3, QTableWidgetItem(supplier['address'] or ''))
            
            # تلوين الرصيد
            balance_item = QTableWidgetItem(f"{supplier['current_balance']:.2f}")
            if supplier['current_balance'] > 0:
                balance_item.setBackground(QColor(255, 200, 200))  # أحمر للمديونية
            elif supplier['current_balance'] < 0:
                balance_item.setBackground(QColor(200, 255, 200))  # أخضر للرصيد الموجب
            self.suppliers_table.setItem(row, 4, balance_item)
            
            total_balance += supplier['current_balance']
            
        # تحديث شريط الحالة
        self.status_label.setText(f"إجمالي الموردين: {len(suppliers)}")
        self.stats_label.setText(f"إجمالي المستحقات: {total_balance:.2f} جنيه")
        
    def search_suppliers(self):
        """البحث في الموردين"""
        search_text = self.search_input.text().lower()
        
        for row in range(self.suppliers_table.rowCount()):
            show_row = False
            
            # البحث في الاسم والهاتف
            name_item = self.suppliers_table.item(row, 1)
            phone_item = self.suppliers_table.item(row, 2)
            
            if name_item and search_text in name_item.text().lower():
                show_row = True
            elif phone_item and search_text in phone_item.text().lower():
                show_row = True
                
            self.suppliers_table.setRowHidden(row, not show_row)
            
    def add_supplier(self):
        """إضافة مورد جديد"""
        dialog = SupplierDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_suppliers()
            
    def edit_supplier(self):
        """تعديل مورد"""
        current_row = self.suppliers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للتعديل")
            return
            
        supplier_id = int(self.suppliers_table.item(current_row, 0).text())
        dialog = SupplierDialog(self.db_manager, supplier_id, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_suppliers()
            
    def delete_supplier(self):
        """حذف مورد"""
        current_row = self.suppliers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للحذف")
            return
            
        supplier_name = self.suppliers_table.item(current_row, 1).text()
        
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   f"هل تريد حذف المورد '{supplier_name}'؟\n"
                                   "سيتم إلغاء تفعيل المورد فقط.",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            supplier_id = int(self.suppliers_table.item(current_row, 0).text())
            
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE suppliers 
                SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (supplier_id,))
            
            conn.commit()
            conn.close()
            
            self.load_suppliers()
            QMessageBox.information(self, "نجح", "تم حذف المورد بنجاح")
            
    def view_supplier_profile(self):
        """عرض الملف الشخصي للمورد"""
        current_row = self.suppliers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد لعرض ملفه")
            return
            
        supplier_id = int(self.suppliers_table.item(current_row, 0).text())
        dialog = SupplierProfileDialog(self.db_manager, supplier_id, parent=self)
        dialog.exec_()


class SupplierDialog(QDialog):
    """نافذة حوار إضافة/تعديل الموردين"""

    def __init__(self, db_manager, supplier_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.supplier_id = supplier_id
        self.init_ui()

        if supplier_id:
            self.load_supplier_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        title = "تعديل المورد" if self.supplier_id else "إضافة مورد جديد"
        self.setWindowTitle(title)
        self.setFixedSize(450, 300)

        layout = QVBoxLayout()

        # معلومات أساسية
        basic_group = QGroupBox("معلومات المورد")
        basic_layout = QFormLayout()

        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("اسم المورد")
        basic_layout.addRow("اسم المورد *:", self.name_input)

        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("رقم الهاتف")
        basic_layout.addRow("رقم الهاتف:", self.phone_input)

        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("البريد الإلكتروني")
        basic_layout.addRow("البريد الإلكتروني:", self.email_input)

        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(80)
        self.address_input.setPlaceholderText("العنوان")
        basic_layout.addRow("العنوان:", self.address_input)

        basic_group.setLayout(basic_layout)
        layout.addWidget(basic_group)

        # الأزرار
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("حفظ")
        save_btn.clicked.connect(self.save_supplier)
        buttons_layout.addWidget(save_btn)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

        self.setLayout(layout)

    def load_supplier_data(self):
        """تحميل بيانات المورد للتعديل"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM suppliers WHERE id = ?', (self.supplier_id,))
        supplier = cursor.fetchone()
        conn.close()

        if supplier:
            self.name_input.setText(supplier['name'])
            self.phone_input.setText(supplier['phone'] or '')
            self.email_input.setText(supplier['email'] or '')
            self.address_input.setPlainText(supplier['address'] or '')

    def save_supplier(self):
        """حفظ المورد"""
        # التحقق من البيانات المطلوبة
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المورد")
            return

        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        try:
            if self.supplier_id:
                # تحديث مورد موجود
                cursor.execute('''
                    UPDATE suppliers
                    SET name=?, phone=?, email=?, address=?, updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                ''', (
                    self.name_input.text().strip(),
                    self.phone_input.text().strip() or None,
                    self.email_input.text().strip() or None,
                    self.address_input.toPlainText().strip() or None,
                    self.supplier_id
                ))
            else:
                # إضافة مورد جديد
                cursor.execute('''
                    INSERT INTO suppliers (name, phone, email, address)
                    VALUES (?, ?, ?, ?)
                ''', (
                    self.name_input.text().strip(),
                    self.phone_input.text().strip() or None,
                    self.email_input.text().strip() or None,
                    self.address_input.toPlainText().strip() or None
                ))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم حفظ المورد بنجاح")
            self.accept()

        except Exception as e:
            conn.close()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ المورد:\n{str(e)}")


class SupplierProfileDialog(QDialog):
    """نافذة الملف الشخصي للمورد"""

    def __init__(self, db_manager, supplier_id, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.supplier_id = supplier_id
        self.init_ui()
        self.load_supplier_profile()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("الملف الشخصي للمورد")
        self.setGeometry(200, 200, 800, 600)

        layout = QVBoxLayout()

        # معلومات المورد الأساسية
        self.supplier_info_label = QLabel()
        self.supplier_info_label.setStyleSheet("""
            background-color: #f0f0f0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-weight: bold;
        """)
        layout.addWidget(self.supplier_info_label)

        # التبويبات
        tabs = QTabWidget()

        # تبويب فواتير الشراء
        invoices_tab = QWidget()
        invoices_layout = QVBoxLayout()

        self.invoices_table = QTableWidget()
        self.invoices_table.setColumnCount(6)
        self.invoices_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "التاريخ", "المبلغ الإجمالي",
            "المدفوع", "المتبقي", "الحالة"
        ])

        invoices_layout.addWidget(self.invoices_table)
        invoices_tab.setLayout(invoices_layout)
        tabs.addTab(invoices_tab, "فواتير الشراء")

        # تبويب المدفوعات
        payments_tab = QWidget()
        payments_layout = QVBoxLayout()

        self.payments_table = QTableWidget()
        self.payments_table.setColumnCount(4)
        self.payments_table.setHorizontalHeaderLabels([
            "التاريخ", "المبلغ", "طريقة الدفع", "ملاحظات"
        ])

        payments_layout.addWidget(self.payments_table)
        payments_tab.setLayout(payments_layout)
        tabs.addTab(payments_tab, "المدفوعات")

        layout.addWidget(tabs)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        export_btn = QPushButton("تصدير PDF")
        export_btn.clicked.connect(self.export_profile)
        buttons_layout.addWidget(export_btn)

        buttons_layout.addStretch()

        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)

        self.setLayout(layout)

    def load_supplier_profile(self):
        """تحميل الملف الشخصي للمورد"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        # معلومات المورد
        cursor.execute('SELECT * FROM suppliers WHERE id = ?', (self.supplier_id,))
        supplier = cursor.fetchone()

        if supplier:
            info_text = f"""
            اسم المورد: {supplier['name']}
            الهاتف: {supplier['phone'] or 'غير محدد'}
            العنوان: {supplier['address'] or 'غير محدد'}
            الرصيد الحالي: {supplier['current_balance']:.2f} جنيه
            """
            self.supplier_info_label.setText(info_text)

        # فواتير الشراء
        cursor.execute('''
            SELECT invoice_number, invoice_date, final_amount, paid_amount, remaining_amount
            FROM invoices
            WHERE supplier_id = ? AND invoice_type = 'purchase' AND is_active = 1
            ORDER BY invoice_date DESC
        ''', (self.supplier_id,))

        invoices = cursor.fetchall()
        self.invoices_table.setRowCount(len(invoices))

        for row, invoice in enumerate(invoices):
            self.invoices_table.setItem(row, 0, QTableWidgetItem(invoice['invoice_number']))
            self.invoices_table.setItem(row, 1, QTableWidgetItem(invoice['invoice_date']))
            self.invoices_table.setItem(row, 2, QTableWidgetItem(f"{invoice['final_amount']:.2f}"))
            self.invoices_table.setItem(row, 3, QTableWidgetItem(f"{invoice['paid_amount']:.2f}"))
            self.invoices_table.setItem(row, 4, QTableWidgetItem(f"{invoice['remaining_amount']:.2f}"))

            status = "مدفوعة" if invoice['remaining_amount'] == 0 else "غير مدفوعة"
            self.invoices_table.setItem(row, 5, QTableWidgetItem(status))

        # المدفوعات
        cursor.execute('''
            SELECT payment_date, amount, payment_method, notes
            FROM payments
            WHERE supplier_id = ?
            ORDER BY payment_date DESC
        ''', (self.supplier_id,))

        payments = cursor.fetchall()
        self.payments_table.setRowCount(len(payments))

        for row, payment in enumerate(payments):
            self.payments_table.setItem(row, 0, QTableWidgetItem(payment['payment_date']))
            self.payments_table.setItem(row, 1, QTableWidgetItem(f"{payment['amount']:.2f}"))

            payment_method = Config.PAYMENT_METHODS.get(payment['payment_method'], payment['payment_method'])
            self.payments_table.setItem(row, 2, QTableWidgetItem(payment_method))
            self.payments_table.setItem(row, 3, QTableWidgetItem(payment['notes'] or ''))

        conn.close()

    def export_profile(self):
        """تصدير قائمة الموردين إلى CSV"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import csv
            from datetime import datetime
            
            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self, 
                "حفظ قائمة الموردين", 
                f"قائمة_الموردين_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv);;All Files (*)"
            )
            
            if file_path:
                # جمع البيانات من الجدول
                table = self.suppliers_table
                data = []
                
                # إضافة العناوين
                headers = []
                for col in range(table.columnCount()):
                    headers.append(table.horizontalHeaderItem(col).text())
                data.append(headers)
                
                # إضافة البيانات
                for row in range(table.rowCount()):
                    if not table.isRowHidden(row):  # فقط الصفوف المرئية
                        row_data = []
                        for col in range(table.columnCount()):
                            item = table.item(row, col)
                            row_data.append(item.text() if item else '')
                        data.append(row_data)
                
                # كتابة الملف
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as file:
                    writer = csv.writer(file)
                    writer.writerows(data)
                
                QMessageBox.information(self, "نجح", f"تم تصدير قائمة الموردين بنجاح إلى:\n{file_path}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير قائمة الموردين:\n{str(e)}")
