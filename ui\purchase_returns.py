"""
نافذة مرتجعات المشتريات
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QLabel, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class PurchaseReturnsWindow(QWidget):
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("مرتجعات المشتريات")
        self.setGeometry(100, 100, 1000, 700)
        
        # تطبيق نفس التصميم المستخدم في فاتورة البيع
        self.setStyleSheet("""
            QWidget {
                background-color: #c8e6c9;
                font-family: 'Arial';
                font-size: 12px;
            }
            QLabel {
                color: #2e7d32;
                font-size: 12px;
            }
        """)
        
        layout = QVBoxLayout()
        
        title = QLabel("🔄 مرتجعات المشتريات")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setStyleSheet("""
            QLabel {
                color: #2e7d32;
                background-color: #e8f5e8;
                padding: 15px;
                border-radius: 8px;
                margin: 10px;
                border: 2px solid #66bb6a;
            }
        """)
        layout.addWidget(title)
        
        info = QLabel("هذه الميزة ستكون متاحة قريباً...")
        info.setAlignment(Qt.AlignCenter)
        info.setFont(QFont("Arial", 14))
        info.setStyleSheet("color: #2e7d32; padding: 20px;")
        layout.addWidget(info)
        
        self.setLayout(layout)
