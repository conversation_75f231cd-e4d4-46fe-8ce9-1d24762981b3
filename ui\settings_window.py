#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة الإعدادات البسيطة - نظام الطيب للتجارة والتوزيع
Simple Settings Window - Al-Tayeb Trading & Distribution System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QMessageBox)
from PyQt5.QtCore import Qt

class SettingsWindow(QWidget):
    """نافذة الإعدادات البسيطة"""
    
    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("⚙️ الإعدادات - نظام الطيب للتجارة والتوزيع")
        self.setGeometry(200, 200, 600, 500)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # العنوان
        title_label = QLabel("⚙️ الإعدادات")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # أزرار الإعدادات
        self.create_settings_buttons(main_layout)
        
        # زر الإغلاق
        close_btn = QPushButton("✕ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        close_btn.clicked.connect(self.close)
        main_layout.addWidget(close_btn)
        
        self.setLayout(main_layout)
        
    def create_settings_buttons(self, layout):
        """إنشاء أزرار الإعدادات"""
        
        # زر إدارة المستخدمين
        users_btn = QPushButton("👥 إدارة المستخدمين")
        users_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-size: 16px;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        users_btn.clicked.connect(self.open_users_management)
        layout.addWidget(users_btn)
        
        # زر النسخ الاحتياطي
        backup_btn = QPushButton("💾 النسخ الاحتياطي")
        backup_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-size: 16px;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        backup_btn.clicked.connect(self.open_backup_management)
        layout.addWidget(backup_btn)
        
        # زر معلومات النظام
        info_btn = QPushButton("ℹ️ معلومات النظام")
        info_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                font-weight: bold;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-size: 16px;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        info_btn.clicked.connect(self.show_system_info)
        layout.addWidget(info_btn)
        
        layout.addStretch()
        
    def open_users_management(self):
        """فتح إدارة المستخدمين"""
        try:
            from ui.users_window import UsersWindow
            self.users_window = UsersWindow(self.db_manager, self.user_data)
            self.users_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح إدارة المستخدمين:\n{str(e)}")
            
    def open_backup_management(self):
        """فتح إدارة النسخ الاحتياطي"""
        try:
            from ui.backup_window import BackupWindow
            self.backup_window = BackupWindow(self.db_manager, self.user_data)
            self.backup_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح إدارة النسخ الاحتياطي:\n{str(e)}")
        
    def show_system_info(self):
        """عرض معلومات النظام"""
        info_text = """🏢 نظام الطيب للتجارة والتوزيع
📱 الإصدار: 2.0
👨‍💻 المطور: فريق التطوير
📅 تاريخ الإصدار: 2024

💻 معلومات النظام:
• قاعدة البيانات: SQLite
• واجهة المستخدم: PyQt5
• اللغة: Python 3.x

📞 للدعم الفني:
• الهاتف: 01008379651
• الهاتف: 01284860988"""
        
        QMessageBox.information(self, "معلومات النظام", info_text)
