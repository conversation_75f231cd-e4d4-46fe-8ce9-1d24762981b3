# نظام الطيب للمبيعات - الملف التنفيذي

## 📁 الملفات المُنشأة

### الملف التنفيذي الرئيسي:
- **الاسم**: `نظام_الطيب_للمبيعات.exe`
- **المسار**: `dist/نظام_الطيب_للمبيعات.exe`
- **الحجم**: حوالي 77 MB
- **النوع**: ملف تنفيذي مستقل (لا يحتاج تثبيت Python)

## 🚀 كيفية التشغيل

### التشغيل المباشر:
1. انتقل إلى مجلد `dist`
2. انقر نقراً مزدوجاً على `نظام_الطيب_للمبيعات.exe`
3. سيبدأ التطبيق مباشرة

### نسخ التطبيق:
- يمكنك نسخ الملف `نظام_الطيب_للمبيعات.exe` إلى أي مكان
- التطبيق مستقل ولا يحتاج ملفات إضافية
- سيتم إنشاء قاعدة البيانات تلقائياً عند التشغيل الأول

## 📋 المتطلبات

### متطلبات النظام:
- **نظام التشغيل**: Windows 7 أو أحدث
- **الذاكرة**: 2 GB RAM على الأقل
- **مساحة القرص**: 100 MB مساحة فارغة
- **الشاشة**: دقة 1024x768 أو أعلى

### لا يتطلب:
- ❌ تثبيت Python
- ❌ تثبيت مكتبات إضافية
- ❌ إعدادات معقدة

## 🔧 إعادة البناء

### لإعادة إنشاء الملف التنفيذي:

#### الطريقة الأولى - السكريبت المبسط:
```bash
python create_exe.py
```

#### الطريقة الثانية - ملف Batch:
```bash
build.bat
```

#### الطريقة الثالثة - PyInstaller مباشرة:
```bash
pyinstaller --onefile --windowed --name=SalesSystem main.py
```

## 📦 محتويات التطبيق

### الوحدات المضمنة:
- ✅ واجهة المستخدم (PyQt5)
- ✅ قاعدة البيانات (SQLite)
- ✅ تقارير PDF (ReportLab)
- ✅ تصدير Excel (OpenPyXL)
- ✅ معالجة البيانات (Pandas)

### الميزات:
- ✅ إدارة المبيعات والمشتريات
- ✅ إدارة العملاء والموردين
- ✅ إدارة المخزون
- ✅ إدارة المرتجعات
- ✅ التقارير المالية
- ✅ إدارة رأس المال
- ✅ نظام المستخدمين

## 🛠️ استكشاف الأخطاء

### إذا لم يعمل التطبيق:
1. **تأكد من صلاحيات التشغيل**
   - انقر بالزر الأيمن → تشغيل كمسؤول

2. **تحقق من مكافح الفيروسات**
   - قد يحجب بعض مكافحات الفيروسات الملف
   - أضف الملف إلى قائمة الاستثناءات

3. **تحقق من مساحة القرص**
   - تأكد من وجود مساحة كافية (100 MB على الأقل)

4. **إعادة تحميل**
   - احذف الملف وأعد إنشاءه باستخدام `create_exe.py`

### رسائل الخطأ الشائعة:
- **"لا يمكن العثور على الملف"**: تأكد من وجود الملف في المسار الصحيح
- **"فشل في التهيئة"**: تأكد من صلاحيات الكتابة في المجلد
- **"خطأ في قاعدة البيانات"**: سيتم إنشاء قاعدة بيانات جديدة تلقائياً

## 📞 الدعم

### معلومات الشركة:
- **الاسم**: شركة الطيب للتجارة والتوزيع
- **الهاتف**: 01008379651
- **الهاتف**: 01284860988

### الإصدار:
- **رقم الإصدار**: 1.0.0
- **تاريخ البناء**: 2024
- **نوع البناء**: إنتاج

---

**ملاحظة**: هذا الملف التنفيذي يحتوي على جميع المكونات المطلوبة ويعمل بشكل مستقل دون الحاجة لتثبيت Python أو أي مكتبات إضافية.
