"""
نافذة تسجيل الدخول
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QPushButton, QMessageBox, QFrame,
                            QGraphicsDropShadowEffect, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPalette, QBrush, QIcon, QColor, QLinearGradient

class LoginWindow(QWidget):
    login_successful = pyqtSignal(dict)

    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.dragging = False
        self.drag_position = None
        self.init_ui()

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        try:
            self.setWindowTitle("تسجيل الدخول - نظام الطيب للتجارة والتوزيع")
            self.setFixedSize(450, 600)
            self.setWindowFlags(Qt.FramelessWindowHint)
            self.setAttribute(Qt.WA_TranslucentBackground)
            
            # Set background to a light gradient
            palette = self.palette()
            gradient = QLinearGradient(0, 0, 0, 400)
            gradient.setColorAt(0, QColor("#f5f7fa"))
            gradient.setColorAt(1, QColor("#c3cfe2"))
            palette.setBrush(QPalette.Window, QBrush(gradient))
            self.setPalette(palette)
            
            # التخطيط الرئيسي
            main_layout = QVBoxLayout()
            main_layout.setContentsMargins(20, 20, 20, 20)
            
            # إطار النافذة الرئيسي
            main_frame = QFrame()
            main_frame.setStyleSheet("""
                QFrame {
                    background-color: white;
                    border-radius: 20px;
                    border: none;
                }
            """)
            
            # إضافة ظل
            shadow = QGraphicsDropShadowEffect()
            shadow.setBlurRadius(30)
            shadow.setColor(QColor(0, 0, 0, 60))
            shadow.setOffset(0, 10)
            main_frame.setGraphicsEffect(shadow)
            
            # تخطيط الإطار
            frame_layout = QVBoxLayout()
            frame_layout.setSpacing(20)
            frame_layout.setContentsMargins(40, 40, 40, 40)
            
            # العنوان مع الأيقونة
            title_layout = QVBoxLayout()
            title_layout.setSpacing(5)
            
            # الأيقونة
            icon_label = QLabel("🏪")
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setFont(QFont("Arial", 36))
            
            title_label = QLabel("نظام الطيب للتجارة والتوزيع")
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setFont(QFont("Arial", 18, QFont.Bold))
            title_label.setStyleSheet("color: #2c3e50;")
            
            subtitle_label = QLabel("تسجيل الدخول")
            subtitle_label.setAlignment(Qt.AlignCenter)
            subtitle_label.setFont(QFont("Arial", 12))
            subtitle_label.setStyleSheet("color: #7f8c8d;")
            
            title_layout.addWidget(icon_label)
            title_layout.addWidget(title_label)
            title_layout.addWidget(subtitle_label)
            
            frame_layout.addLayout(title_layout)
            
            # معلومات الشركة
            company_info = QLabel("مرحباً بكم في شركة الطيب للتجارة والتوزيع\n📞 01008379651 - 01284860988")
            company_info.setAlignment(Qt.AlignCenter)
            company_info.setFont(QFont("Arial", 11))
            company_info.setStyleSheet("""
                QLabel {
                    color: #7f8c8d;
                    padding: 10px;
                    line-height: 1.5;
                    margin-top: 20px;
                }
            """)
            frame_layout.addWidget(company_info)
            
            # حقول الإدخال
            self.create_input_fields(frame_layout)
            
            # أزرار التحكم
            self.create_buttons(frame_layout)
            
            main_frame.setLayout(frame_layout)
            main_layout.addWidget(main_frame)
            
            self.setLayout(main_layout)
            
            # تركيز على حقل اسم المستخدم
            self.username_input.setFocus()
            
        except Exception as e:
            print(f"[ERROR] خطأ في إعداد واجهة تسجيل الدخول: {str(e)}")
            import traceback
            traceback.print_exc()
            
    def create_input_fields(self, layout):
        """إنشاء حقول الإدخال"""
        # اسم المستخدم
        username_label = QLabel("👤 اسم المستخدم:")
        username_label.setFont(QFont("Arial", 12, QFont.Bold))
        username_label.setStyleSheet("color: #2c3e50; margin-bottom: 5px;")
        layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setFont(QFont("Arial", 12))
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 15px;
                border: 1px solid #e0e0e0;
                border-radius: 10px;
                font-size: 14px;
                background-color: #f8f9fa;
            }
            QLineEdit:focus {
                border-color: #4da1ff;
                background-color: white;
                box-shadow: 0 0 0 2px rgba(77, 161, 255, 0.2);
            }
        """)
        self.username_input.textChanged.connect(self.on_input_changed)
        layout.addWidget(self.username_input)
        
        # كلمة المرور
        password_label = QLabel("🔒 كلمة المرور:")
        password_label.setFont(QFont("Arial", 12, QFont.Bold))
        password_label.setStyleSheet("color: #2c3e50; margin-bottom: 5px; margin-top: 10px;")
        layout.addWidget(password_label)
        
        # إطار كلمة المرور مع زر إظهار/إخفاء
        password_frame = QFrame()
        password_layout = QHBoxLayout()
        password_layout.setContentsMargins(0, 0, 0, 0)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setFont(QFont("Arial", 12))
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 15px;
                border: 1px solid #e0e0e0;
                border-radius: 10px;
                font-size: 14px;
                background-color: #f8f9fa;
            }
            QLineEdit:focus {
                border-color: #4da1ff;
                background-color: white;
                box-shadow: 0 0 0 2px rgba(77, 161, 255, 0.2);
            }
        """)
        self.password_input.textChanged.connect(self.on_input_changed)
        self.password_input.returnPressed.connect(self.login)
        
        # زر إظهار/إخفاء كلمة المرور
        self.show_password_btn = QPushButton("👁")
        self.show_password_btn.setFixedSize(40, 40)
        self.show_password_btn.setStyleSheet("""
            QPushButton {
                border: none;
                background-color: transparent;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #ecf0f1;
                border-radius: 20px;
            }
        """)
        self.show_password_btn.clicked.connect(self.toggle_password_visibility)
        
        password_layout.addWidget(self.password_input)
        password_layout.addWidget(self.show_password_btn)
        password_frame.setLayout(password_layout)
        layout.addWidget(password_frame)
        
        # Connect username returnPressed after password field is created
        self.username_input.returnPressed.connect(self.password_input.setFocus)
        
        # خيار تذكر المستخدم
        self.remember_checkbox = QCheckBox("تذكر بياناتي")
        self.remember_checkbox.setFont(QFont("Arial", 10))
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                color: #7f8c8d;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #bdc3c7;
                border-radius: 3px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #3498db;
                border-radius: 3px;
                background-color: #3498db;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
        """)
        layout.addWidget(self.remember_checkbox)
        
    def create_buttons(self, layout):
        """إنشاء الأزرار"""
        # زر تسجيل الدخول
        self.login_button = QPushButton("🔑 تسجيل الدخول")
        self.login_button.setFont(QFont("Arial", 14, QFont.Bold))
        self.login_button.setMinimumHeight(50)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #4da1ff;
                color: white;
                border: none;
                border-radius: 25px;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                transition: background-color 0.3s;
            }
            QPushButton:hover {
                background-color: #3a7bc8;
            }
            QPushButton:pressed {
                background-color: #2a5a8c;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        self.login_button.clicked.connect(self.login)
        self.login_button.setEnabled(False)  # معطل في البداية
        layout.addWidget(self.login_button)
        
        # زر مسح الحقول
        clear_button = QPushButton("🗑 مسح الحقول")
        clear_button.setFont(QFont("Arial", 12))
        clear_button.setMinimumHeight(40)
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 20px;
                padding: 10px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        clear_button.clicked.connect(self.clear_fields)
        layout.addWidget(clear_button)
        
    def toggle_password_visibility(self):
        """تبديل إظهار/إخفاء كلمة المرور"""
        if self.password_input.echoMode() == QLineEdit.Password:
            self.password_input.setEchoMode(QLineEdit.Normal)
            self.show_password_btn.setText("🙈")
        else:
            self.password_input.setEchoMode(QLineEdit.Password)
            self.show_password_btn.setText("👁")
            
    def login(self):
        """معالج تسجيل الدخول مع معالجة محسنة للأخطاء"""
        try:
            username = self.username_input.text().strip()
            password = self.password_input.text().strip()
            
            # التحقق من صحة البيانات المدخلة
            if not username or not password:
                self.show_error_message("يرجى إدخال اسم المستخدم وكلمة المرور")
                return
                
            if len(username) < 3:
                self.show_error_message("اسم المستخدم يجب أن يكون 3 أحرف على الأقل")
                return
                
            if len(password) < 4:
                self.show_error_message("كلمة المرور يجب أن تكون 4 أحرف على الأقل")
                return
            
            print(f"🔄 محاولة تسجيل دخول للمستخدم: {username}")
            
            # تعطيل الزر أثناء المعالجة
            self.login_button.setEnabled(False)
            self.login_button.setText("جاري التحقق...")
            
            # محاولة المصادقة
            user_data = self.db_manager.authenticate_user(username, password)
            
            if user_data:
                print(f"✅ تم تسجيل الدخول بنجاح: {username}")
                self.show_success_message()
                self.login_successful.emit(user_data)
            else:
                print(f"❌ فشل تسجيل الدخول: {username}")
                self.show_error_message("اسم المستخدم أو كلمة المرور غير صحيحة")
                # مسح كلمة المرور للأمان
                self.password_input.clear()
                self.password_input.setFocus()
                
        except Exception as e:
            print(f"[ERROR] خطأ في عملية تسجيل الدخول: {str(e)}")
            import traceback
            traceback.print_exc()
            self.show_error_message(f"حدث خطأ في النظام: {str(e)}")
        finally:
            # إعادة تفعيل الزر
            self.login_button.setEnabled(True)
            
    def show_success_message(self):
        """عرض رسالة نجاح تسجيل الدخول"""
        self.login_button.setText("تم تسجيل الدخول ✓")
        self.login_button.setStyleSheet(self.login_button.styleSheet() + "background: #27ae60;")
        
    def show_error_message(self, message):
        """عرض رسالة خطأ مع تأثيرات بصرية"""
        self.login_button.setText("خطأ في البيانات ✗")
        self.login_button.setStyleSheet(self.login_button.styleSheet() + "background: #e74c3c;")
        
        # عرض رسالة تفصيلية
        QMessageBox.warning(self, "خطأ في تسجيل الدخول", message)
        
        # إعادة تعيين النص والتنسيق بعد ثانيتين
        QTimer.singleShot(2000, self.reset_login_button)
        
    def reset_login_button(self):
        """إعادة تعيين زر تسجيل الدخول"""
        self.login_button.setText("🔑 تسجيل الدخول")
        self.login_button.setStyleSheet(self.login_button.styleSheet().replace("background: #e74c3c;", "").replace("background: #27ae60;", ""))
        
    def clear_fields(self):
        """مسح الحقول"""
        self.username_input.clear()
        self.password_input.clear()
        self.username_input.setFocus()

    def on_input_changed(self):
        """تحديث حالة زر تسجيل الدخول عند تغيير النص"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()

        # تفعيل/تعطيل زر تسجيل الدخول
        self.login_button.setEnabled(bool(username and password))

    def mousePressEvent(self, event):
        """بداية سحب النافذة"""
        if event.button() == Qt.LeftButton:
            self.dragging = True
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """سحب النافذة"""
        if event.buttons() == Qt.LeftButton and self.dragging:
            self.move(event.globalPos() - self.drag_position)
            event.accept()

    def mouseReleaseEvent(self, event):
        """انتهاء سحب النافذة"""
        self.dragging = False
