#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة فواتير الشراء - نظام نقاط البيع والمحاسبة
Purchase Invoices Window - POS and Accounting System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QComboBox, QDialog, QFormLayout, 
                            QMessageBox, QHeaderView, QAbstractItemView,
                            QFrame, QGroupBox, QSpinBox, QDoubleSpinBox,
                            QDateEdit, QTextEdit, QCheckBox, QSplitter,
                            QGridLayout)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QColor
from utils.config import Config
from datetime import datetime
import uuid

class PurchaseWindow(QWidget):
    """نافذة فواتير الشراء"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        self.load_invoices()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("فواتير الشراء")
        self.setGeometry(100, 100, 1200, 700)

        # تطبيق التصميم العصري (نفس تصميم فاتورة المبيعات)
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                font-family: 'Segoe UI', Arial, sans-serif;
                color: #2c3e50;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f1f3f4);
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px 15px;
                font-size: 13px;
                font-weight: 600;
                color: #2c3e50;
                min-width: 120px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e3f2fd, stop:1 #bbdefb);
                border-color: #2196f3;
                color: #1976d2;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #bbdefb, stop:1 #90caf9);
            }
            QTableWidget {
                background-color: white;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f0f0f0;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f5f5f5, stop:1 #e0e0e0);
                border: 1px solid #d0d0d0;
                padding: 8px;
                font-weight: 600;
                color: #2c3e50;
            }
            QLineEdit {
                background-color: white;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #2196f3;
                background-color: #fafafa;
            }
            QComboBox {
                background-color: white;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                min-width: 150px;
            }
            QComboBox:focus {
                border-color: #2196f3;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
                margin-right: 5px;
            }
            QGroupBox {
                font-weight: 600;
                font-size: 14px;
                color: #2c3e50;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                background-color: #f8f9fa;
            }
        """)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        new_invoice_btn = QPushButton("فاتورة شراء جديدة")
        new_invoice_btn.clicked.connect(self.new_invoice)
        toolbar_layout.addWidget(new_invoice_btn)
        
        view_invoice_btn = QPushButton("عرض الفاتورة")
        view_invoice_btn.clicked.connect(self.view_invoice)
        toolbar_layout.addWidget(view_invoice_btn)

        preview_invoice_btn = QPushButton("👁️ معاينة الفاتورة")
        preview_invoice_btn.clicked.connect(self.preview_invoice_from_list)
        toolbar_layout.addWidget(preview_invoice_btn)

        print_invoice_btn = QPushButton("🖨️ طباعة الفاتورة")
        print_invoice_btn.clicked.connect(self.print_invoice)
        toolbar_layout.addWidget(print_invoice_btn)
        
        toolbar_layout.addStretch()
        
        # البحث
        search_label = QLabel("البحث:")
        toolbar_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("رقم الفاتورة أو اسم المورد...")
        self.search_input.textChanged.connect(self.search_invoices)
        toolbar_layout.addWidget(self.search_input)
        
        main_layout.addLayout(toolbar_layout)
        
        # جدول الفواتير
        self.invoices_table = QTableWidget()
        self.invoices_table.setColumnCount(8)
        self.invoices_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "التاريخ", "المورد", "المبلغ الإجمالي",
            "المدفوع", "المتبقي", "طريقة الدفع", "الحالة"
        ])
        
        # تنسيق الجدول
        header = self.invoices_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.invoices_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.invoices_table.setAlternatingRowColors(True)
        self.invoices_table.doubleClicked.connect(self.view_invoice)
        
        main_layout.addWidget(self.invoices_table)
        
        # شريط الحالة
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("جاري التحميل...")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # إحصائيات سريعة
        self.stats_label = QLabel()
        self.stats_label.setStyleSheet("color: blue; font-weight: bold;")
        status_layout.addWidget(self.stats_label)
        
        main_layout.addLayout(status_layout)
        
        self.setLayout(main_layout)
        
    def load_invoices(self):
        """تحميل فواتير الشراء من قاعدة البيانات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT pi.*, s.name as supplier_name
                FROM purchase_invoices pi
                LEFT JOIN suppliers s ON pi.supplier_id = s.id
                WHERE pi.is_active = 1
                ORDER BY pi.invoice_date DESC
            ''')
            
            invoices = cursor.fetchall()
            conn.close()
            
            # تحديث الجدول
            self.invoices_table.setRowCount(len(invoices))
            
            total_amount = 0
            paid_amount = 0
            
            for row, invoice in enumerate(invoices):
                # رقم الفاتورة
                self.invoices_table.setItem(row, 0, QTableWidgetItem(str(invoice[1])))
                
                # التاريخ
                date_str = invoice[2]
                if isinstance(date_str, str):
                    try:
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                        formatted_date = date_obj.strftime('%Y-%m-%d')
                    except:
                        formatted_date = date_str
                else:
                    formatted_date = str(date_str)
                self.invoices_table.setItem(row, 1, QTableWidgetItem(formatted_date))
                
                # المورد
                supplier_name = invoice[-1] if invoice[-1] else "غير محدد"
                self.invoices_table.setItem(row, 2, QTableWidgetItem(supplier_name))
                
                # المبلغ الإجمالي
                total = float(invoice[4]) if invoice[4] else 0
                self.invoices_table.setItem(row, 3, QTableWidgetItem(f"{total:,.2f}"))
                total_amount += total
                
                # المدفوع
                paid = float(invoice[5]) if invoice[5] else 0
                self.invoices_table.setItem(row, 4, QTableWidgetItem(f"{paid:,.2f}"))
                paid_amount += paid
                
                # المتبقي
                remaining = total - paid
                self.invoices_table.setItem(row, 5, QTableWidgetItem(f"{remaining:,.2f}"))
                
                # طريقة الدفع
                payment_method = invoice[6] if invoice[6] else "نقدي"
                self.invoices_table.setItem(row, 6, QTableWidgetItem(payment_method))
                
                # الحالة
                if remaining <= 0:
                    status = "مدفوعة"
                    status_item = QTableWidgetItem(status)
                    status_item.setBackground(QColor(200, 255, 200))
                elif paid > 0:
                    status = "مدفوعة جزئياً"
                    status_item = QTableWidgetItem(status)
                    status_item.setBackground(QColor(255, 255, 200))
                else:
                    status = "غير مدفوعة"
                    status_item = QTableWidgetItem(status)
                    status_item.setBackground(QColor(255, 200, 200))
                
                self.invoices_table.setItem(row, 7, status_item)
            
            # تحديث الإحصائيات
            remaining_amount = total_amount - paid_amount
            self.stats_label.setText(
                f"إجمالي: {total_amount:,.2f} | مدفوع: {paid_amount:,.2f} | متبقي: {remaining_amount:,.2f}"
            )
            
            self.status_label.setText(f"تم تحميل {len(invoices)} فاتورة شراء")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل فواتير الشراء:\n{str(e)}")
            self.status_label.setText("فشل في التحميل")

    def search_invoices(self):
        """البحث في الفواتير"""
        search_text = self.search_input.text().lower()

        for row in range(self.invoices_table.rowCount()):
            show_row = False

            # البحث في رقم الفاتورة
            invoice_number = self.invoices_table.item(row, 0)
            if invoice_number and search_text in invoice_number.text().lower():
                show_row = True

            # البحث في اسم المورد
            supplier_name = self.invoices_table.item(row, 2)
            if supplier_name and search_text in supplier_name.text().lower():
                show_row = True

            self.invoices_table.setRowHidden(row, not show_row)

    def new_invoice(self):
        """إنشاء فاتورة شراء جديدة"""
        try:
            from ui.purchase_invoice_new import PurchaseInvoiceNewWindow
            self.new_invoice_window = PurchaseInvoiceNewWindow(self.db_manager, self.user_data)
            self.new_invoice_window.invoice_saved.connect(self.load_invoices)
            self.new_invoice_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة فاتورة جديدة:\n{str(e)}")

    def view_invoice(self):
        """عرض الفاتورة المحددة"""
        current_row = self.invoices_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة أولاً")
            return

        try:
            # الحصول على رقم الفاتورة
            invoice_number = self.invoices_table.item(current_row, 0).text()

            # البحث عن معرف الفاتورة
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT id FROM purchase_invoices WHERE invoice_number = ?", (invoice_number,))
            result = cursor.fetchone()
            conn.close()

            if result:
                invoice_id = result[0]
                from ui.purchase_invoice_new import PurchaseInvoiceNewWindow
                self.view_invoice_window = PurchaseInvoiceNewWindow(
                    self.db_manager, self.user_data, invoice_id=invoice_id
                )
                self.view_invoice_window.invoice_saved.connect(self.load_invoices)
                self.view_invoice_window.show()
            else:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في عرض الفاتورة:\n{str(e)}")

    def preview_invoice_from_list(self):
        """معاينة الفاتورة من القائمة"""
        current_row = self.invoices_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة أولاً")
            return

        try:
            # الحصول على رقم الفاتورة
            invoice_number = self.invoices_table.item(current_row, 0).text()

            # البحث عن معرف الفاتورة
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT id FROM purchase_invoices WHERE invoice_number = ?", (invoice_number,))
            result = cursor.fetchone()
            conn.close()

            if result:
                invoice_id = result[0]
                self.preview_invoice(invoice_id)
            else:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في معاينة الفاتورة:\n{str(e)}")

    def preview_invoice(self, invoice_id):
        """معاينة الفاتورة"""
        try:
            from ui.invoice_preview import InvoicePreview
            
            # الحصول على بيانات الفاتورة من قاعدة البيانات
            invoice_data = self.get_invoice_data(invoice_id)
            if not invoice_data:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على بيانات الفاتورة")
                return
                
            # فتح نافذة المعاينة
            preview_window = InvoicePreview(invoice_data, "purchase", self)
            preview_window.exec_()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في معاينة الفاتورة:\n{str(e)}")
            
    def get_invoice_data(self, invoice_id):
        """الحصول على بيانات الفاتورة من قاعدة البيانات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # الحصول على بيانات الفاتورة الرئيسية
            cursor.execute('''
                SELECT i.*, s.name as supplier_name
                FROM invoices i
                LEFT JOIN suppliers s ON i.supplier_id = s.id
                WHERE i.id = ?
            ''', (invoice_id,))
            
            invoice = cursor.fetchone()
            if not invoice:
                conn.close()
                return None
                
            # الحصول على عناصر الفاتورة
            cursor.execute('''
                SELECT ii.*, p.name as product_name
                FROM invoice_items ii
                JOIN products p ON ii.product_id = p.id
                WHERE ii.invoice_id = ?
            ''', (invoice_id,))
            
            items = cursor.fetchall()
            conn.close()
            
            # تجميع البيانات
            invoice_data = {
                'invoice_type': 'purchase',
                'invoice_number': invoice['invoice_number'],
                'supplier_name': invoice['supplier_name'] or 'غير محدد',
                'invoice_date': invoice['invoice_date'],
                'subtotal': float(invoice['subtotal_amount']),
                'tax': float(invoice['tax_amount']),
                'discount': float(invoice['discount_amount']),
                'total': float(invoice['final_amount']),
                'paid': float(invoice['paid_amount']),
                'remaining': float(invoice['remaining_amount']),
                'notes': invoice['notes'] or '',
                'items': []
            }
            
            # إضافة العناصر
            for item in items:
                item_data = {
                    'product_name': item['product_name'],
                    'quantity': float(item['quantity']),
                    'unit_price': float(item['unit_price']),
                    'total_price': float(item['total_price'])
                }
                invoice_data['items'].append(item_data)
            
            return invoice_data
            
        except Exception as e:
            print(f"خطأ في الحصول على بيانات الفاتورة: {e}")
            return None

    def print_invoice(self):
        """طباعة الفاتورة"""
        current_row = self.invoices_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة أولاً")
            return

        try:
            # الحصول على رقم الفاتورة
            invoice_number = self.invoices_table.item(current_row, 0).text()

            # البحث عن معرف الفاتورة
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT id FROM purchase_invoices WHERE invoice_number = ?", (invoice_number,))
            result = cursor.fetchone()
            conn.close()

            if result:
                invoice_id = result['id']
                
                # محاولة استخدام أداة الطباعة المتقدمة
                try:
                    from ui.professional_printer import ProfessionalPrinter
                    printer_dialog = ProfessionalPrinter(self.db_manager, invoice_id, 'purchase', self)
                    printer_dialog.exec_()
                except:
                    # في حالة عدم وجود أداة الطباعة، استخدم طريقة بديلة
                    invoice_data = self.get_invoice_data(invoice_id)
                    if invoice_data:
                        self.simple_print_invoice(invoice_data)
                    else:
                        QMessageBox.warning(self, "خطأ", "لم يتم العثور على بيانات الفاتورة")
            else:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة الفاتورة:\n{str(e)}")
            
    def simple_print_invoice(self, invoice_data):
        """طباعة بسيطة للفاتورة"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont
            
            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)
            
            if print_dialog.exec_() == QPrintDialog.Accepted:
                painter = QPainter(printer)
                
                # إعداد الخط
                font = QFont("Arial", 12)
                painter.setFont(font)
                
                # طباعة عنوان الفاتورة
                y = 100
                painter.drawText(100, y, f"فاتورة شراء - {invoice_data.get('invoice_number', 'غير محدد')}")
                y += 40
                painter.drawText(100, y, f"المورد: {invoice_data.get('supplier_name', 'غير محدد')}")
                y += 30
                painter.drawText(100, y, f"التاريخ: {invoice_data.get('invoice_date', 'غير محدد')}")
                y += 50
                
                # طباعة العناصر
                painter.drawText(100, y, "العناصر:")
                y += 30
                
                for item in invoice_data.get('items', []):
                    painter.drawText(100, y, f"{item['product_name']} - الكمية: {item['quantity']} - السعر: {item['unit_price']} - الإجمالي: {item['total_price']}")
                    y += 25
                
                # طباعة الإجماليات
                y += 30
                painter.drawText(100, y, f"المجموع الفرعي: {invoice_data.get('subtotal', 0)}")
                y += 25
                painter.drawText(100, y, f"الضريبة: {invoice_data.get('tax', 0)}")
                y += 25
                painter.drawText(100, y, f"الخصم: {invoice_data.get('discount', 0)}")
                y += 25
                painter.drawText(100, y, f"الإجمالي: {invoice_data.get('total', 0)}")
                
                painter.end()
                QMessageBox.information(self, "نجح", "تم إرسال الفاتورة للطباعة")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة الفاتورة:\n{str(e)}")
