#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة إدارة المصاريف - نظام نقاط البيع والمحاسبة
Expenses Management Window - POS and Accounting System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QComboBox, QDialog, QFormLayout, 
                            QMessageBox, QHeaderView, QAbstractItemView,
                            QFrame, QGroupBox, QDoubleSpinBox, QDateEdit, 
                            QTextEdit, QTabWidget, QFileDialog)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QColor
from utils.config import Config
from datetime import datetime

class ExpensesWindow(QWidget):
    """نافذة إدارة المصاريف"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        self.load_expenses()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة المصاريف")
        self.setGeometry(100, 100, 1200, 700)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # التبويبات
        tabs = QTabWidget()
        
        # تبويب المصاريف
        expenses_tab = QWidget()
        self.setup_expenses_tab(expenses_tab)
        tabs.addTab(expenses_tab, "المصاريف")
        
        # تبويب أنواع المصاريف
        categories_tab = QWidget()
        self.setup_categories_tab(categories_tab)
        tabs.addTab(categories_tab, "أنواع المصاريف")
        
        main_layout.addWidget(tabs)
        self.setLayout(main_layout)
        
    def setup_expenses_tab(self, tab):
        """إعداد تبويب المصاريف"""
        layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        new_expense_btn = QPushButton("مصروف جديد")
        new_expense_btn.clicked.connect(self.new_expense)
        toolbar_layout.addWidget(new_expense_btn)
        
        edit_expense_btn = QPushButton("تعديل المصروف")
        edit_expense_btn.clicked.connect(self.edit_expense)
        toolbar_layout.addWidget(edit_expense_btn)
        
        delete_expense_btn = QPushButton("حذف المصروف")
        delete_expense_btn.clicked.connect(self.delete_expense)
        toolbar_layout.addWidget(delete_expense_btn)
        
        toolbar_layout.addStretch()
        
        # فلتر التاريخ
        date_label = QLabel("من:")
        toolbar_layout.addWidget(date_label)
        
        self.from_date = QDateEdit()
        self.from_date.setDate(QDate.currentDate().addDays(-30))
        self.from_date.dateChanged.connect(self.filter_expenses)
        toolbar_layout.addWidget(self.from_date)
        
        to_label = QLabel("إلى:")
        toolbar_layout.addWidget(to_label)
        
        self.to_date = QDateEdit()
        self.to_date.setDate(QDate.currentDate())
        self.to_date.dateChanged.connect(self.filter_expenses)
        toolbar_layout.addWidget(self.to_date)
        
        # البحث
        search_label = QLabel("البحث:")
        toolbar_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("رقم المصروف أو الوصف...")
        self.search_input.textChanged.connect(self.search_expenses)
        toolbar_layout.addWidget(self.search_input)
        
        layout.addLayout(toolbar_layout)
        
        # جدول المصاريف
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(7)
        self.expenses_table.setHorizontalHeaderLabels([
            "رقم المصروف", "التاريخ", "النوع", "المبلغ", 
            "طريقة الدفع", "الوصف", "المستخدم"
        ])
        
        # تنسيق الجدول
        header = self.expenses_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.expenses_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.expenses_table.setAlternatingRowColors(True)
        self.expenses_table.doubleClicked.connect(self.edit_expense)
        
        layout.addWidget(self.expenses_table)
        
        # شريط الحالة
        status_layout = QHBoxLayout()
        
        self.expenses_status_label = QLabel("جاري التحميل...")
        status_layout.addWidget(self.expenses_status_label)
        
        status_layout.addStretch()
        
        # إحصائيات المصاريف
        self.expenses_stats_label = QLabel()
        self.expenses_stats_label.setStyleSheet("color: red; font-weight: bold;")
        status_layout.addWidget(self.expenses_stats_label)
        
        layout.addLayout(status_layout)
        
        tab.setLayout(layout)
        
    def setup_categories_tab(self, tab):
        """إعداد تبويب أنواع المصاريف"""
        layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        new_category_btn = QPushButton("نوع جديد")
        new_category_btn.clicked.connect(self.new_category)
        toolbar_layout.addWidget(new_category_btn)
        
        edit_category_btn = QPushButton("تعديل النوع")
        edit_category_btn.clicked.connect(self.edit_category)
        toolbar_layout.addWidget(edit_category_btn)
        
        delete_category_btn = QPushButton("حذف النوع")
        delete_category_btn.clicked.connect(self.delete_category)
        toolbar_layout.addWidget(delete_category_btn)
        
        toolbar_layout.addStretch()
        
        layout.addLayout(toolbar_layout)
        
        # جدول أنواع المصاريف
        self.categories_table = QTableWidget()
        self.categories_table.setColumnCount(4)
        self.categories_table.setHorizontalHeaderLabels([
            "الرقم", "اسم النوع", "الوصف", "تاريخ الإنشاء"
        ])
        
        # تنسيق الجدول
        header = self.categories_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.categories_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.categories_table.setAlternatingRowColors(True)
        self.categories_table.doubleClicked.connect(self.edit_category)
        
        layout.addWidget(self.categories_table)
        
        # شريط الحالة
        status_layout = QHBoxLayout()
        
        self.categories_status_label = QLabel("جاري التحميل...")
        status_layout.addWidget(self.categories_status_label)
        
        layout.addLayout(status_layout)
        
        tab.setLayout(layout)
        
        # تحميل أنواع المصاريف
        self.load_categories()
        
    def load_expenses(self):
        """تحميل المصاريف من قاعدة البيانات"""
        from_date = self.from_date.date().toString('yyyy-MM-dd') if hasattr(self, 'from_date') else '1900-01-01'
        to_date = self.to_date.date().toString('yyyy-MM-dd') if hasattr(self, 'to_date') else '2100-12-31'
        
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT e.*, ec.name as category_name, u.full_name as user_name
            FROM expenses e
            JOIN expense_categories ec ON e.category_id = ec.id
            JOIN users u ON e.user_id = u.id
            WHERE e.expense_date BETWEEN ? AND ?
            ORDER BY e.expense_date DESC, e.created_at DESC
        ''', (from_date, to_date))
        
        expenses = cursor.fetchall()
        conn.close()
        
        # تحديث الجدول
        self.expenses_table.setRowCount(len(expenses))
        
        total_amount = 0
        
        for row, expense in enumerate(expenses):
            self.expenses_table.setItem(row, 0, QTableWidgetItem(expense['expense_number']))
            self.expenses_table.setItem(row, 1, QTableWidgetItem(expense['expense_date']))
            self.expenses_table.setItem(row, 2, QTableWidgetItem(expense['category_name']))
            
            # تلوين المبلغ بالأحمر
            amount_item = QTableWidgetItem(f"{expense['amount']:.2f}")
            amount_item.setForeground(QColor(200, 0, 0))
            self.expenses_table.setItem(row, 3, amount_item)
            
            payment_method = Config.PAYMENT_METHODS.get(expense['payment_method'], expense['payment_method'])
            self.expenses_table.setItem(row, 4, QTableWidgetItem(payment_method))
            self.expenses_table.setItem(row, 5, QTableWidgetItem(expense['description'] or ''))
            self.expenses_table.setItem(row, 6, QTableWidgetItem(expense['user_name']))
            
            total_amount += expense['amount']
            
        # تحديث شريط الحالة
        self.expenses_status_label.setText(f"إجمالي المصاريف: {len(expenses)}")
        self.expenses_stats_label.setText(f"إجمالي المبلغ: {total_amount:.2f} جنيه")
        
    def load_categories(self):
        """تحميل أنواع المصاريف"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM expense_categories 
            WHERE is_active = 1
            ORDER BY name
        ''')
        
        categories = cursor.fetchall()
        conn.close()
        
        # تحديث الجدول
        self.categories_table.setRowCount(len(categories))
        
        for row, category in enumerate(categories):
            self.categories_table.setItem(row, 0, QTableWidgetItem(str(category['id'])))
            self.categories_table.setItem(row, 1, QTableWidgetItem(category['name']))
            self.categories_table.setItem(row, 2, QTableWidgetItem(category['description'] or ''))
            self.categories_table.setItem(row, 3, QTableWidgetItem(category['created_at']))
            
        # تحديث شريط الحالة
        self.categories_status_label.setText(f"إجمالي الأنواع: {len(categories)}")
        
    def filter_expenses(self):
        """فلترة المصاريف حسب التاريخ"""
        self.load_expenses()
        
    def search_expenses(self):
        """البحث في المصاريف"""
        search_text = self.search_input.text().lower()
        
        for row in range(self.expenses_table.rowCount()):
            show_row = False
            
            # البحث في رقم المصروف والوصف
            expense_number_item = self.expenses_table.item(row, 0)
            description_item = self.expenses_table.item(row, 5)
            
            if expense_number_item and search_text in expense_number_item.text().lower():
                show_row = True
            elif description_item and search_text in description_item.text().lower():
                show_row = True
                
            self.expenses_table.setRowHidden(row, not show_row)
            
    def new_expense(self):
        """إضافة مصروف جديد"""
        dialog = ExpenseDialog(self.db_manager, self.user_data, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_expenses()
            
    def edit_expense(self):
        """تعديل مصروف"""
        current_row = self.expenses_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مصروف للتعديل")
            return
            
        expense_number = self.expenses_table.item(current_row, 0).text()
        
        # البحث عن المصروف
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM expenses WHERE expense_number = ?', (expense_number,))
        expense = cursor.fetchone()
        conn.close()
        
        if expense:
            dialog = ExpenseDialog(self.db_manager, self.user_data, expense['id'], parent=self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_expenses()
                
    def delete_expense(self):
        """حذف مصروف"""
        current_row = self.expenses_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مصروف للحذف")
            return
            
        expense_number = self.expenses_table.item(current_row, 0).text()
        
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   f"هل تريد حذف المصروف '{expense_number}'؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('DELETE FROM expenses WHERE expense_number = ?', (expense_number,))
            
            conn.commit()
            conn.close()
            
            self.load_expenses()
            QMessageBox.information(self, "نجح", "تم حذف المصروف بنجاح")
            
    def new_category(self):
        """إضافة نوع مصروف جديد"""
        dialog = ExpenseCategoryDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_categories()
            
    def edit_category(self):
        """تعديل نوع مصروف"""
        current_row = self.categories_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار نوع للتعديل")
            return
            
        category_id = int(self.categories_table.item(current_row, 0).text())
        dialog = ExpenseCategoryDialog(self.db_manager, category_id, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_categories()
            
    def delete_category(self):
        """حذف نوع مصروف"""
        current_row = self.categories_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار نوع للحذف")
            return
            
        category_name = self.categories_table.item(current_row, 1).text()
        
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   f"هل تريد حذف نوع المصروف '{category_name}'؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            category_id = int(self.categories_table.item(current_row, 0).text())
            
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # التحقق من وجود مصاريف مرتبطة بهذا النوع
            cursor.execute('SELECT COUNT(*) FROM expenses WHERE category_id = ?', (category_id,))
            expense_count = cursor.fetchone()[0]
            
            if expense_count > 0:
                QMessageBox.warning(self, "تحذير", 
                                  f"لا يمكن حذف هذا النوع لأنه مرتبط بـ {expense_count} مصروف")
                conn.close()
                return
                
            cursor.execute('UPDATE expense_categories SET is_active = 0 WHERE id = ?', (category_id,))
            
            conn.commit()
            conn.close()
            
            self.load_categories()
            QMessageBox.information(self, "نجح", "تم حذف نوع المصروف بنجاح")


class ExpenseDialog(QDialog):
    """نافذة حوار إضافة/تعديل المصاريف"""

    def __init__(self, db_manager, user_data, expense_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        self.expense_id = expense_id
        self.init_ui()

        if expense_id:
            self.load_expense_data()
        else:
            self.generate_expense_number()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        title = "تعديل المصروف" if self.expense_id else "مصروف جديد"
        self.setWindowTitle(title)
        self.setFixedSize(500, 450)

        layout = QVBoxLayout()

        # معلومات المصروف
        expense_group = QGroupBox("معلومات المصروف")
        expense_layout = QFormLayout()

        # رقم المصروف
        self.expense_number_label = QLabel()
        expense_layout.addRow("رقم المصروف:", self.expense_number_label)

        # نوع المصروف
        self.category_combo = QComboBox()
        self.load_categories()
        expense_layout.addRow("نوع المصروف *:", self.category_combo)

        # المبلغ
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setMaximum(999999.99)
        self.amount_input.setDecimals(2)
        self.amount_input.setSuffix(" جنيه")
        expense_layout.addRow("المبلغ *:", self.amount_input)

        # التاريخ
        self.expense_date = QDateEdit()
        self.expense_date.setDate(QDate.currentDate())
        expense_layout.addRow("تاريخ المصروف:", self.expense_date)

        # طريقة الدفع
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItem("نقدي", "cash")
        self.payment_method_combo.addItem("تحويل بنكي", "bank_transfer")
        expense_layout.addRow("طريقة الدفع:", self.payment_method_combo)

        # رقم المرجع
        self.reference_input = QLineEdit()
        self.reference_input.setPlaceholderText("رقم المرجع (اختياري)")
        expense_layout.addRow("رقم المرجع:", self.reference_input)

        expense_group.setLayout(expense_layout)
        layout.addWidget(expense_group)

        # الوصف
        description_group = QGroupBox("الوصف والملاحظات")
        description_layout = QVBoxLayout()

        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(100)
        self.description_input.setPlaceholderText("وصف المصروف...")
        description_layout.addWidget(self.description_input)

        description_group.setLayout(description_layout)
        layout.addWidget(description_group)

        # الأزرار
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("حفظ المصروف")
        save_btn.clicked.connect(self.save_expense)
        buttons_layout.addWidget(save_btn)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

        self.setLayout(layout)

    def generate_expense_number(self):
        """توليد رقم مصروف جديد"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        prefix = self.db_manager.get_setting('expense_number_prefix', 'EXP')
        today = datetime.now().strftime("%Y%m%d")

        # البحث عن رقم مصروف فريد
        counter = 1
        while True:
            expense_number = f"{prefix}{today}{counter:03d}"

            # التحقق من عدم وجود هذا الرقم
            cursor.execute('''
                SELECT COUNT(*) FROM expenses
                WHERE expense_number = ?
            ''', (expense_number,))

            if cursor.fetchone()[0] == 0:
                break

            counter += 1

        conn.close()
        self.expense_number_label.setText(expense_number)

    def load_categories(self):
        """تحميل أنواع المصاريف"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, name FROM expense_categories
            WHERE is_active = 1
            ORDER BY name
        ''')

        categories = cursor.fetchall()
        conn.close()

        self.category_combo.clear()
        for category in categories:
            self.category_combo.addItem(category['name'], category['id'])

    def load_expense_data(self):
        """تحميل بيانات المصروف للتعديل"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM expenses WHERE id = ?', (self.expense_id,))
        expense = cursor.fetchone()
        conn.close()

        if expense:
            self.expense_number_label.setText(expense['expense_number'])

            # تحديد نوع المصروف
            for i in range(self.category_combo.count()):
                if self.category_combo.itemData(i) == expense['category_id']:
                    self.category_combo.setCurrentIndex(i)
                    break

            self.amount_input.setValue(expense['amount'])
            self.expense_date.setDate(QDate.fromString(expense['expense_date'], 'yyyy-MM-dd'))

            # تحديد طريقة الدفع
            for i in range(self.payment_method_combo.count()):
                if self.payment_method_combo.itemData(i) == expense['payment_method']:
                    self.payment_method_combo.setCurrentIndex(i)
                    break

            self.reference_input.setText(expense['reference_number'] or '')
            self.description_input.setPlainText(expense['description'] or '')

    def save_expense(self):
        """حفظ المصروف"""
        # التحقق من البيانات المطلوبة
        if self.category_combo.currentData() is None:
            QMessageBox.warning(self, "خطأ", "يرجى اختيار نوع المصروف")
            return

        if self.amount_input.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ صحيح")
            return

        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        try:
            if self.expense_id:
                # تحديث مصروف موجود
                cursor.execute('''
                    UPDATE expenses
                    SET category_id=?, amount=?, description=?, expense_date=?,
                        payment_method=?, reference_number=?
                    WHERE id=?
                ''', (
                    self.category_combo.currentData(),
                    self.amount_input.value(),
                    self.description_input.toPlainText().strip() or None,
                    self.expense_date.date().toString('yyyy-MM-dd'),
                    self.payment_method_combo.currentData(),
                    self.reference_input.text().strip() or None,
                    self.expense_id
                ))
            else:
                # إضافة مصروف جديد
                cursor.execute('''
                    INSERT INTO expenses
                    (expense_number, category_id, amount, description, expense_date,
                     payment_method, reference_number, user_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    self.expense_number_label.text(),
                    self.category_combo.currentData(),
                    self.amount_input.value(),
                    self.description_input.toPlainText().strip() or None,
                    self.expense_date.date().toString('yyyy-MM-dd'),
                    self.payment_method_combo.currentData(),
                    self.reference_input.text().strip() or None,
                    self.user_data['id']
                ))

                expense_id = cursor.lastrowid

                # خصم المبلغ من الخزينة (جميع المصاريف تخصم من الخزينة)
                try:
                    # الحصول على حساب الخزينة
                    treasury_account = self.db_manager.get_account_by_type('treasury')
                    if treasury_account:
                        # خصم المبلغ من الخزينة
                        self.db_manager.update_account_balance(
                            treasury_account['id'],
                            self.amount_input.value(),
                            'subtract',
                            'expense',
                            expense_id,
                            f"مصروف رقم {self.expense_number_label.text()} - {self.description_input.toPlainText().strip() or 'مصروف'}",
                            self.user_data['id']
                        )
                except Exception as e:
                    # في حالة فشل تحديث الخزينة، نتجاهل الخطأ ونكمل حفظ المصروف
                    print(f"خطأ في تحديث الخزينة: {str(e)}")

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم حفظ المصروف بنجاح")
            self.accept()

        except Exception as e:
            conn.close()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ المصروف:\n{str(e)}")


class ExpenseCategoryDialog(QDialog):
    """نافذة حوار إضافة/تعديل أنواع المصاريف"""

    def __init__(self, db_manager, category_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.category_id = category_id
        self.init_ui()

        if category_id:
            self.load_category_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        title = "تعديل نوع المصروف" if self.category_id else "نوع مصروف جديد"
        self.setWindowTitle(title)
        self.setFixedSize(400, 250)

        layout = QVBoxLayout()

        # معلومات النوع
        category_group = QGroupBox("معلومات نوع المصروف")
        category_layout = QFormLayout()

        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("اسم نوع المصروف")
        category_layout.addRow("اسم النوع *:", self.name_input)

        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(80)
        self.description_input.setPlaceholderText("وصف نوع المصروف")
        category_layout.addRow("الوصف:", self.description_input)

        category_group.setLayout(category_layout)
        layout.addWidget(category_group)

        # الأزرار
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("حفظ")
        save_btn.clicked.connect(self.save_category)
        buttons_layout.addWidget(save_btn)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

        self.setLayout(layout)

    def load_category_data(self):
        """تحميل بيانات نوع المصروف للتعديل"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM expense_categories WHERE id = ?', (self.category_id,))
        category = cursor.fetchone()
        conn.close()

        if category:
            self.name_input.setText(category['name'])
            self.description_input.setPlainText(category['description'] or '')

    def save_category(self):
        """حفظ نوع المصروف"""
        # التحقق من البيانات المطلوبة
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم نوع المصروف")
            return

        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        try:
            if self.category_id:
                # تحديث نوع موجود
                cursor.execute('''
                    UPDATE expense_categories
                    SET name=?, description=?
                    WHERE id=?
                ''', (
                    self.name_input.text().strip(),
                    self.description_input.toPlainText().strip() or None,
                    self.category_id
                ))
            else:
                # إضافة نوع جديد
                cursor.execute('''
                    INSERT INTO expense_categories (name, description)
                    VALUES (?, ?)
                ''', (
                    self.name_input.text().strip(),
                    self.description_input.toPlainText().strip() or None
                ))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم حفظ نوع المصروف بنجاح")
            self.accept()

        except Exception as e:
            conn.close()
            if "UNIQUE constraint failed" in str(e):
                QMessageBox.critical(self, "خطأ", "اسم نوع المصروف موجود بالفعل")
            else:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ نوع المصروف:\n{str(e)}")
