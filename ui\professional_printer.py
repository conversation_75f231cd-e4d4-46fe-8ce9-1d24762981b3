#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام طباعة احترافي للفواتير - مطابق للتصميم المطلوب
Professional Invoice Printer - Matching Required Design
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTextEdit, QMessageBox, QFileDialog)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QTextDocument
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
from datetime import datetime

class ProfessionalPrinter(QDialog):
    """نافذة طباعة احترافية"""
    
    def __init__(self, db_manager, invoice_id, invoice_type='sale', parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.invoice_id = invoice_id
        self.invoice_type = invoice_type
        self.invoice_data = None
        self.invoice_items = []
        
        self.init_ui()
        self.load_invoice_data()
        self.generate_professional_html()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("🖨️ طباعة الفاتورة")
        self.setGeometry(100, 100, 900, 700)
        
        layout = QVBoxLayout()
        
        # عنوان
        title = QLabel("معاينة الفاتورة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px; color: #2e7d32;")
        layout.addWidget(title)
        
        # منطقة المعاينة
        self.preview_area = QTextEdit()
        self.preview_area.setReadOnly(True)
        layout.addWidget(self.preview_area)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        # زر الطباعة
        print_btn = QPushButton("🖨️ طباعة")
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #4caf50;
                color: white;
                font-weight: bold;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                border: none;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        print_btn.clicked.connect(self.print_invoice)
        buttons_layout.addWidget(print_btn)
        
        # زر حفظ PDF
        save_pdf_btn = QPushButton("💾 حفظ PDF")
        save_pdf_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196f3;
                color: white;
                font-weight: bold;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                border: none;
            }
            QPushButton:hover {
                background-color: #1976d2;
            }
        """)
        save_pdf_btn.clicked.connect(self.save_as_pdf)
        buttons_layout.addWidget(save_pdf_btn)
        
        # زر إغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                font-weight: bold;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                border: none;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
        
    def load_invoice_data(self):
        """تحميل بيانات الفاتورة"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # تحميل بيانات الفاتورة الأساسية
            if self.invoice_type == 'sale':
                cursor.execute('''
                    SELECT i.*, c.name as customer_name, c.phone as customer_phone, c.address as customer_address
                    FROM invoices i
                    LEFT JOIN customers c ON i.customer_id = c.id
                    WHERE i.id = ? AND i.invoice_type = 'sale'
                ''', (self.invoice_id,))
            else:
                cursor.execute('''
                    SELECT i.*, s.name as supplier_name, s.phone as supplier_phone, s.address as supplier_address
                    FROM invoices i
                    LEFT JOIN suppliers s ON i.supplier_id = s.id
                    WHERE i.id = ? AND i.invoice_type = 'purchase'
                ''', (self.invoice_id,))
            
            self.invoice_data = cursor.fetchone()
            
            # تحميل أصناف الفاتورة
            cursor.execute('''
                SELECT ii.*, p.name as product_name, p.unit
                FROM invoice_items ii
                JOIN products p ON ii.product_id = p.id
                WHERE ii.invoice_id = ?
                ORDER BY ii.id
            ''', (self.invoice_id,))
            
            self.invoice_items = cursor.fetchall()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل بيانات الفاتورة:\n{str(e)}")
        finally:
            conn.close()
    
    def generate_professional_html(self):
        """إنشاء HTML احترافي للفاتورة مطابق للتصميم المطلوب"""
        if not self.invoice_data:
            self.preview_area.setPlainText("خطأ: لم يتم تحميل بيانات الفاتورة")
            return

        try:
            print(f"بيانات الفاتورة: {dict(self.invoice_data)}")  # للتشخيص
            print(f"أصناف الفاتورة: {[dict(item) for item in self.invoice_items]}")  # للتشخيص
            # تحديد نوع الفاتورة
            invoice_title = "فاتورة مبيعات" if self.invoice_type == 'sale' else "فاتورة مشتريات"
            entity_name = self.invoice_data['customer_name'] if self.invoice_type == 'sale' and self.invoice_data['customer_name'] else 'عميل نقدي'
            if self.invoice_type == 'purchase':
                entity_name = self.invoice_data['supplier_name'] if self.invoice_data['supplier_name'] else 'مورد نقدي'

            # تحويل طريقة الدفع
            payment_methods = {
                'cash': 'نقدي',
                'bank_transfer': 'تحويل بنكي',
                'credit': 'آجل',
                'partial': 'جزئي'
            }
            payment_method = payment_methods.get(self.invoice_data['payment_method'], self.invoice_data['payment_method'])
            
            html = f"""
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <style>
                    @page {{
                        size: A4;
                        margin: 15mm;
                    }}
                    body {{
                        font-family: 'Arial', sans-serif;
                        margin: 0;
                        padding: 0;
                        direction: rtl;
                        font-size: 12px;
                        line-height: 1.4;
                    }}
                    .invoice-container {{
                        border: 2px solid #2e7d32;
                        padding: 15px;
                        background-color: #f8f9fa;
                    }}
                    .header {{
                        text-align: center;
                        margin-bottom: 20px;
                        border-bottom: 2px solid #2e7d32;
                        padding-bottom: 15px;
                    }}
                    .company-name {{
                        font-size: 20px;
                        font-weight: bold;
                        color: #2e7d32;
                        margin-bottom: 5px;
                    }}
                    .invoice-title {{
                        font-size: 16px;
                        font-weight: bold;
                        color: #333;
                        background-color: #e8f5e8;
                        padding: 8px;
                        border-radius: 5px;
                    }}
                    .info-section {{
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 20px;
                        background-color: white;
                        padding: 10px;
                        border: 1px solid #ddd;
                    }}
                    .info-left, .info-right {{
                        width: 48%;
                    }}
                    .info-row {{
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 5px;
                        padding: 3px 0;
                        border-bottom: 1px dotted #ccc;
                    }}
                    .info-label {{
                        font-weight: bold;
                        color: #2e7d32;
                    }}
                    .items-table {{
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 15px;
                        background-color: white;
                    }}
                    .items-table th {{
                        background-color: #4caf50;
                        color: white;
                        padding: 10px 5px;
                        text-align: center;
                        font-weight: bold;
                        border: 1px solid #2e7d32;
                    }}
                    .items-table td {{
                        padding: 8px 5px;
                        text-align: center;
                        border: 1px solid #ddd;
                    }}
                    .items-table tr:nth-child(even) {{
                        background-color: #f9f9f9;
                    }}
                    .totals-section {{
                        display: flex;
                        justify-content: space-between;
                        margin-top: 20px;
                    }}
                    .totals-left {{
                        width: 60%;
                        background-color: white;
                        padding: 10px;
                        border: 1px solid #ddd;
                    }}
                    .totals-right {{
                        width: 35%;
                        background-color: #e8f5e8;
                        padding: 10px;
                        border: 2px solid #2e7d32;
                    }}
                    .total-row {{
                        display: flex;
                        justify-content: space-between;
                        padding: 5px 0;
                        border-bottom: 1px dotted #ccc;
                    }}
                    .final-total {{
                        font-weight: bold;
                        font-size: 14px;
                        color: #2e7d32;
                        border-top: 2px solid #2e7d32;
                        padding-top: 8px;
                        margin-top: 8px;
                    }}
                    .footer {{
                        text-align: center;
                        margin-top: 20px;
                        padding-top: 15px;
                        border-top: 1px solid #ddd;
                        color: #666;
                        font-size: 10px;
                    }}
                </style>
            </head>
            <body>
                <div class="invoice-container">
                    <div class="header">
                        <div class="company-name">شركة شادي الطيب للتجارة</div>
                        <div class="invoice-title">{invoice_title}</div>
                    </div>
                    
                    <div class="info-section">
                        <div class="info-right">
                            <div class="info-row">
                                <span class="info-label">التاريخ:</span>
                                <span>{self.invoice_data['invoice_date']}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">رقم البيان:</span>
                                <span>{self.invoice_data['invoice_number']}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">العميل:</span>
                                <span>{entity_name}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">العنوان:</span>
                                <span>غير محدد</span>
                            </div>
                        </div>
                        <div class="info-left">
                            <div class="info-row">
                                <span class="info-label">بيان مبيعات نقدي</span>
                                <span></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">المستخدم:</span>
                                <span>المدير</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">كود العميل:</span>
                                <span>9</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">تليفون:</span>
                                <span>01008379651</span>
                            </div>
                        </div>
                    </div>
                    
                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>م</th>
                                <th>الصنف</th>
                                <th>الوحدة</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
            """
            
            # إضافة أصناف الفاتورة
            for i, item in enumerate(self.invoice_items, 1):
                product_name = item['product_name'] if item['product_name'] else 'غير محدد'
                unit = item['unit'] if item['unit'] else 'قطعة'
                quantity = item['quantity'] if item['quantity'] else 0
                unit_price = item['unit_price'] if item['unit_price'] else 0
                total_price = item['total_price'] if item['total_price'] else 0
                
                html += f"""
                        <tr>
                            <td>{i}</td>
                            <td>{product_name}</td>
                            <td>{unit}</td>
                            <td>{quantity}</td>
                            <td>{unit_price:.2f}</td>
                            <td>{total_price:.2f}</td>
                        </tr>
                """
            
            html += f"""
                        </tbody>
                    </table>
                    
                    <div class="totals-section">
                        <div class="totals-left">
                            <div class="info-row">
                                <span class="info-label">إجمالي الكمية:</span>
                                <span>{sum(item['quantity'] if item['quantity'] else 0 for item in self.invoice_items)}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">الرصيد السابق:</span>
                                <span>0.00</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">إجمالي:</span>
                                <span>{self.invoice_data['total_amount']:.2f}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">المدفوع:</span>
                                <span>{self.invoice_data['paid_amount']:.2f}</span>
                            </div>
                            <div class="info-row final-total">
                                <span class="info-label">الرصيد الحالي:</span>
                                <span>{self.invoice_data['remaining_amount']:.2f}</span>
                            </div>
                        </div>
                        <div class="totals-right">
                            <div class="total-row">
                                <span>الإجمالي قبل الخصم:</span>
                                <span>{self.invoice_data['total_amount']:.2f}</span>
                            </div>
                            <div class="total-row">
                                <span>خصم عام:</span>
                                <span>{self.invoice_data['discount_amount']:.2f}</span>
                            </div>
                            <div class="total-row">
                                <span>صافي الفاتورة:</span>
                                <span>{self.invoice_data['final_amount']:.2f}</span>
                            </div>
                            <div class="total-row final-total">
                                <span>إجمالي الكمية:</span>
                                <span>{sum(item['quantity'] if item['quantity'] else 0 for item in self.invoice_items)}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="footer">
                        <p>شكراً لتعاملكم معنا</p>
                        <p>تم إنشاء هذه الفاتورة بواسطة نظام نقاط البيع - {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            self.preview_area.setHtml(html)
            
        except Exception as e:
            error_msg = f"خطأ في إنشاء معاينة الفاتورة:\n{str(e)}"
            self.preview_area.setPlainText(error_msg)
            print(f"خطأ في generate_professional_html: {str(e)}")
    
    def print_invoice(self):
        """طباعة الفاتورة"""
        try:
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            
            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec_() == QPrintDialog.Accepted:
                document = QTextDocument()
                document.setHtml(self.preview_area.toHtml())
                document.print_(printer)
                QMessageBox.information(self, "نجح", "تم إرسال الفاتورة للطباعة بنجاح!")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الطباعة:\n{str(e)}")
    
    def save_as_pdf(self):
        """حفظ الفاتورة كملف PDF"""
        try:
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ الفاتورة كـ PDF",
                f"فاتورة_{self.invoice_data['invoice_number']}.pdf",
                "PDF Files (*.pdf)"
            )
            
            if filename:
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(filename)
                printer.setPageSize(QPrinter.A4)
                
                document = QTextDocument()
                document.setHtml(self.preview_area.toHtml())
                document.print_(printer)
                
                QMessageBox.information(self, "نجح", f"تم حفظ الفاتورة بنجاح في:\n{filename}")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ PDF:\n{str(e)}")
