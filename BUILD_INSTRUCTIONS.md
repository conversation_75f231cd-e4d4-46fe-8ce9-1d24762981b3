# دليل بناء نظام نقاط البيع 🏗️
## Build Instructions for POS System

### 📋 المتطلبات الأساسية:
- Python 3.8 أو أحدث
- نظام Windows 10/11
- مساحة قرص صلب: 2 جيجابايت على الأقل
- ذاكرة RAM: 4 جيجابايت على الأقل

### 🚀 خطوات البناء السريعة:

#### الطريقة الأولى - التشغيل التلقائي:
```bash
# 1. تثبيت المتطلبات
install_requirements.bat

# 2. بناء الملف التنفيذي
build_exe.bat
```

#### الطريقة الثانية - التشغيل اليدوي:
```bash
# 1. تثبيت المتطلبات
pip install -r requirements.txt

# 2. إنشاء الأيقونة
python create_icon.py

# 3. بناء التطبيق
pyinstaller --clean pos_system.spec
```

### 📁 هيكل المجلدات بعد البناء:
```
d:/New folder (4)/
├── dist/
│   └── POS_System.exe          # 🎯 الملف التنفيذي النهائي
├── build/                      # ملفات البناء المؤقتة
├── ui/                         # ملفات واجهة المستخدم
├── database/                   # ملفات قاعدة البيانات
├── utils/                      # الأدوات المساعدة
├── temp/                       # ملفات مؤقتة
├── main.py                     # الملف الرئيسي
├── pos_system.spec            # ملف تكوين PyInstaller
├── requirements.txt           # قائمة المتطلبات
├── icon.ico                   # أيقونة التطبيق
└── version_info.txt           # معلومات الإصدار
```

### 🎯 النتيجة النهائية:
- **الملف التنفيذي**: `dist/POS_System.exe`
- **الحجم المتوقع**: 200-300 ميجابايت
- **نوع الملف**: تطبيق Windows مستقل
- **المتطلبات**: لا يحتاج تثبيت Python منفصل

### 🔧 حل المشاكل الشائعة:

#### مشكلة 1: خطأ في تثبيت PyQt5
```bash
# الحل
pip install PyQt5==5.15.9
pip install PyQt5-Qt5==5.15.2
```

#### مشكلة 2: خطأ في مكتبات البار كود
```bash
# الحل
pip install python-barcode[images]
pip install qrcode[pil]
pip install Pillow
```

#### مشكلة 3: ملف exe لا يعمل
- تأكد من وجود جميع المجلدات (database, ui, utils)
- تأكد من أن قاعدة البيانات في المكان الصحيح
- تشغيل التطبيق كمدير

### 🎨 تخصيص التطبيق:

#### تغيير الأيقونة:
1. استبدل `icon.ico` بأيقونة جديدة
2. أعد بناء التطبيق

#### تغيير معلومات الإصدار:
1. عدل `version_info.txt`
2. أعد بناء التطبيق

#### إضافة ملفات جديدة:
1. عدل `pos_system.spec` في قسم `datas`
2. أضف المجلدات الجديدة

### 📦 التوزيع:

#### الملفات المطلوبة للتوزيع:
```
التطبيق_النهائي/
├── POS_System.exe              # الملف التنفيذي
├── database/                   # قاعدة البيانات
├── config/                     # ملفات التكوين
├── temp/                       # مجلد مؤقت
├── README.md                   # دليل المستخدم
└── INSTALLATION_GUIDE.md      # دليل التثبيت
```

### 🔒 الأمان:
- التطبيق محمي بكلمة مرور افتراضية
- قاعدة البيانات مشفرة محلياً
- لا يتم إرسال بيانات خارجية

### 📞 الدعم:
- **المطور**: شادي الطيب
- **الهاتف**: 01008379651 / 01284860988
- **العنوان**: القاهرة

### 🔄 التحديثات:
- الإصدار الحالي: 1.0.0
- آخر تحديث: 2024
- التحديثات التلقائية: قيد التطوير

---

## 🎉 مبروك!
بعد إتمام البناء بنجاح، ستحصل على تطبيق نظام نقاط البيع مستقل تماماً يعمل على أي جهاز Windows بدون الحاجة لتثبيت Python أو أي مكتبات إضافية!