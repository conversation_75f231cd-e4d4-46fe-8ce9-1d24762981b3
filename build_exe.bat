@echo off
chcp 65001 > nul
title Building POS System

echo ===============================================
echo        Building POS and Accounting System
echo ===============================================
echo.

echo [1/6] Updating pip...
python -m pip install --upgrade pip

echo.
echo [2/6] Installing requirements...
pip install -r requirements.txt

echo.
echo [3/6] Creating icon...
python create_icon.py

echo.
echo [4/6] Cleaning previous builds...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "__pycache__" rmdir /s /q "__pycache__"

echo.
echo [5/6] Building executable...
pyinstaller --clean POS_System.spec

echo.
echo [6/6] Checking result...

if exist "dist\نظام_الطيب_للتجارة_والتوزيع.exe" (
    echo Build successful!
    echo.
    echo Executable file located at: dist\نظام_الطيب_للتجارة_والتوزيع.exe
    echo.
    echo File information:
    dir "dist\نظام_الطيب_للتجارة_والتوزيع.exe"
    echo.
    echo You can now run the application from: dist\نظام_الطيب_للتجارة_والتوزيع.exe
) else (
    echo Build failed!
    echo.
    echo Check error messages above
)

echo.
echo ===============================================
echo                Build Complete
echo ===============================================
echo.
pause
