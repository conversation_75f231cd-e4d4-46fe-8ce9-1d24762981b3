#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لجميع الوظائف - نظام الطيب للتجارة والتوزيع
"""

import sys
from PyQt5.QtWidgets import QApplication, QMessageBox

def test_imports():
    """اختبار استيراد جميع الوحدات"""
    print("🔍 اختبار استيراد الوحدات...")
    
    try:
        from database.db_manager import DatabaseManager
        print("✅ DatabaseManager - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ DatabaseManager - فشل الاستيراد: {e}")
        return False
    
    try:
        from ui.login_window import LoginWindow
        print("✅ LoginWindow - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ LoginWindow - فشل الاستيراد: {e}")
        return False
    
    try:
        from ui.modern_dashboard import ModernDashboard
        print("✅ ModernDashboard - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ ModernDashboard - فشل الاستيراد: {e}")
        return False
    
    # اختبار الوحدات الوظيفية
    modules = [
        ('ui.products_window', 'ProductsWindow'),
        ('ui.customers_window', 'CustomersWindow'),
        ('ui.suppliers_window', 'SuppliersWindow'),
        ('ui.sales_window', 'SalesWindow'),
        ('ui.purchases_main', 'PurchasesMainWindow'),
        ('ui.basic_returns', 'BasicReturnsWindow'),
        ('ui.banking_window', 'BankingWindow'),
        ('ui.reports_window', 'ReportsWindow'),
        ('ui.users_window', 'UsersWindow'),
        ('ui.expenses_window', 'ExpensesWindow'),
        ('ui.financial_management', 'FinancialManagementWindow'),
        ('ui.multi_sales_window', 'MultiSalesWindow'),
        ('ui.backup_window', 'BackupWindow')
    ]
    
    for module_name, class_name in modules:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {class_name} - تم الاستيراد بنجاح")
        except ImportError as e:
            print(f"⚠️ {class_name} - فشل الاستيراد: {e}")
        except AttributeError as e:
            print(f"⚠️ {class_name} - الفئة غير موجودة: {e}")
    
    return True

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    
    try:
        from database.db_manager import DatabaseManager
        db = DatabaseManager()
        print("✅ تم إنشاء قاعدة البيانات بنجاح")
        
        # اختبار الجداول الأساسية
        tables = ['users', 'customers', 'suppliers', 'products', 'sales_invoices']
        for table in tables:
            try:
                result = db.execute_query(f"SELECT COUNT(*) FROM {table}")
                count = result[0][0] if result else 0
                print(f"✅ جدول {table}: {count} سجل")
            except Exception as e:
                print(f"⚠️ جدول {table}: خطأ - {e}")
        
        return True
    except Exception as e:
        print(f"❌ فشل في اختبار قاعدة البيانات: {e}")
        return False

def test_ui_creation():
    """اختبار إنشاء الواجهات"""
    print("\n🎨 اختبار إنشاء الواجهات...")
    
    try:
        app = QApplication(sys.argv)
        
        from database.db_manager import DatabaseManager
        from ui.modern_dashboard import ModernDashboard
        
        db = DatabaseManager()
        user_data = {'username': 'admin', 'role': 'admin'}
        
        dashboard = ModernDashboard(db, user_data)
        print("✅ تم إنشاء الشاشة الرئيسية الحديثة بنجاح")
        
        # اختبار الإحصائيات
        stats = dashboard.get_real_stats()
        print(f"✅ الإحصائيات: {stats}")
        
        return True
    except Exception as e:
        print(f"❌ فشل في اختبار الواجهات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار نظام الطيب للتجارة والتوزيع")
    print("=" * 50)
    
    # اختبار الاستيرادات
    if not test_imports():
        print("\n❌ فشل في اختبار الاستيرادات")
        return 1
    
    # اختبار قاعدة البيانات
    if not test_database():
        print("\n❌ فشل في اختبار قاعدة البيانات")
        return 1
    
    # اختبار الواجهات
    if not test_ui_creation():
        print("\n❌ فشل في اختبار الواجهات")
        return 1
    
    print("\n" + "=" * 50)
    print("🎉 جميع الاختبارات نجحت!")
    print("✅ النظام جاهز للاستخدام")
    print("\n🚀 لتشغيل النظام:")
    print("   python modern_app.py")
    print("   أو انقر مرتين على start_modern.bat")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
