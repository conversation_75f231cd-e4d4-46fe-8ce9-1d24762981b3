@echo off
chcp 65001 > nul
title تثبيت متطلبات نظام نقاط البيع

echo ════════════════════════════════════════════════════════════════
echo           تثبيت متطلبات نظام نقاط البيع والمحاسبة
echo           Installing POS System Requirements
echo ════════════════════════════════════════════════════════════════
echo.

echo [1/4] تحديث pip...
echo [1/4] Updating pip...
python -m pip install --upgrade pip

echo.
echo [2/4] تثبيت المتطلبات الأساسية...
echo [2/4] Installing basic requirements...
pip install -r requirements.txt

echo.
echo [3/4] تثبيت متطلبات البار كود...
echo [3/4] Installing barcode requirements...
pip install -r requirements_barcode.txt

echo.
echo [4/4] التحقق من التثبيت...
echo [4/4] Verifying installation...

python -c "import PyQt5; print('✅ PyQt5 installed successfully')"
python -c "import barcode; print('✅ Barcode library installed successfully')"
python -c "import qrcode; print('✅ QR Code library installed successfully')"
python -c "import PIL; print('✅ PIL/Pillow installed successfully')"
python -c "import pyinstaller; print('✅ PyInstaller installed successfully')"

echo.
echo ✅ تم تثبيت جميع المتطلبات بنجاح!
echo ✅ All requirements installed successfully!
echo.
echo 🚀 يمكنك الآن بناء التطبيق باستخدام: build_exe.bat
echo 🚀 You can now build the application using: build_exe.bat
echo.
pause