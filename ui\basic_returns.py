#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المرتجعات الأساسي - بدون أخطاء
Basic Returns System - Error Free
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QLineEdit, QDialog, QFormLayout,
                            QMessageBox, QGroupBox, QDateEdit, QTextEdit,
                            QTableWidget, QTableWidgetItem, QComboBox, QSpinBox,
                            QTabWidget, QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, QDate
from datetime import datetime

class BasicReturnsWindow(QWidget):
    """نافذة المرتجعات الأساسية"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة المرتجعات")
        self.setGeometry(100, 100, 1200, 800)

        layout = QVBoxLayout()

        # العنوان
        title_label = QLabel("إدارة المرتجعات")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 15px;
                border-radius: 10px;
                margin: 10px;
            }
        """)
        layout.addWidget(title_label)

        # الأزرار
        buttons_layout = QHBoxLayout()

        # زر مرتجع المبيعات
        sales_return_btn = QPushButton("🔄 مرتجع مبيعات جديد")
        sales_return_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                font-size: 16px;
                padding: 15px 30px;
                border-radius: 10px;
                border: none;
                min-height: 50px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        sales_return_btn.clicked.connect(self.open_sales_return)
        buttons_layout.addWidget(sales_return_btn)

        # زر مرتجع المشتريات
        purchase_return_btn = QPushButton("🔄 مرتجع مشتريات جديد")
        purchase_return_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                font-size: 16px;
                padding: 15px 30px;
                border-radius: 10px;
                border: none;
                min-height: 50px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        purchase_return_btn.clicked.connect(self.open_purchase_return)
        buttons_layout.addWidget(purchase_return_btn)

        # زر تحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                font-size: 16px;
                padding: 15px 30px;
                border-radius: 10px;
                border: none;
                min-height: 50px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        refresh_btn.clicked.connect(self.load_returns)
        buttons_layout.addWidget(refresh_btn)

        layout.addLayout(buttons_layout)

        # فهرس المرتجعات
        returns_group = QGroupBox("فهرس المرتجعات")
        returns_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin: 10px;
                padding-top: 15px;
                font-size: 16px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        returns_layout = QVBoxLayout()

        # جدول المرتجعات
        self.returns_table = QTableWidget()
        self.returns_table.setColumnCount(8)
        self.returns_table.setHorizontalHeaderLabels([
            "رقم المرتجع", "النوع", "التاريخ", "الفاتورة الأصلية",
            "العميل/المورد", "المبلغ", "السبب", "تاريخ الإنشاء"
        ])

        # تنسيق الجدول
        self.returns_table.horizontalHeader().setStretchLastSection(True)
        self.returns_table.setAlternatingRowColors(True)
        self.returns_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.returns_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
            QHeaderView::section {
                background-color: #6c757d;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: none;
                border-right: 1px solid #495057;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)

        returns_layout.addWidget(self.returns_table)
        returns_group.setLayout(returns_layout)
        layout.addWidget(returns_group)

        self.setLayout(layout)

        # تحميل المرتجعات
        self.load_returns()
        
    def open_sales_return(self):
        """فتح نافذة مرتجع المبيعات"""
        try:
            dialog = AdvancedSalesReturnDialog(self.db_manager, self.user_data, self)
            result = dialog.exec_()
            if result == QDialog.Accepted:
                print("🔄 تحديث فهرس المرتجعات بعد إضافة مرتجع جديد...")
                self.load_returns()  # تحديث الجدول فوراً
                print("✅ تم تحديث فهرس المرتجعات")
        except Exception as e:
            print(f"خطأ في مرتجع المبيعات: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ:\n{str(e)}")

    def open_purchase_return(self):
        """فتح نافذة مرتجع المشتريات"""
        try:
            dialog = AdvancedPurchaseReturnDialog(self.db_manager, self.user_data, self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_returns()  # تحديث الجدول
                QMessageBox.information(self, "نجح", "تم إنشاء مرتجع المشتريات بنجاح!")
        except Exception as e:
            print(f"خطأ في مرتجع المشتريات: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ:\n{str(e)}")

    def load_returns(self):
        """تحميل فهرس المرتجعات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # إنشاء جدول المرتجعات إذا لم يكن موجوداً
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS advanced_returns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    return_number TEXT UNIQUE NOT NULL,
                    return_type TEXT NOT NULL,
                    return_date TEXT NOT NULL,
                    original_invoice_id INTEGER,
                    original_invoice_number TEXT,
                    customer_supplier_name TEXT,
                    total_amount REAL NOT NULL DEFAULT 0,
                    reason TEXT,
                    user_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # إنشاء جدول تفاصيل المرتجعات إذا لم يكن موجوداً
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS advanced_return_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    return_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    product_name TEXT,
                    return_quantity REAL NOT NULL,
                    unit_price REAL NOT NULL,
                    total_price REAL NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (return_id) REFERENCES advanced_returns (id)
                )
            """)

            # التحقق من وجود بيانات في الجدول مع طباعة تفصيلية
            cursor.execute("SELECT COUNT(*) FROM advanced_returns")
            count = cursor.fetchone()[0]
            print(f"📊 إجمالي المرتجعات في قاعدة البيانات: {count}")

            # تحميل جميع المرتجعات (حتى لو كان العدد صفر)
            cursor.execute("""
                SELECT return_number, return_type, return_date,
                       COALESCE(original_invoice_number, 'غير محدد') as invoice_number,
                       COALESCE(customer_supplier_name, 'غير محدد') as client_name,
                       total_amount,
                       COALESCE(reason, 'غير محدد') as reason,
                       datetime(created_at, 'localtime') as created_at_local
                FROM advanced_returns
                ORDER BY created_at DESC
                LIMIT 100
            """)

            returns = cursor.fetchall()
            print(f"📋 تم استرجاع {len(returns)} مرتجع من قاعدة البيانات")

            # طباعة تفاصيل كل مرتجع للتشخيص
            for i, ret in enumerate(returns):
                print(f"   {i+1}. {ret[0]} - {ret[1]} - {ret[4]} - {ret[5]:.2f} جنيه")

            conn.close()

            # عرض البيانات في الجدول
            self.returns_table.setRowCount(len(returns))

            if len(returns) == 0:
                print("📝 الجدول فارغ - لا توجد مرتجعات لعرضها")
                return

            for row, return_data in enumerate(returns):
                for col, value in enumerate(return_data):
                    # تنسيق القيم
                    if col == 5:  # عمود المبلغ
                        display_value = f"{float(value):.2f} جنيه" if value else "0.00 جنيه"
                    elif col == 7:  # عمود التاريخ
                        display_value = str(value)[:19] if value else "غير محدد"  # عرض التاريخ والوقت
                    else:
                        display_value = str(value) if value else "غير محدد"

                    item = QTableWidgetItem(display_value)
                    item.setTextAlignment(Qt.AlignCenter)

                    # تلوين حسب النوع
                    if col == 1:  # عمود النوع
                        if "مبيعات" in str(value):
                            item.setBackground(Qt.lightBlue)
                        elif "مشتريات" in str(value):
                            item.setBackground(Qt.lightCoral)

                    self.returns_table.setItem(row, col, item)

            # تعديل عرض الأعمدة
            header = self.returns_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم المرتجع
            header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # النوع
            header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # التاريخ
            header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الفاتورة
            header.setSectionResizeMode(4, QHeaderView.Stretch)           # العميل/المورد
            header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # المبلغ
            header.setSectionResizeMode(6, QHeaderView.Stretch)           # السبب
            header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # تاريخ الإنشاء

            print(f"✅ تم عرض {len(returns)} مرتجع في الجدول بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تحميل المرتجعات: {str(e)}")
            import traceback
            traceback.print_exc()

            # عرض جدول فارغ
            self.returns_table.setRowCount(0)
            print("📋 سيتم عرض جدول فارغ بسبب الخطأ")


class AdvancedSalesReturnDialog(QDialog):
    """نافذة مرتجع المبيعات الأساسية"""
    
    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        self.selected_invoice = None
        self.invoice_items = []
        self.return_items = []
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("مرتجع مبيعات جديد - متقدم")
        self.setFixedSize(1000, 700)

        layout = QVBoxLayout()

        # العنوان
        title_label = QLabel("إنشاء مرتجع مبيعات جديد")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: white;
                background-color: #3498db;
                padding: 15px;
                border-radius: 8px;
                margin: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # معلومات المرتجع
        info_group = QGroupBox("معلومات المرتجع")
        info_layout = QFormLayout()

        # رقم المرتجع
        self.return_number_input = QLineEdit()
        self.return_number_input.setText(self.generate_return_number())
        self.return_number_input.setReadOnly(True)
        self.return_number_input.setStyleSheet("background-color: #f8f9fa; font-weight: bold;")
        info_layout.addRow("رقم المرتجع:", self.return_number_input)

        # التاريخ
        self.return_date = QDateEdit()
        self.return_date.setDate(QDate.currentDate())
        self.return_date.setCalendarPopup(True)
        info_layout.addRow("تاريخ المرتجع:", self.return_date)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        # اختيار الفاتورة
        invoice_group = QGroupBox("اختيار الفاتورة الأصلية")
        invoice_layout = QVBoxLayout()

        # البحث
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("البحث:"))

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("رقم الفاتورة أو اسم العميل...")
        self.search_input.textChanged.connect(self.search_invoices)
        search_layout.addWidget(self.search_input)

        search_btn = QPushButton("🔍 بحث")
        search_btn.clicked.connect(self.search_invoices)
        search_layout.addWidget(search_btn)

        invoice_layout.addLayout(search_layout)

        # قائمة الفواتير
        self.invoices_combo = QComboBox()
        self.invoices_combo.currentIndexChanged.connect(self.on_invoice_selected)
        invoice_layout.addWidget(self.invoices_combo)

        # معلومات الفاتورة المختارة
        self.invoice_info_label = QLabel("اختر فاتورة لعرض التفاصيل...")
        self.invoice_info_label.setStyleSheet("""
            QLabel {
                background-color: #e9ecef;
                padding: 10px;
                border-radius: 5px;
                border: 1px solid #ced4da;
            }
        """)
        invoice_layout.addWidget(self.invoice_info_label)

        invoice_group.setLayout(invoice_layout)
        layout.addWidget(invoice_group)

        # أصناف الفاتورة
        self.items_group = QGroupBox("أصناف الفاتورة")
        self.items_group.setVisible(False)  # مخفي في البداية
        items_layout = QVBoxLayout()

        # جدول الأصناف
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(6)
        self.items_table.setHorizontalHeaderLabels([
            "الصنف", "الكمية الأصلية", "كمية المرتجع", "السعر", "إجمالي المرتجع", "إجراء"
        ])
        self.items_table.horizontalHeader().setStretchLastSection(True)
        self.items_table.setAlternatingRowColors(True)
        self.items_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QHeaderView::section {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 8px;
                border: none;
            }
        """)
        items_layout.addWidget(self.items_table)

        # إجمالي المرتجع
        total_layout = QHBoxLayout()
        total_layout.addStretch()
        total_layout.addWidget(QLabel("إجمالي المرتجع:"))
        self.total_label = QLabel("0.00 جنيه")
        self.total_label.setStyleSheet("font-weight: bold; font-size: 16px; color: #e74c3c;")
        total_layout.addWidget(self.total_label)
        items_layout.addLayout(total_layout)

        self.items_group.setLayout(items_layout)
        layout.addWidget(self.items_group)

        # السبب
        reason_group = QGroupBox("سبب المرتجع")
        reason_layout = QVBoxLayout()

        self.reason_input = QTextEdit()
        self.reason_input.setPlaceholderText("اكتب سبب المرتجع...")
        self.reason_input.setMaximumHeight(80)
        reason_layout.addWidget(self.reason_input)

        reason_group.setLayout(reason_layout)
        layout.addWidget(reason_group)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("حفظ المرتجع")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 12px 24px;
                border-radius: 6px;
                border: none;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_btn.clicked.connect(self.save_return)
        buttons_layout.addWidget(save_btn)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                padding: 12px 24px;
                border-radius: 6px;
                border: none;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)

        # تحميل الفواتير
        self.load_invoices()

    def generate_return_number(self):
        """توليد رقم مرتجع جديد"""
        today = datetime.now().strftime("%Y%m%d")
        import random
        return f"SR{today}{random.randint(100, 999)}"

    def load_invoices(self):
        """تحميل فواتير البيع"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # التحقق من هيكل جدول الفواتير
            cursor.execute("PRAGMA table_info(invoices)")
            columns_info = cursor.fetchall()
            column_names = [col[1] for col in columns_info]

            print(f"أعمدة جدول invoices: {column_names}")

            # استعلام مرن حسب الأعمدة المتوفرة
            if 'invoice_type' in column_names and 'final_amount' in column_names:
                # الاستعلام الكامل
                cursor.execute("""
                    SELECT i.id, i.invoice_number,
                           COALESCE(c.name, 'عميل نقدي') as customer_name,
                           i.invoice_date, i.final_amount
                    FROM invoices i
                    LEFT JOIN customers c ON i.customer_id = c.id
                    WHERE i.invoice_type = 'sale'
                    ORDER BY i.invoice_date DESC
                    LIMIT 100
                """)
            elif 'total_amount' in column_names:
                # استعلام بديل مع total_amount
                cursor.execute("""
                    SELECT i.id, i.invoice_number,
                           COALESCE(c.name, 'عميل نقدي') as customer_name,
                           i.invoice_date, i.total_amount
                    FROM invoices i
                    LEFT JOIN customers c ON i.customer_id = c.id
                    WHERE i.invoice_type = 'sale'
                    ORDER BY i.invoice_date DESC
                    LIMIT 100
                """)
            else:
                # استعلام أساسي
                cursor.execute("""
                    SELECT i.id, i.invoice_number,
                           COALESCE(c.name, 'عميل نقدي') as customer_name,
                           i.invoice_date, 0 as amount
                    FROM invoices i
                    LEFT JOIN customers c ON i.customer_id = c.id
                    ORDER BY i.invoice_date DESC
                    LIMIT 100
                """)

            invoices = cursor.fetchall()
            conn.close()

            print(f"تم العثور على {len(invoices)} فاتورة")

            self.invoices_combo.clear()
            self.invoices_combo.addItem("اختر فاتورة...", None)

            for invoice in invoices:
                try:
                    invoice_id = invoice[0]
                    invoice_number = invoice[1] or f"فاتورة {invoice_id}"
                    customer_name = invoice[2] or 'عميل نقدي'
                    invoice_date = invoice[3] or 'غير محدد'
                    amount = float(invoice[4]) if invoice[4] else 0.0

                    display_text = f"{invoice_number} - {customer_name} - {amount:.2f} جنيه - {invoice_date}"
                    self.invoices_combo.addItem(display_text, invoice)

                except Exception as invoice_error:
                    print(f"خطأ في معالجة فاتورة: {str(invoice_error)}")
                    continue

        except Exception as e:
            print(f"خطأ في تحميل الفواتير: {str(e)}")
            import traceback
            traceback.print_exc()

            # رسالة خطأ مفصلة
            error_message = f"خطأ في تحميل فواتير البيع:\n{str(e)}\n\nتفاصيل إضافية:\n"

            if "no such table" in str(e).lower():
                error_message += "• جدول invoices غير موجود في قاعدة البيانات"
            elif "no such column" in str(e).lower():
                error_message += "• هيكل جدول invoices غير متوافق"
            else:
                error_message += f"• خطأ تقني: {str(e)}"

            QMessageBox.critical(self, "خطأ في تحميل الفواتير", error_message)

    def search_invoices(self):
        """البحث في الفواتير"""
        search_text = self.search_input.text().strip()
        if not search_text:
            self.load_invoices()
            return

        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # التحقق من الأعمدة المتوفرة
            cursor.execute("PRAGMA table_info(invoices)")
            columns_info = cursor.fetchall()
            column_names = [col[1] for col in columns_info]

            # استعلام البحث المرن
            if 'invoice_type' in column_names and 'final_amount' in column_names:
                cursor.execute("""
                    SELECT i.id, i.invoice_number,
                           COALESCE(c.name, 'عميل نقدي') as customer_name,
                           i.invoice_date, i.final_amount
                    FROM invoices i
                    LEFT JOIN customers c ON i.customer_id = c.id
                    WHERE i.invoice_type = 'sale'
                    AND (i.invoice_number LIKE ? OR COALESCE(c.name, '') LIKE ?)
                    ORDER BY i.invoice_date DESC
                    LIMIT 50
                """, (f"%{search_text}%", f"%{search_text}%"))
            else:
                # استعلام بديل
                cursor.execute("""
                    SELECT i.id, i.invoice_number,
                           COALESCE(c.name, 'عميل نقدي') as customer_name,
                           i.invoice_date, COALESCE(i.total_amount, 0) as amount
                    FROM invoices i
                    LEFT JOIN customers c ON i.customer_id = c.id
                    WHERE (i.invoice_number LIKE ? OR COALESCE(c.name, '') LIKE ?)
                    ORDER BY i.invoice_date DESC
                    LIMIT 50
                """, (f"%{search_text}%", f"%{search_text}%"))

            invoices = cursor.fetchall()
            conn.close()

            self.invoices_combo.clear()
            self.invoices_combo.addItem("اختر فاتورة...", None)

            if not invoices:
                self.invoices_combo.addItem("لا توجد نتائج للبحث", None)
                return

            for invoice in invoices:
                try:
                    customer_name = invoice[2] or 'عميل نقدي'
                    amount = float(invoice[4]) if invoice[4] else 0.0
                    display_text = f"{invoice[1]} - {customer_name} - {amount:.2f} جنيه - {invoice[3]}"
                    self.invoices_combo.addItem(display_text, invoice)
                except Exception as invoice_error:
                    print(f"خطأ في معالجة نتيجة البحث: {str(invoice_error)}")
                    continue

        except Exception as e:
            print(f"خطأ في البحث: {str(e)}")
            QMessageBox.critical(self, "خطأ في البحث", f"خطأ في البحث عن الفواتير:\n{str(e)}")

    def on_invoice_selected(self):
        """عند اختيار فاتورة"""
        self.selected_invoice = self.invoices_combo.currentData()

        if self.selected_invoice:
            # عرض معلومات الفاتورة
            customer_name = self.selected_invoice[2] or 'عميل نقدي'
            info_text = (f"📋 رقم الفاتورة: {self.selected_invoice[1]}\n"
                        f"👤 العميل: {customer_name}\n"
                        f"📅 التاريخ: {self.selected_invoice[3]}\n"
                        f"💰 إجمالي الفاتورة: {self.selected_invoice[4]:.2f} جنيه")
            self.invoice_info_label.setText(info_text)

            # تحميل أصناف الفاتورة
            self.load_invoice_items()
            self.items_group.setVisible(True)
        else:
            self.invoice_info_label.setText("اختر فاتورة لعرض التفاصيل...")
            self.items_group.setVisible(False)

    def load_invoice_items(self):
        """تحميل أصناف الفاتورة"""
        if not self.selected_invoice:
            return

        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # التحقق من هيكل جدول invoice_items أولاً
            cursor.execute("PRAGMA table_info(invoice_items)")
            columns_info = cursor.fetchall()
            column_names = [col[1] for col in columns_info]

            print(f"أعمدة جدول invoice_items: {column_names}")

            # استعلام مرن يتكيف مع هيكل الجدول
            if 'product_id' in column_names:
                # الاستعلام الأساسي مع ربط المنتجات
                cursor.execute("""
                    SELECT ii.id, ii.product_id, ii.quantity, ii.unit_price,
                           ii.total_price, p.name as product_name
                    FROM invoice_items ii
                    LEFT JOIN products p ON ii.product_id = p.id
                    WHERE ii.invoice_id = ?
                """, (self.selected_invoice[0],))
            else:
                # استعلام بديل إذا كان هيكل الجدول مختلف
                cursor.execute("""
                    SELECT id, product_name, quantity, unit_price, total_price, product_name
                    FROM invoice_items
                    WHERE invoice_id = ?
                """, (self.selected_invoice[0],))

            items = cursor.fetchall()
            print(f"تم العثور على {len(items)} صنف في الفاتورة")

            if not items:
                QMessageBox.information(self, "تنبيه", "لا توجد أصناف في هذه الفاتورة")
                conn.close()
                return

            conn.close()

            self.invoice_items = items
            self.return_items = []

            # عرض الأصناف في الجدول
            self.items_table.setRowCount(len(items))

            for row, item in enumerate(items):
                try:
                    # التعامل مع البيانات بشكل آمن
                    item_id = item[0] if len(item) > 0 else 0
                    product_id = item[1] if len(item) > 1 else 0
                    quantity = float(item[2]) if len(item) > 2 and item[2] else 0
                    unit_price = float(item[3]) if len(item) > 3 and item[3] else 0
                    total_price = float(item[4]) if len(item) > 4 and item[4] else 0
                    product_name = str(item[5]) if len(item) > 5 and item[5] else f"منتج {product_id}"

                    # اسم الصنف
                    self.items_table.setItem(row, 0, QTableWidgetItem(product_name))

                    # الكمية الأصلية
                    self.items_table.setItem(row, 1, QTableWidgetItem(str(quantity)))

                    # كمية المرتجع (SpinBox)
                    return_qty_spin = QSpinBox()
                    return_qty_spin.setMinimum(0)
                    return_qty_spin.setMaximum(int(quantity))
                    return_qty_spin.valueChanged.connect(lambda: self.update_return_total())
                    self.items_table.setCellWidget(row, 2, return_qty_spin)

                    # السعر
                    self.items_table.setItem(row, 3, QTableWidgetItem(f"{unit_price:.2f}"))

                    # إجمالي المرتجع (سيتم تحديثه)
                    self.items_table.setItem(row, 4, QTableWidgetItem("0.00"))

                    # زر إضافة/إزالة
                    toggle_btn = QPushButton("إضافة")
                    toggle_btn.setStyleSheet("""
                        QPushButton {
                            background-color: #28a745;
                            color: white;
                            border: none;
                            padding: 5px 10px;
                            border-radius: 3px;
                        }
                        QPushButton:hover {
                            background-color: #218838;
                        }
                    """)
                    toggle_btn.clicked.connect(lambda checked, r=row: self.toggle_item_return(r))
                    self.items_table.setCellWidget(row, 5, toggle_btn)

                    # إضافة بيانات الصنف للقائمة
                    self.return_items.append({
                        'item_id': item_id,
                        'product_id': product_id,
                        'product_name': product_name,
                        'original_quantity': quantity,
                        'unit_price': unit_price,
                        'return_quantity': 0,
                        'included': False
                    })

                    print(f"تم إضافة الصنف: {product_name} - كمية: {quantity}")

                except Exception as item_error:
                    print(f"خطأ في معالجة الصنف رقم {row}: {str(item_error)}")
                    continue

        except Exception as e:
            print(f"خطأ في تحميل أصناف الفاتورة: {str(e)}")
            import traceback
            traceback.print_exc()

            # رسالة خطأ مفصلة للمستخدم
            error_message = f"خطأ في تحميل أصناف الفاتورة:\n{str(e)}\n\nتفاصيل إضافية:\n"

            if "no such table" in str(e).lower():
                error_message += "• جدول invoice_items غير موجود في قاعدة البيانات"
            elif "no such column" in str(e).lower():
                error_message += "• هيكل جدول invoice_items غير متوافق"
            else:
                error_message += f"• خطأ تقني: {str(e)}"

            QMessageBox.critical(self, "خطأ في تحميل الأصناف", error_message)

    def toggle_item_return(self, row):
        """تبديل حالة إضافة الصنف للمرتجع"""
        if row >= len(self.return_items):
            return

        item = self.return_items[row]
        toggle_btn = self.items_table.cellWidget(row, 5)
        return_qty_spin = self.items_table.cellWidget(row, 2)

        if item['included']:
            # إزالة من المرتجع
            item['included'] = False
            item['return_quantity'] = 0
            return_qty_spin.setValue(0)
            return_qty_spin.setEnabled(False)
            toggle_btn.setText("إضافة")
            toggle_btn.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
            """)
        else:
            # إضافة للمرتجع
            item['included'] = True
            return_qty_spin.setEnabled(True)
            return_qty_spin.setValue(1)  # قيمة افتراضية
            toggle_btn.setText("إزالة")
            toggle_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """)

        self.update_return_total()

    def update_return_total(self):
        """تحديث إجمالي المرتجع"""
        total = 0.0

        for row in range(self.items_table.rowCount()):
            if row < len(self.return_items) and self.return_items[row]['included']:
                return_qty_spin = self.items_table.cellWidget(row, 2)
                return_qty = return_qty_spin.value()
                unit_price = self.return_items[row]['unit_price']
                item_total = return_qty * unit_price

                # تحديث الكمية في البيانات
                self.return_items[row]['return_quantity'] = return_qty

                # تحديث إجمالي الصف
                self.items_table.setItem(row, 4, QTableWidgetItem(f"{item_total:.2f}"))

                total += item_total
            else:
                self.items_table.setItem(row, 4, QTableWidgetItem("0.00"))

        self.total_label.setText(f"{total:.2f} جنيه")
        
    def save_return(self):
        """حفظ المرتجع"""
        # التحقق من البيانات
        if not self.selected_invoice:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة أولاً")
            return

        # التحقق من وجود أصناف للمرتجع
        return_products = [item for item in self.return_items if item['included'] and item['return_quantity'] > 0]
        if not return_products:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار أصناف للمرتجع وتحديد الكميات")
            return
            
        # حساب إجمالي المرتجع
        total_amount = sum(item['return_quantity'] * item['unit_price'] for item in return_products)

        # تأكيد من المستخدم مع توضيح التأثير على الرصيد
        customer_name = self.selected_invoice[2] or 'عميل نقدي'

        # الحصول على رصيد العميل الحالي للعرض
        current_balance_text = "غير محدد"
        try:
            conn_temp = self.db_manager.get_connection()
            cursor_temp = conn_temp.cursor()
            cursor_temp.execute("SELECT balance FROM customers WHERE name = ?", (customer_name,))
            balance_result = cursor_temp.fetchone()
            if balance_result:
                current_balance = float(balance_result[0]) if balance_result[0] else 0.0
                new_balance = current_balance - total_amount
                current_balance_text = f"{current_balance:.2f} جنيه → {new_balance:.2f} جنيه"
            conn_temp.close()
        except:
            current_balance_text = "سيتم تحديثه"

        reply = QMessageBox.question(self, "تأكيد المرتجع",
            f"هل أنت متأكد من إنشاء مرتجع المبيعات؟\n\n"
            f"📋 رقم المرتجع: {self.return_number_input.text()}\n"
            f"📋 الفاتورة الأصلية: {self.selected_invoice[1]}\n"
            f"👤 العميل: {customer_name}\n"
            f"💰 مبلغ المرتجع: {total_amount:.2f} جنيه\n"
            f"📦 عدد الأصناف: {len(return_products)}\n\n"
            f"📊 التأثير على النظام:\n"
            f"• سيتم إضافة الكميات للمخزون\n"
            f"• سيتم خصم المبلغ من رصيد العميل\n"
            f"• رصيد العميل: {current_balance_text}",
            QMessageBox.Yes | QMessageBox.No)

        if reply != QMessageBox.Yes:
            return
            
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # إنشاء جدول المرتجعات المتقدم
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS advanced_returns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    return_number TEXT UNIQUE NOT NULL,
                    return_type TEXT NOT NULL,
                    return_date TEXT NOT NULL,
                    original_invoice_id INTEGER,
                    original_invoice_number TEXT,
                    customer_supplier_name TEXT,
                    total_amount REAL NOT NULL DEFAULT 0,
                    reason TEXT,
                    user_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # إنشاء جدول تفاصيل المرتجعات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS advanced_return_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    return_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    product_name TEXT,
                    return_quantity REAL NOT NULL,
                    unit_price REAL NOT NULL,
                    total_price REAL NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (return_id) REFERENCES advanced_returns (id)
                )
            """)

            # إدراج المرتجع الرئيسي
            cursor.execute("""
                INSERT INTO advanced_returns
                (return_number, return_type, return_date, original_invoice_id,
                 original_invoice_number, customer_supplier_name, total_amount,
                 reason, user_id, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
            """, (
                self.return_number_input.text(),
                "مرتجع مبيعات",
                self.return_date.date().toString('yyyy-MM-dd'),
                self.selected_invoice[0],  # invoice_id
                self.selected_invoice[1],  # invoice_number
                customer_name,
                total_amount,
                self.reason_input.toPlainText().strip(),
                self.user_data.get('id', 1)
            ))

            return_id = cursor.lastrowid
            print(f"✅ تم حفظ المرتجع برقم ID: {return_id}")
            print(f"✅ رقم المرتجع: {self.return_number_input.text()}")
            print(f"✅ نوع المرتجع: مرتجع مبيعات")
            print(f"✅ العميل: {customer_name}")
            print(f"✅ المبلغ: {total_amount:.2f}")

            # إدراج تفاصيل المرتجع
            for item in return_products:
                cursor.execute("""
                    INSERT INTO advanced_return_items
                    (return_id, product_id, product_name, return_quantity,
                     unit_price, total_price, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
                """, (
                    return_id,
                    item['product_id'],
                    item['product_name'],
                    item['return_quantity'],
                    item['unit_price'],
                    item['return_quantity'] * item['unit_price']
                ))

                # تحديث المخزون (إضافة الكمية المرتجعة)
                try:
                    cursor.execute("""
                        UPDATE products
                        SET current_stock = current_stock + ?
                        WHERE id = ?
                    """, (item['return_quantity'], item['product_id']))
                    print(f"✅ تم تحديث مخزون {item['product_name']}: +{item['return_quantity']}")
                except Exception as stock_error:
                    print(f"⚠️ تحذير: خطأ في تحديث المخزون للمنتج {item['product_name']}: {str(stock_error)}")

            # تحديث رصيد العميل والفاتورة
            customer_balance_updated = False
            try:
                # الحصول على معرف العميل من الفاتورة الأصلية
                cursor.execute("""
                    SELECT customer_id, final_amount, payment_status
                    FROM invoices
                    WHERE id = ?
                """, (self.selected_invoice[0],))

                invoice_info = cursor.fetchone()

                if invoice_info and invoice_info[0]:  # إذا كان هناك عميل مرتبط بالفاتورة
                    customer_id = invoice_info[0]
                    original_invoice_amount = float(invoice_info[1]) if invoice_info[1] else 0.0
                    payment_status = invoice_info[2] if invoice_info[2] else 'unpaid'

                    print(f"🔍 معلومات الفاتورة الأصلية:")
                    print(f"   معرف العميل: {customer_id}")
                    print(f"   مبلغ الفاتورة: {original_invoice_amount:.2f}")
                    print(f"   حالة الدفع: {payment_status}")

                    # الحصول على بيانات العميل
                    cursor.execute("""
                        SELECT id, name, balance FROM customers WHERE id = ?
                    """, (customer_id,))

                    customer_data = cursor.fetchone()

                    if customer_data:
                        customer_name_db = customer_data[1]
                        current_balance = float(customer_data[2]) if customer_data[2] else 0.0

                        # حساب الرصيد الجديد
                        # إذا كانت الفاتورة مدفوعة، نخصم المبلغ المرتجع من رصيد العميل
                        # إذا كانت غير مدفوعة، نقلل من مديونية العميل
                        if payment_status == 'paid':
                            # فاتورة مدفوعة - نرد المبلغ للعميل (نقلل من رصيده المدين أو نزيد رصيده الدائن)
                            new_balance = current_balance - total_amount
                            balance_action = "خصم من الرصيد (رد مبلغ)"
                        else:
                            # فاتورة غير مدفوعة - نقلل من مديونية العميل
                            new_balance = current_balance + total_amount
                            balance_action = "تقليل المديونية"

                        # تحديث رصيد العميل
                        cursor.execute("""
                            UPDATE customers
                            SET balance = ?
                            WHERE id = ?
                        """, (new_balance, customer_id))

                        # تحديث مبلغ الفاتورة الأصلية
                        new_invoice_amount = original_invoice_amount - total_amount
                        cursor.execute("""
                            UPDATE invoices
                            SET final_amount = ?
                            WHERE id = ?
                        """, (new_invoice_amount, self.selected_invoice[0]))

                        customer_balance_updated = True

                        print(f"✅ تم تحديث بيانات العميل {customer_name_db} (ID: {customer_id}):")
                        print(f"   الرصيد السابق: {current_balance:.2f}")
                        print(f"   المبلغ المرتجع: {total_amount:.2f}")
                        print(f"   الرصيد الجديد: {new_balance:.2f}")
                        print(f"   نوع التحديث: {balance_action}")
                        print(f"✅ تم تحديث مبلغ الفاتورة الأصلية:")
                        print(f"   المبلغ السابق: {original_invoice_amount:.2f}")
                        print(f"   المبلغ الجديد: {new_invoice_amount:.2f}")

                    else:
                        print(f"⚠️ تحذير: لم يتم العثور على بيانات العميل بالمعرف {customer_id}")

                else:
                    print("ℹ️ هذه فاتورة نقدية أو بدون عميل محدد - لا يوجد رصيد لتحديثه")

            except Exception as balance_error:
                print(f"⚠️ خطأ في تحديث رصيد العميل: {str(balance_error)}")
                import traceback
                traceback.print_exc()

            # حفظ التغييرات والتحقق من النجاح
            conn.commit()

            # التحقق من أن المرتجع تم حفظه فعلاً
            cursor.execute("SELECT COUNT(*) FROM advanced_returns WHERE id = ?", (return_id,))
            saved_count = cursor.fetchone()[0]

            if saved_count > 0:
                print(f"✅ تم التأكد من حفظ المرتجع في قاعدة البيانات (ID: {return_id})")
            else:
                print(f"❌ خطأ: لم يتم حفظ المرتجع في قاعدة البيانات!")

            conn.close()

            # رسالة النجاح مع تفاصيل التحديثات
            success_message = (
                f"🎉 تم حفظ مرتجع المبيعات بنجاح!\n\n"
                f"📋 رقم المرتجع: {self.return_number_input.text()}\n"
                f"📋 الفاتورة الأصلية: {self.selected_invoice[1]}\n"
                f"👤 العميل: {customer_name}\n"
                f"💰 إجمالي المبلغ المرتجع: {total_amount:.2f} جنيه\n"
                f"📦 عدد الأصناف المرتجعة: {len(return_products)}\n\n"
                f"✅ تم تحديث المخزون تلقائياً (إضافة الكميات)\n"
                f"✅ تم خصم المبلغ من رصيد العميل\n"
                f"✅ تم حفظ جميع التفاصيل في قاعدة البيانات"
            )

            QMessageBox.information(self, "نجح العملية ✅", success_message)

            # تحديث فهرس المرتجعات في النافذة الرئيسية
            if hasattr(self.parent(), 'load_returns'):
                self.parent().load_returns()
                print("✅ تم تحديث فهرس المرتجعات في النافذة الرئيسية")

            self.accept()

        except Exception as e:
            print(f"خطأ في حفظ مرتجع المبيعات: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ المرتجع:\n{str(e)}")


class AdvancedPurchaseReturnDialog(QDialog):
    """نافذة مرتجع المشتريات الأساسية"""
    
    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("مرتجع مشتريات جديد - مبسط")
        self.setFixedSize(600, 500)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("إنشاء مرتجع مشتريات جديد")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: white;
                background-color: #e74c3c;
                padding: 15px;
                border-radius: 8px;
                margin: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # النموذج
        form_group = QGroupBox("بيانات المرتجع")
        form_layout = QFormLayout()
        
        # رقم المرتجع
        self.return_number_input = QLineEdit()
        self.return_number_input.setText(self.generate_return_number())
        self.return_number_input.setReadOnly(True)
        form_layout.addRow("رقم المرتجع:", self.return_number_input)
        
        # التاريخ
        self.return_date = QDateEdit()
        self.return_date.setDate(QDate.currentDate())
        self.return_date.setCalendarPopup(True)
        form_layout.addRow("تاريخ المرتجع:", self.return_date)
        
        # رقم فاتورة الشراء
        self.invoice_number_input = QLineEdit()
        self.invoice_number_input.setPlaceholderText("رقم فاتورة الشراء...")
        form_layout.addRow("رقم الفاتورة:", self.invoice_number_input)
        
        # اسم المورد
        self.supplier_name_input = QLineEdit()
        self.supplier_name_input.setPlaceholderText("اسم المورد...")
        form_layout.addRow("اسم المورد:", self.supplier_name_input)
        
        # مبلغ المرتجع
        self.amount_input = QLineEdit()
        self.amount_input.setPlaceholderText("0.00")
        form_layout.addRow("مبلغ المرتجع:", self.amount_input)
        
        # السبب
        self.reason_input = QTextEdit()
        self.reason_input.setPlaceholderText("سبب المرتجع...")
        self.reason_input.setMaximumHeight(80)
        form_layout.addRow("السبب:", self.reason_input)
        
        form_group.setLayout(form_layout)
        layout.addWidget(form_group)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("حفظ المرتجع")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 12px 24px;
                border-radius: 6px;
                border: none;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_btn.clicked.connect(self.save_return)
        buttons_layout.addWidget(save_btn)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                padding: 12px 24px;
                border-radius: 6px;
                border: none;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
        
    def generate_return_number(self):
        """توليد رقم مرتجع جديد"""
        today = datetime.now().strftime("%Y%m%d")
        import random
        return f"PR{today}{random.randint(100, 999)}"
        
    def save_return(self):
        """حفظ المرتجع"""
        # التحقق من البيانات
        if not self.invoice_number_input.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم فاتورة الشراء")
            return
            
        if not self.supplier_name_input.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المورد")
            return
            
        try:
            amount = float(self.amount_input.text() or "0")
            if amount <= 0:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
                return
        except ValueError:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
            return
            
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # إنشاء جدول المرتجعات البسيط
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS basic_returns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    return_number TEXT UNIQUE NOT NULL,
                    return_type TEXT NOT NULL,
                    return_date TEXT NOT NULL,
                    original_invoice_number TEXT,
                    customer_name TEXT,
                    amount REAL NOT NULL DEFAULT 0,
                    reason TEXT,
                    user_id INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # إدراج المرتجع
            cursor.execute("""
                INSERT INTO basic_returns 
                (return_number, return_type, return_date, original_invoice_number,
                 customer_name, amount, reason, user_id, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
            """, (
                self.return_number_input.text(),
                "مرتجع مشتريات",
                self.return_date.date().toString('yyyy-MM-dd'),
                self.invoice_number_input.text().strip(),
                self.supplier_name_input.text().strip(),
                amount,
                self.reason_input.toPlainText().strip(),
                self.user_data.get('id', 1)
            ))
            
            conn.commit()
            conn.close()
            
            QMessageBox.information(self, "نجح", 
                f"تم حفظ مرتجع المشتريات بنجاح!\n\n"
                f"رقم المرتجع: {self.return_number_input.text()}\n"
                f"المبلغ: {amount:.2f} جنيه\n"
                f"المورد: {self.supplier_name_input.text()}")
            
            self.accept()
            
        except Exception as e:
            print(f"خطأ في حفظ مرتجع المشتريات: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ المرتجع:\n{str(e)}")
