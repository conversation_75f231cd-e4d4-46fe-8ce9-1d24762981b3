@echo off
chcp 65001 > nul
title بناء مبسط - Minimal Build

echo ════════════════════════════════════════════════════════════════
echo                    بناء مبسط للتطبيق
echo                    Minimal Build
echo ════════════════════════════════════════════════════════════════
echo.

echo [1/3] تنظيف الملفات...
echo [1/3] Cleaning files...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "POS_System.spec" del "POS_System.spec"

echo.
echo [2/3] بناء التطبيق (بسيط)...
echo [2/3] Building application (simple)...
pyinstaller --onefile --windowed --name=POS_System --icon=icon.ico main.py

echo.
echo [3/3] التحقق من النتيجة...
echo [3/3] Checking result...

if exist "dist\POS_System.exe" (
    echo.
    echo ✅ تم بناء التطبيق بنجاح!
    echo ✅ Build successful!
    echo.
    echo 📁 الملف: dist\POS_System.exe
    echo 📁 File: dist\POS_System.exe
    echo.
    dir "dist\POS_System.exe"
    echo.
    echo 🎯 نسخ الملفات المطلوبة...
    echo 🎯 Copying required files...
    
    if not exist "dist\database" mkdir "dist\database"
    if not exist "dist\ui" mkdir "dist\ui"
    if not exist "dist\utils" mkdir "dist\utils"
    if not exist "dist\temp" mkdir "dist\temp"
    if not exist "dist\config" mkdir "dist\config"
    
    xcopy "database\*" "dist\database\" /E /I /Q
    xcopy "ui\*" "dist\ui\" /E /I /Q
    xcopy "utils\*" "dist\utils\" /E /I /Q
    xcopy "config\*" "dist\config\" /E /I /Q
    
    echo.
    echo ✅ تم نسخ الملفات المطلوبة
    echo ✅ Required files copied successfully
    echo.
    echo 🚀 تشغيل التطبيق؟ [Y/N]
    set /p choice=Run application? [Y/N]: 
    if /i "%choice%"=="Y" (
        echo تشغيل التطبيق...
        cd dist
        start "POS System" "POS_System.exe"
        cd ..
    )
) else (
    echo.
    echo ❌ فشل في البناء!
    echo ❌ Build failed!
    echo.
    echo تحقق من الأخطاء أعلاه
    echo Check errors above
)

echo.
pause