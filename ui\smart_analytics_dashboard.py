#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
لوحة التحليلات الذكية - نظام نقاط البيع والمحاسبة
Smart Analytics Dashboard - POS and Accounting System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QFrame, QTabWidget, QTableWidget,
                            QTableWidgetItem, QSizePolicy, QHeaderView)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QColor, QPainter
from PyQt5.QtChart import QChart, QChartView, QBarSeries, QBarSet, QBarCategoryAxis, QValueAxis, QPieSeries
import sqlite3
from datetime import datetime, timedelta

class SmartAnalyticsDashboard(QWidget):
    """لوحة التحليلات الذكية"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.setWindowTitle("التحليلات الذكية")
        self.setGeometry(100, 100, 1000, 700)
        
        # Initialize debug logger
        import logging
        self.logger = logging.getLogger("SmartAnalytics")
        self.logger.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        
        # Create console handler
        ch = logging.StreamHandler()
        ch.setLevel(logging.DEBUG)
        ch.setFormatter(formatter)
        self.logger.addHandler(ch)
        
        self.logger.debug("SmartAnalytics dashboard initialized")
        
        try:
            # Verify database connection
            if not self.db_manager or not self.db_manager.connection:
                raise Exception("Database connection is invalid")
            self.logger.debug("Database connection verified")
            
            self.init_ui()
            
        except Exception as e:
            self.logger.error(f"Initialization error: {str(e)}")
            # Create a minimal UI to show the error
            error_layout = QVBoxLayout()
            error_label = QLabel(f"خطأ في تهيئة لوحة التحليلات: {str(e)}")
            error_label.setStyleSheet("font-size: 16px; color: red;")
            error_layout.addWidget(error_label)
            self.setLayout(error_layout)
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # إضافة البانر العلوي لعرض الرسائل
        self.message_label = QLabel("جاري تحميل البيانات...")
        self.message_label.setAlignment(Qt.AlignCenter)
        self.message_label.setStyleSheet("""
            background-color: #ffeb3b;
            color: #333;
            font-size: 16px;
            font-weight: bold;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        """)
        main_layout.addWidget(self.message_label)
        
        # عنوان اللوحة
        title_label = QLabel("التحليلات الذكية")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
        """)
        main_layout.addWidget(title_label)
        
        # تبويبات التحليلات
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane { border: 1px solid #ddd; border-radius: 5px; }
            QTabBar::tab { 
                background: #f8f9fa; 
                border: 1px solid #ddd; 
                padding: 8px 15px; 
                margin-right: 2px; 
                border-top-left-radius: 5px; 
                border-top-right-radius: 5px; 
            }
            QTabBar::tab:selected { 
                background: #667eea; 
                color: white; 
                border-bottom: 2px solid #764ba2; 
            }
        """)
        
        # إنشاء تبويبات التحليلات
        self.create_monthly_sales_tab()
        self.create_top_products_tab()
        self.create_top_customers_tab()
        self.create_profit_loss_tab()
        self.create_stagnant_products_tab()
        self.create_daily_sales_tab()
        self.create_customer_classification_tab()
        
        main_layout.addWidget(self.tabs)
        self.setLayout(main_layout)
        
        # Connect tab change signal to load data
        self.tabs.currentChanged.connect(self.on_tab_changed)
        # Load initial data for the first tab
        self.tabs.setCurrentIndex(0)
        self.on_tab_changed(0)
    def create_monthly_sales_tab(self):
        """إنشاء تبويب المبيعات الشهرية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        title = QLabel("المبيعات الشهرية")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 15px;")
        layout.addWidget(title)
        
        # Create chart view for monthly sales
        self.monthly_sales_chart_view = QChartView()
        self.monthly_sales_chart_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(self.monthly_sales_chart_view)
        
        self.tabs.addTab(tab, "المبيعات الشهرية")
        
    def create_top_products_tab(self):
        """إنشاء تبويب أكثر الأصناف مبيعًا"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        title = QLabel("أكثر الأصناف مبيعًا")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 15px;")
        layout.addWidget(title)
        
        # Create chart view for top products
        self.top_products_chart_view = QChartView()
        self.top_products_chart_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(self.top_products_chart_view)
        
        self.tabs.addTab(tab, "أكثر الأصناف مبيعًا")
        
    def create_top_customers_tab(self):
        """إنشاء تبويب أكثر العملاء شراءً"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        title = QLabel("أكثر العملاء شراءً")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 15px;")
        layout.addWidget(title)
        
        # Create chart view for top customers
        self.top_customers_chart_view = QChartView()
        self.top_customers_chart_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(self.top_customers_chart_view)
        
        self.tabs.addTab(tab, "أكثر العملاء شراءً")
        
    def create_profit_loss_tab(self):
        """إنشاء تبويب الأرباح والخسائر"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        title = QLabel("تحليل الأرباح والخسائر")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 15px;")
        layout.addWidget(title)
        
        # Create chart view for profit/loss
        self.profit_loss_chart_view = QChartView()
        self.profit_loss_chart_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(self.profit_loss_chart_view)
        
        self.tabs.addTab(tab, "الأرباح والخسائر")
        
    def create_stagnant_products_tab(self):
        """إنشاء تبويب المنتجات الراكدة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        title = QLabel("المنتجات الراكدة")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 15px;")
        layout.addWidget(title)
        
        # Create table for stagnant products
        self.stagnant_products_table = QTableWidget()
        self.stagnant_products_table.setColumnCount(3)
        self.stagnant_products_table.setHorizontalHeaderLabels(["اسم المنتج", "الكمية", "أيام بدون حركة"])
        self.stagnant_products_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.stagnant_products_table)
        
        self.tabs.addTab(tab, "المنتجات الراكدة")
        
    def create_daily_sales_tab(self):
        """إنشاء تبويب متوسط المبيعات اليومية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        title = QLabel("متوسط المبيعات اليومية")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 15px;")
        layout.addWidget(title)
        
        # Create chart view for daily sales
        self.daily_sales_chart_view = QChartView()
        self.daily_sales_chart_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(self.daily_sales_chart_view)
        
        self.tabs.addTab(tab, "المبيعات اليومية")
        
    def create_customer_classification_tab(self):
        """إنشاء تبويب تصنيف العملاء حسب النشاط"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        title = QLabel("تصنيف العملاء حسب النشاط")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 15px;")
        layout.addWidget(title)
        
        # Create chart view for customer classification
        self.customer_classification_chart_view = QChartView()
        self.customer_classification_chart_view.setRenderHint(QPainter.Antialiasing)
        layout.addWidget(self.customer_classification_chart_view)
        
        self.tabs.addTab(tab, "تصنيف العملاء")

    def load_monthly_sales(self):
        """تحميل بيانات المبيعات الشهرية"""
        try:
            # Fetch monthly sales data
            query = """
            SELECT
                strftime('%Y-%m', sale_date) AS month,
                COALESCE(SUM(total_amount), 0) AS total
            FROM sales
            WHERE sale_date IS NOT NULL
            GROUP BY month
            ORDER BY month
            """
            results = self.db_manager.execute_query(query)
            self.logger.debug(f"Monthly sales query: {query}")
            self.logger.debug(f"Monthly sales results: {results}")
            
            if not results:
                self.message_label.setText("لا توجد بيانات للمبيعات الشهرية")
                self.message_label.setStyleSheet("background-color: #ff6b6b; color: white;")
                return
                
            # Create bar series
            months = []
            sales_set = QBarSet("المبيعات")
            for month, total in results:
                months.append(month)
                sales_set.append(total)
                
            # Validate data
            if not months or not sales_set:
                self.logger.warning("Monthly sales data is empty")
                return
            
            series = QBarSeries()
            series.append(sales_set)
            
            # Create chart
            chart = QChart()
            chart.addSeries(series)
            chart.setTitle("المبيعات الشهرية")
            
            # Create axes
            axis_x = QBarCategoryAxis()
            axis_x.append(months)
            chart.addAxis(axis_x, Qt.AlignBottom)
            series.attachAxis(axis_x)
            
            axis_y = QValueAxis()
            axis_y.setLabelFormat("%d")
            chart.addAxis(axis_y, Qt.AlignLeft)
            series.attachAxis(axis_y)
            
            # Set chart to view
            self.monthly_sales_chart_view.setChart(chart)
            
        except Exception as e:
            error_msg = f"خطأ في تحميل المبيعات الشهرية: {str(e)}"
            self.message_label.setText(error_msg)
            self.message_label.setStyleSheet("background-color: #ff6b6b; color: white;")
            print(error_msg)
        
    def load_top_products(self):
        """تحميل بيانات أكثر المنتجات مبيعًا"""
        try:
            # Fetch top products data
            query = """
            SELECT
                p.name AS product_name,
                COALESCE(SUM(si.quantity), 0) AS total_sold
            FROM products p
            LEFT JOIN sale_items si ON p.id = si.product_id
            GROUP BY p.name
            ORDER BY total_sold DESC
            LIMIT 10
            """
            results = self.db_manager.execute_query(query)
            self.logger.debug(f"Top products query: {query}")
            self.logger.debug(f"Top products results: {results}")
            
            if not results:
                self.message_label.setText("لا توجد بيانات للأصناف الأكثر مبيعاً")
                self.message_label.setStyleSheet("background-color: #ff6b6b; color: white;")
                return
                
            # Create bar series
            products = []
            sales_set = QBarSet("الكمية المباعة")
            for product, quantity in results:
                products.append(product)
                sales_set.append(quantity)
                
            # Validate data
            if not products or not sales_set:
                self.logger.warning("Top products data is empty")
                return
            
            series = QBarSeries()
            series.append(sales_set)
            
            # Create chart
            chart = QChart()
            chart.addSeries(series)
            chart.setTitle("أكثر المنتجات مبيعًا")
            
            # Create axes
            axis_x = QBarCategoryAxis()
            axis_x.append(products)
            chart.addAxis(axis_x, Qt.AlignBottom)
            series.attachAxis(axis_x)
            
            axis_y = QValueAxis()
            axis_y.setLabelFormat("%d")
            chart.addAxis(axis_y, Qt.AlignLeft)
            series.attachAxis(axis_y)
            
            # Set chart to view
            self.top_products_chart_view.setChart(chart)
            
        except Exception as e:
            error_msg = f"خطأ في تحميل الأصناف الأكثر مبيعاً: {str(e)}"
            self.message_label.setText(error_msg)
            self.message_label.setStyleSheet("background-color: #ff6b6b; color: white;")
            print(error_msg)
        
    def load_top_customers(self):
        """تحميل بيانات أكثر العملاء شراءً"""
        try:
            # Fetch top customers data directly from sales table
            query = """
            SELECT
                customer_name,
                COALESCE(SUM(total_amount), 0) AS total_spent
            FROM sales
            WHERE customer_name IS NOT NULL
            GROUP BY customer_name
            ORDER BY total_spent DESC
            LIMIT 10
            """
            results = self.db_manager.execute_query(query)
            self.logger.debug(f"Top customers query: {query}")
            self.logger.debug(f"Top customers results: {results}")
            
            if not results:
                self.message_label.setText("لا توجد بيانات لأفضل العملاء")
                self.message_label.setStyleSheet("background-color: #ff6b6b; color: white;")
                return
                
            # Create bar series
            customers = []
            spending_set = QBarSet("إجمالي المشتريات")
            for customer, total in results:
                customers.append(customer)
                spending_set.append(total)
                
            # Validate data
            if not customers or not spending_set:
                self.logger.warning("Top customers data is empty")
                return
            
            series = QBarSeries()
            series.append(spending_set)
            
            # Create chart
            chart = QChart()
            chart.addSeries(series)
            chart.setTitle("أفضل العملاء")
            
            # Create axes
            axis_x = QBarCategoryAxis()
            axis_x.append(customers)
            chart.addAxis(axis_x, Qt.AlignBottom)
            series.attachAxis(axis_x)
            
            axis_y = QValueAxis()
            axis_y.setLabelFormat("%.2f")
            chart.addAxis(axis_y, Qt.AlignLeft)
            series.attachAxis(axis_y)
            
            # Set chart to view
            self.top_customers_chart_view.setChart(chart)
            
        except Exception as e:
            error_msg = f"خطأ في تحميل أفضل العملاء: {str(e)}"
            self.message_label.setText(error_msg)
            self.message_label.setStyleSheet("background-color: #ff6b6b; color: white;")
            print(error_msg)
        
    def load_profit_loss(self):
        """تحميل بيانات الأرباح والخسائر"""
        try:
            # First try the detailed query with cost columns
            try:
                query = """
                SELECT
                    strftime('%Y-%m', s.sale_date) AS month,
                    COALESCE(SUM(s.total_amount), 0) AS revenue,
                    COALESCE(SUM(
                        CASE
                            WHEN p.purchase_price IS NOT NULL THEN p.purchase_price * si.quantity
                            ELSE 0
                        END
                    ), 0) AS cost,
                    COALESCE(SUM(s.total_amount), 0) - COALESCE(SUM(
                        CASE
                            WHEN p.purchase_price IS NOT NULL THEN p.purchase_price * si.quantity
                            ELSE 0
                        END
                    ), 0) AS profit
                FROM sales s
                JOIN sale_items si ON s.id = si.sale_id
                JOIN products p ON si.product_id = p.id
                WHERE s.sale_date IS NOT NULL
                GROUP BY month
                ORDER BY month
                """
                results = self.db_manager.execute_query(query)
                self.logger.debug(f"Detailed profit/loss query succeeded")
                
            except sqlite3.OperationalError as e:
                # If detailed query fails, use revenue-only fallback
                self.logger.warning(f"Detailed profit/loss query failed: {str(e)}. Using revenue-only fallback.")
                
                query = """
                SELECT
                    strftime('%Y-%m', sale_date) AS month,
                    COALESCE(SUM(total_amount), 0) AS revenue,
                    0 AS cost,
                    COALESCE(SUM(total_amount), 0) AS profit
                FROM sales
                WHERE sale_date IS NOT NULL
                GROUP BY month
                ORDER BY month
                """
                results = self.db_manager.execute_query(query)
                
            self.logger.debug(f"Profit/loss results: {results}")
            
            if not results:
                self.message_label.setText("لا توجد بيانات للأرباح والخسائر")
                self.message_label.setStyleSheet("background-color: #ff6b6b; color: white;")
                return
                
            # Create series
            months = []
            revenue_set = QBarSet("الإيرادات")
            cost_set = QBarSet("التكاليف")
            profit_set = QBarSet("الأرباح")
            
            for month, revenue, cost, profit in results:
                months.append(month)
                revenue_set.append(revenue)
                cost_set.append(cost)
                profit_set.append(profit)
            
            series = QBarSeries()
            series.append(revenue_set)
            series.append(cost_set)
            series.append(profit_set)
            
            # Create chart
            chart = QChart()
            chart.addSeries(series)
            chart.setTitle("الأرباح والخسائر")
            
            # Create axes
            axis_x = QBarCategoryAxis()
            axis_x.append(months)
            chart.addAxis(axis_x, Qt.AlignBottom)
            series.attachAxis(axis_x)
            
            axis_y = QValueAxis()
            axis_y.setLabelFormat("%.2f")
            chart.addAxis(axis_y, Qt.AlignLeft)
            series.attachAxis(axis_y)
            
            # Set chart to view
            self.profit_loss_chart_view.setChart(chart)
            
        except Exception as e:
            error_msg = f"خطأ في تحميل الأرباح والخسائر: {str(e)}"
            self.message_label.setText(error_msg)
            self.message_label.setStyleSheet("background-color: #ff6b6b; color: white;")
            print(error_msg)
        
    def load_stagnant_products(self):
        """تحميل بيانات المنتجات الراكدة"""
        try:
            # Fetch stagnant products data
            query = """
            SELECT
                p.name,
                p.quantity_in_stock,
                CASE
                    WHEN MAX(s.sale_date) IS NULL THEN 'غير معروف'
                    ELSE CAST(JULIANDAY('now') - JULIANDAY(MAX(s.sale_date)) AS TEXT)
                END AS days_inactive
            FROM products p
            LEFT JOIN sale_items si ON p.id = si.product_id
            LEFT JOIN sales s ON si.sale_id = s.id
            GROUP BY p.id, p.name, p.quantity_in_stock
            HAVING days_inactive = 'غير معروف' OR CAST(days_inactive AS INTEGER) > 30
            ORDER BY CASE WHEN days_inactive = 'غير معروف' THEN 1 ELSE 0 END, CAST(days_inactive AS INTEGER) DESC
            """
            results = self.db_manager.execute_query(query)
            self.logger.debug(f"Stagnant products query: {query}")
            self.logger.debug(f"Stagnant products results: {results}")
            
            if not results:
                self.message_label.setText("لا توجد منتجات راكدة")
                self.message_label.setStyleSheet("background-color: #ff6b6b; color: white;")
                return
                
            # Populate table
            self.stagnant_products_table.setRowCount(len(results))
            for row_idx, (name, quantity, days_inactive) in enumerate(results):
                self.stagnant_products_table.setItem(row_idx, 0, QTableWidgetItem(name))
                self.stagnant_products_table.setItem(row_idx, 1, QTableWidgetItem(str(quantity)))
                self.stagnant_products_table.setItem(row_idx, 2, QTableWidgetItem(str(int(days_inactive) if days_inactive else "غير معروف")))
            
        except Exception as e:
            error_msg = f"خطأ في تحميل المنتجات الراكدة: {str(e)}"
            self.message_label.setText(error_msg)
            self.message_label.setStyleSheet("background-color: #ff6b6b; color: white;")
            print(error_msg)
        
    def load_daily_sales(self):
        """تحميل بيانات متوسط المبيعات اليومية"""
        try:
            # Fetch daily sales data
            query = """
            SELECT
                strftime('%w', sale_date) AS day_of_week,
                COALESCE(AVG(total_amount), 0) AS avg_sales
            FROM sales
            WHERE sale_date IS NOT NULL
            GROUP BY day_of_week
            ORDER BY day_of_week
            """
            results = self.db_manager.execute_query(query)
            
            if not results:
                self.message_label.setText("لا توجد بيانات للمبيعات اليومية")
                self.message_label.setStyleSheet("background-color: #ff6b6b; color: white;")
                return
                
            # Map day numbers to names
            day_names = ["الأحد", "الإثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"]
            
            # Create bar series
            days = []
            sales_set = QBarSet("متوسط المبيعات")
            for day_num, avg_sales in results:
                days.append(day_names[int(day_num)])
                sales_set.append(avg_sales)
            
            series = QBarSeries()
            series.append(sales_set)
            
            # Create chart
            chart = QChart()
            chart.addSeries(series)
            chart.setTitle("متوسط المبيعات اليومية")
            
            # Create axes
            axis_x = QBarCategoryAxis()
            axis_x.append(days)
            chart.addAxis(axis_x, Qt.AlignBottom)
            series.attachAxis(axis_x)
            
            axis_y = QValueAxis()
            axis_y.setLabelFormat("%.2f")
            chart.addAxis(axis_y, Qt.AlignLeft)
            series.attachAxis(axis_y)
            
            # Set chart to view
            self.daily_sales_chart_view.setChart(chart)
            
        except Exception as e:
            error_msg = f"خطأ في تحميل المبيعات اليومية: {str(e)}"
            self.message_label.setText(error_msg)
            self.message_label.setStyleSheet("background-color: #ff6b6b; color: white;")
            print(error_msg)
        
    def load_customer_classification(self):
        """تحميل بيانات تصنيف العملاء"""
        try:
            # Fetch customer classification data directly from sales table
            query = """
            SELECT
                classification,
                COUNT(*) AS count
            FROM (
                SELECT
                    CASE
                        WHEN COALESCE(SUM(total_amount), 0) > 10000 THEN 'VIP'
                        WHEN COALESCE(SUM(total_amount), 0) > 5000 THEN 'ممتاز'
                        WHEN COALESCE(SUM(total_amount), 0) > 1000 THEN 'جيد'
                        ELSE 'عادي'
                    END AS classification
                FROM sales
                WHERE customer_name IS NOT NULL
                GROUP BY customer_name
            )
            GROUP BY classification
            """
            results = self.db_manager.execute_query(query)
            self.logger.debug(f"Customer classification query: {query}")
            self.logger.debug(f"Customer classification results: {results}")
            
            if not results:
                self.message_label.setText("لا توجد بيانات لتصنيف العملاء")
                self.message_label.setStyleSheet("background-color: #ff6b6b; color: white;")
                return
                
            # Create pie series
            series = QPieSeries()
            for classification, count in results:
                series.append(classification, count)
            
            # Create chart
            chart = QChart()
            chart.addSeries(series)
            chart.setTitle("تصنيف العملاء")
            
            # Set chart to view
            self.customer_classification_chart_view.setChart(chart)
            
        except Exception as e:
            error_msg = f"خطأ في تحميل تصنيف العملاء: {str(e)}"
            self.message_label.setText(error_msg)
            self.message_label.setStyleSheet("background-color: #ff6b6b; color: white;")
            print(error_msg)
            
    def on_tab_changed(self, index):
        """Load data when tab is changed"""
        try:
            tab_name = self.tabs.tabText(index)
            
            if tab_name == "المبيعات الشهرية":
                self.load_monthly_sales()
            elif tab_name == "أكثر الأصناف مبيعًا":
                self.load_top_products()
            elif tab_name == "أكثر العملاء شراءً":
                self.load_top_customers()
            elif tab_name == "الأرباح والخسائر":
                self.load_profit_loss()
            elif tab_name == "المنتجات الراكدة":
                self.load_stagnant_products()
            elif tab_name == "المبيعات اليومية":
                self.load_daily_sales()
            elif tab_name == "تصنيف العملاء":
                self.load_customer_classification()
                
        except Exception as e:
            error_msg = f"خطأ في تحميل تبويب {tab_name}: {str(e)}"
            self.logger.error(error_msg)
            self.message_label.setText(error_msg)
            self.message_label.setStyleSheet("background-color: #ff6b6b; color: white;")