# دليل البدء السريع 🚀
## Quick Start Guide

### الطريقة الأولى - تحويل لملف EXE:

#### 1. البناء المبسط (الأسرع):
```bash
build_minimal.bat
```

#### 2. البناء الكامل (الأفضل):
```bash
build_simple.bat
```

### الطريقة الثانية - تشغيل مباشر:

#### تشغيل التطبيق بـ Python:
```bash
python main.py
```

### 📁 هيكل المجلد بعد البناء:

```
dist/
├── POS_System.exe          # الملف التنفيذي
├── database/               # قاعدة البيانات
├── ui/                     # واجهة المستخدم
├── utils/                  # الأدوات
├── config/                 # الإعدادات
└── temp/                   # ملفات مؤقتة
```

### 🔧 إذا واجهت مشاكل:

#### مشكلة 1: PyInstaller غير مثبت
```bash
pip install pyinstaller
```

#### مشكلة 2: مكتبات البار كود غير موجودة
```bash
pip install python-barcode[images] qrcode[pil] Pillow
```

#### مشكلة 3: PyQt5 غير مثبت
```bash
pip install PyQt5
```

### 🎯 معلومات مهمة:

- **حجم الملف**: ~200-300 ميجابايت
- **وقت البناء**: 5-10 دقائق
- **نظام التشغيل**: Windows 10/11
- **متطلبات**: Python 3.8+

### 📞 الدعم:
- **المطور**: شادي الطيب
- **الهاتف**: 01008379651 / 01284860988

### 🎉 ملاحظات:
- التطبيق يعمل بدون إنترنت
- قاعدة البيانات محلية
- جميع الملفات محفوظة محلياً
- الأسهم المضافة للقوائم تعمل بشكل مثالي
- أداة البار كود مدمجة وجاهزة للاستخدام

### 🔧 الإصلاحات الأخيرة:
- ✅ إزالة رسالة عدد الأصناف عند فتح فاتورة الشراء
- ✅ تحسين إضافة المنتجات الجديدة مع معلومات كاملة
- ✅ عرض تفاصيل المنتج عند الاختيار (سعر، كمية، وصف)
- ✅ نافذة شاملة لإضافة منتج جديد بجميع التفاصيل

---

## مبروك! 🎊
بعد البناء، ستحصل على نظام نقاط بيع كامل يعمل على أي جهاز Windows!