"""
واجهة إدارة النسخ الاحتياطي - Backup Management Window
إدارة النسخ الاحتياطي التلقائي والاستعادة
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QTabWidget, QLabel, QPushButton, QTableWidget, 
                             QTableWidgetItem, QGroupBox, QCheckBox, QSpinBox,
                             QComboBox, QProgressBar, QTextEdit, QMessageBox,
                             QFileDialog, QHeaderView, QFrame)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor, QIcon
# استيراد آمن لمدير النسخ الاحتياطي
try:
    from utils.backup_manager import BackupManager
    BACKUP_AVAILABLE = True
except ImportError as e:
    print(f"تحذير: لم يتم العثور على مدير النسخ الاحتياطي: {e}")
    BACKUP_AVAILABLE = False

    # إنشاء فئة بديلة بسيطة
    class BackupManager:
        def __init__(self, *args, **kwargs):
            self.backup_dir = "backups"
            self.config = {
                "auto_backup_enabled": True,
                "backup_interval_hours": 6,
                "backup_on_startup": False,
                "backup_on_shutdown": False,
                "max_backups": 30,
                "compress_backups": True,
                "backup_database": True,
                "backup_reports": True,
                "backup_config": True
            }

        def create_backup(self, backup_type="manual"):
            import shutil
            import os
            from datetime import datetime

            try:
                # إنشاء مجلد النسخ الاحتياطي
                os.makedirs(self.backup_dir, exist_ok=True)

                # نسخ قاعدة البيانات
                db_path = "database/business_system.db"
                if os.path.exists(db_path):
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    backup_name = f"backup_{backup_type}_{timestamp}.db"
                    backup_path = os.path.join(self.backup_dir, backup_name)
                    shutil.copy2(db_path, backup_path)
                    return backup_path
                else:
                    return None
            except Exception as e:
                print(f"خطأ في النسخ الاحتياطي: {e}")
                return None

        def get_backup_list(self):
            import os
            import glob
            from datetime import datetime

            try:
                backups = []
                backup_files = glob.glob(os.path.join(self.backup_dir, "backup_*.db"))

                for file_path in backup_files:
                    try:
                        filename = os.path.basename(file_path)
                        size = os.path.getsize(file_path)
                        mtime = os.path.getmtime(file_path)
                        timestamp = datetime.fromtimestamp(mtime).strftime("%Y%m%d_%H%M%S")

                        backups.append({
                            "timestamp": timestamp,
                            "type": "manual",
                            "size": size,
                            "status": "completed",
                            "path": file_path
                        })
                    except Exception as e:
                        print(f"خطأ في قراءة ملف النسخة الاحتياطية: {e}")

                return sorted(backups, key=lambda x: x["timestamp"], reverse=True)
            except Exception as e:
                print(f"خطأ في الحصول على قائمة النسخ: {e}")
                return []

        def save_config(self):
            pass

        def cleanup_old_backups(self):
            pass
from datetime import datetime
import os
import json


# تم حذف BackupThread لتجنب مشاكل الخيوط


class BackupWindow(QMainWindow):
    """نافذة إدارة النسخ الاحتياطي"""
    
    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data

        print("بدء تهيئة نافذة النسخ الاحتياطي...")

        # تهيئة آمنة لمدير النسخ الاحتياطي
        self.backup_manager = None
        try:
            self.backup_manager = BackupManager()
            print("✅ تم تهيئة مدير النسخ الاحتياطي بنجاح")
        except Exception as e:
            print(f"❌ خطأ في تهيئة مدير النسخ الاحتياطي: {e}")
            # سنستمر بدون مدير النسخ الاحتياطي

        # تهيئة الواجهة
        try:
            self.init_ui()
            print("✅ تم تهيئة الواجهة بنجاح")
        except Exception as e:
            print(f"❌ خطأ في تهيئة الواجهة: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تهيئة واجهة النسخ الاحتياطي:\n{str(e)}")
            return

        # تحميل البيانات
        self.safe_load_data()

        print("✅ تم تهيئة نافذة النسخ الاحتياطي بنجاح")

    def safe_load_data(self):
        """تحميل البيانات بشكل آمن"""
        try:
            if self.backup_manager:
                self.load_backup_list()
                self.load_settings()
            else:
                self.statusBar().showMessage("مدير النسخ الاحتياطي غير متاح")
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            self.statusBar().showMessage(f"خطأ في تحميل البيانات: {e}")

    def safe_load_backup_list(self):
        """تحميل قائمة النسخ بشكل آمن"""
        try:
            if self.backup_manager:
                self.load_backup_list()
        except Exception as e:
            print(f"خطأ في التحديث التلقائي: {e}")
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة النسخ الاحتياطي - Backup Management")
        self.setGeometry(100, 100, 1000, 700)
        
        # تطبيق التصميم
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTabWidget::pane {
                border: 2px solid #3498db;
                background-color: white;
                border-radius: 5px;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
                font-size: 14px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin: 10px;
                padding: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #2c3e50;
                font-size: 16px;
            }
        """)
        
        # إنشاء التبويبات
        self.tabs = QTabWidget()
        
        # تبويب النسخ الاحتياطي
        self.backup_tab = self.create_backup_tab()
        self.tabs.addTab(self.backup_tab, "💾 النسخ الاحتياطي")
        
        # تبويب الإعدادات
        self.settings_tab = self.create_settings_tab()
        self.tabs.addTab(self.settings_tab, "⚙️ الإعدادات")
        
        # تبويب السجلات
        self.logs_tab = self.create_logs_tab()
        self.tabs.addTab(self.logs_tab, "📋 السجلات")
        
        self.setCentralWidget(self.tabs)
        
        # شريط الحالة
        self.statusBar().showMessage("جاهز لإدارة النسخ الاحتياطي")
    
    def create_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطي"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # أزرار التحكم
        controls_group = QGroupBox("التحكم في النسخ الاحتياطي")
        controls_layout = QHBoxLayout()
        
        # زر إنشاء نسخة احتياطية
        create_backup_btn = QPushButton("🔄 إنشاء نسخة احتياطية الآن")
        create_backup_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 15px 25px;
                border-radius: 8px;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        create_backup_btn.clicked.connect(self.create_manual_backup)
        controls_layout.addWidget(create_backup_btn)
        
        # زر استعادة نسخة احتياطية
        restore_backup_btn = QPushButton("📥 استعادة نسخة احتياطية")
        restore_backup_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                font-weight: bold;
                padding: 15px 25px;
                border-radius: 8px;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        restore_backup_btn.clicked.connect(self.show_restore_info)
        controls_layout.addWidget(restore_backup_btn)
        
        # زر تنظيف النسخ القديمة
        cleanup_btn = QPushButton("🗑️ تنظيف النسخ القديمة")
        cleanup_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                padding: 15px 25px;
                border-radius: 8px;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cleanup_btn.clicked.connect(self.cleanup_old_backups)
        controls_layout.addWidget(cleanup_btn)
        
        controls_layout.addStretch()
        controls_group.setLayout(controls_layout)
        layout.addWidget(controls_group)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # رسالة الحالة
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 10px;")
        layout.addWidget(self.status_label)
        
        # قائمة النسخ الاحتياطية
        backups_group = QGroupBox("النسخ الاحتياطية المتاحة")
        backups_layout = QVBoxLayout()
        
        # أزرار التحديث والتصدير
        list_controls_layout = QHBoxLayout()
        
        refresh_btn = QPushButton("🔄 تحديث القائمة")
        refresh_btn.clicked.connect(self.load_backup_list)
        list_controls_layout.addWidget(refresh_btn)
        
        export_btn = QPushButton("📤 تصدير نسخة احتياطية")
        export_btn.clicked.connect(self.export_backup)
        list_controls_layout.addWidget(export_btn)
        
        list_controls_layout.addStretch()
        backups_layout.addLayout(list_controls_layout)
        
        # جدول النسخ الاحتياطية
        self.backups_table = QTableWidget()
        self.backups_table.setColumnCount(6)
        self.backups_table.setHorizontalHeaderLabels([
            "التاريخ والوقت", "النوع", "الحجم", "الحالة", "المسار", "الإجراءات"
        ])
        self.backups_table.horizontalHeader().setStretchLastSection(True)
        self.backups_table.setAlternatingRowColors(True)
        self.backups_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        backups_layout.addWidget(self.backups_table)
        backups_group.setLayout(backups_layout)
        layout.addWidget(backups_group)
        
        widget.setLayout(layout)
        return widget
    
    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات النسخ التلقائي
        auto_backup_group = QGroupBox("إعدادات النسخ التلقائي")
        auto_layout = QVBoxLayout()
        
        # تفعيل النسخ التلقائي
        self.auto_backup_enabled = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        auto_layout.addWidget(self.auto_backup_enabled)
        
        # فترة النسخ
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("فترة النسخ التلقائي:"))
        self.backup_interval = QSpinBox()
        self.backup_interval.setRange(1, 24)
        self.backup_interval.setSuffix(" ساعة")
        interval_layout.addWidget(self.backup_interval)
        interval_layout.addStretch()
        auto_layout.addLayout(interval_layout)
        
        # نسخ عند بدء التشغيل
        self.backup_on_startup = QCheckBox("إنشاء نسخة احتياطية عند بدء التشغيل")
        auto_layout.addWidget(self.backup_on_startup)
        
        # نسخ عند الإغلاق
        self.backup_on_shutdown = QCheckBox("إنشاء نسخة احتياطية عند إغلاق البرنامج")
        auto_layout.addWidget(self.backup_on_shutdown)
        
        auto_backup_group.setLayout(auto_layout)
        layout.addWidget(auto_backup_group)
        
        # إعدادات النسخ
        backup_settings_group = QGroupBox("إعدادات النسخ")
        backup_settings_layout = QVBoxLayout()
        
        # الحد الأقصى للنسخ
        max_backups_layout = QHBoxLayout()
        max_backups_layout.addWidget(QLabel("الحد الأقصى للنسخ المحفوظة:"))
        self.max_backups = QSpinBox()
        self.max_backups.setRange(5, 100)
        self.max_backups.setSuffix(" نسخة")
        max_backups_layout.addWidget(self.max_backups)
        max_backups_layout.addStretch()
        backup_settings_layout.addLayout(max_backups_layout)
        
        # ضغط النسخ
        self.compress_backups = QCheckBox("ضغط النسخ الاحتياطية (توفير مساحة)")
        backup_settings_layout.addWidget(self.compress_backups)
        
        # نسخ قاعدة البيانات
        self.backup_database = QCheckBox("نسخ قاعدة البيانات")
        backup_settings_layout.addWidget(self.backup_database)
        
        # نسخ الإعدادات
        self.backup_config = QCheckBox("نسخ ملفات الإعدادات")
        backup_settings_layout.addWidget(self.backup_config)
        
        # نسخ التقارير
        self.backup_reports = QCheckBox("نسخ التقارير والسجلات")
        backup_settings_layout.addWidget(self.backup_reports)
        
        backup_settings_group.setLayout(backup_settings_layout)
        layout.addWidget(backup_settings_group)
        
        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()
        
        save_settings_btn = QPushButton("💾 حفظ الإعدادات")
        save_settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        save_settings_btn.clicked.connect(self.save_settings)
        buttons_layout.addWidget(save_settings_btn)
        
        reset_settings_btn = QPushButton("🔄 إعادة تعيين")
        reset_settings_btn.clicked.connect(self.load_settings)
        buttons_layout.addWidget(reset_settings_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
    
    def create_logs_tab(self):
        """إنشاء تبويب السجلات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # أزرار التحكم في السجلات
        logs_controls_layout = QHBoxLayout()
        
        refresh_logs_btn = QPushButton("🔄 تحديث السجلات")
        refresh_logs_btn.clicked.connect(self.load_logs)
        logs_controls_layout.addWidget(refresh_logs_btn)
        
        clear_logs_btn = QPushButton("🗑️ مسح السجلات")
        clear_logs_btn.clicked.connect(self.clear_logs)
        logs_controls_layout.addWidget(clear_logs_btn)
        
        logs_controls_layout.addStretch()
        layout.addLayout(logs_controls_layout)
        
        # منطقة عرض السجلات
        self.logs_text = QTextEdit()
        self.logs_text.setReadOnly(True)
        self.logs_text.setFont(QFont("Courier", 10))
        layout.addWidget(self.logs_text)
        
        widget.setLayout(layout)
        return widget

    def load_backup_list(self):
        """تحميل قائمة النسخ الاحتياطية"""
        try:
            backups = self.backup_manager.get_backup_list()
            self.backups_table.setRowCount(len(backups))

            for row, backup in enumerate(backups):
                # التاريخ والوقت
                timestamp = backup.get("timestamp", "")
                if timestamp:
                    try:
                        dt = datetime.strptime(timestamp, "%Y%m%d_%H%M%S")
                        formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
                    except:
                        formatted_time = timestamp
                else:
                    formatted_time = "غير محدد"

                self.backups_table.setItem(row, 0, QTableWidgetItem(formatted_time))

                # النوع
                backup_type = backup.get("type", "غير محدد")
                type_text = {
                    "manual": "يدوي",
                    "auto": "تلقائي",
                    "startup": "بدء التشغيل",
                    "shutdown": "إغلاق البرنامج"
                }.get(backup_type, backup_type)

                self.backups_table.setItem(row, 1, QTableWidgetItem(type_text))

                # الحجم
                size = backup.get("size", 0)
                size_text = self.format_file_size(size)
                self.backups_table.setItem(row, 2, QTableWidgetItem(size_text))

                # الحالة
                status = backup.get("status", "غير محدد")
                status_text = {
                    "completed": "مكتمل",
                    "in_progress": "قيد التنفيذ",
                    "failed": "فشل"
                }.get(status, status)

                status_item = QTableWidgetItem(status_text)
                if status == "completed":
                    status_item.setBackground(QColor(46, 204, 113))
                    status_item.setForeground(QColor(255, 255, 255))
                elif status == "failed":
                    status_item.setBackground(QColor(231, 76, 60))
                    status_item.setForeground(QColor(255, 255, 255))

                self.backups_table.setItem(row, 3, status_item)

                # المسار
                backup_path = backup.get("path", "")
                path_item = QTableWidgetItem(os.path.basename(backup_path) if backup_path else "غير محدد")
                path_item.setToolTip(backup_path)
                self.backups_table.setItem(row, 4, path_item)

                # الإجراءات
                actions_btn = QPushButton("📋 تفاصيل")
                actions_btn.clicked.connect(lambda checked, b=backup: self.show_backup_details(b))
                self.backups_table.setCellWidget(row, 5, actions_btn)

            self.statusBar().showMessage(f"تم تحميل {len(backups)} نسخة احتياطية")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل قائمة النسخ الاحتياطية:\n{str(e)}")

    def format_file_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"

    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            config = self.backup_manager.config

            self.auto_backup_enabled.setChecked(config.get("auto_backup_enabled", True))
            self.backup_interval.setValue(config.get("backup_interval_hours", 6))
            self.backup_on_startup.setChecked(config.get("backup_on_startup", True))
            self.backup_on_shutdown.setChecked(config.get("backup_on_shutdown", True))
            self.max_backups.setValue(config.get("max_backups", 30))
            self.compress_backups.setChecked(config.get("compress_backups", True))
            self.backup_database.setChecked(config.get("backup_database", True))
            self.backup_config.setChecked(config.get("backup_config", True))
            self.backup_reports.setChecked(config.get("backup_reports", True))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل الإعدادات:\n{str(e)}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            self.backup_manager.config.update({
                "auto_backup_enabled": self.auto_backup_enabled.isChecked(),
                "backup_interval_hours": self.backup_interval.value(),
                "backup_on_startup": self.backup_on_startup.isChecked(),
                "backup_on_shutdown": self.backup_on_shutdown.isChecked(),
                "max_backups": self.max_backups.value(),
                "compress_backups": self.compress_backups.isChecked(),
                "backup_database": self.backup_database.isChecked(),
                "backup_config": self.backup_config.isChecked(),
                "backup_reports": self.backup_reports.isChecked()
            })

            self.backup_manager.save_config()
            QMessageBox.information(self, "نجح", "تم حفظ الإعدادات بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ الإعدادات:\n{str(e)}")

    def load_logs(self):
        """تحميل السجلات"""
        try:
            log_file = os.path.join(self.backup_manager.backup_dir, 'backup.log')
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    logs = f.read()
                self.logs_text.setText(logs)

                # التمرير إلى النهاية
                cursor = self.logs_text.textCursor()
                cursor.movePosition(cursor.End)
                self.logs_text.setTextCursor(cursor)
            else:
                self.logs_text.setText("لا توجد سجلات متاحة")

        except Exception as e:
            self.logs_text.setText(f"خطأ في تحميل السجلات: {str(e)}")

    def clear_logs(self):
        """مسح السجلات"""
        reply = QMessageBox.question(
            self,
            "تأكيد المسح",
            "هل تريد مسح جميع السجلات؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                log_file = os.path.join(self.backup_manager.backup_dir, 'backup.log')
                if os.path.exists(log_file):
                    open(log_file, 'w').close()  # مسح محتوى الملف

                self.logs_text.clear()
                QMessageBox.information(self, "نجح", "تم مسح السجلات بنجاح")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ في مسح السجلات:\n{str(e)}")

    def create_manual_backup(self):
        """إنشاء نسخة احتياطية يدوية (مبسط وآمن)"""
        print("🔄 بدء إنشاء نسخة احتياطية يدوية...")

        try:
            # التحقق من وجود مدير النسخ الاحتياطي
            if not self.backup_manager:
                QMessageBox.warning(self, "تحذير", "مدير النسخ الاحتياطي غير متاح.\nسيتم إنشاء نسخة احتياطية بسيطة.")
                self.create_simple_backup()
                return

            # عرض شريط التقدم
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            self.status_label.setText("جاري إنشاء النسخة الاحتياطية...")

            # تعطيل الزر
            sender = self.sender()
            if sender:
                sender.setEnabled(False)

            # إنشاء النسخة الاحتياطية مباشرة (بدون خيوط)
            try:
                backup_path = self.backup_manager.create_backup("manual")

                # إخفاء شريط التقدم
                self.progress_bar.setVisible(False)

                # إعادة تفعيل الزر
                if sender:
                    sender.setEnabled(True)

                if backup_path:
                    self.status_label.setText("✅ تم إنشاء النسخة الاحتياطية بنجاح")
                    QMessageBox.information(self, "نجح", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{os.path.basename(backup_path)}")
                    self.safe_load_backup_list()
                else:
                    self.status_label.setText("❌ فشل في إنشاء النسخة الاحتياطية")
                    QMessageBox.warning(self, "فشل", "فشل في إنشاء النسخة الاحتياطية")

            except Exception as e:
                print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
                self.progress_bar.setVisible(False)
                if sender:
                    sender.setEnabled(True)
                self.status_label.setText("❌ خطأ في إنشاء النسخة الاحتياطية")
                QMessageBox.critical(self, "خطأ", f"حدث خطأ في إنشاء النسخة الاحتياطية:\n{str(e)}")

        except Exception as e:
            print(f"خطأ عام في النسخ الاحتياطي: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ عام:\n{str(e)}")

    def create_simple_backup(self):
        """إنشاء نسخة احتياطية بسيطة بدون مدير النسخ الاحتياطي"""
        try:
            import shutil
            import os
            from datetime import datetime

            # إنشاء مجلد النسخ الاحتياطي
            backup_dir = "backups"
            os.makedirs(backup_dir, exist_ok=True)

            # نسخ قاعدة البيانات
            db_path = "database/business_system.db"
            if os.path.exists(db_path):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"simple_backup_{timestamp}.db"
                backup_path = os.path.join(backup_dir, backup_name)

                shutil.copy2(db_path, backup_path)

                QMessageBox.information(self, "نجح", f"تم إنشاء نسخة احتياطية بسيطة:\n{backup_name}")
                self.safe_load_backup_list()
            else:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على قاعدة البيانات")

        except Exception as e:
            print(f"خطأ في النسخة الاحتياطية البسيطة: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء النسخة الاحتياطية البسيطة:\n{str(e)}")

    def show_restore_info(self):
        """عرض معلومات الاستعادة"""
        QMessageBox.information(
            self,
            "استعادة النسخة الاحتياطية",
            "ميزة الاستعادة التلقائية قيد التطوير.\n\n"
            "للاستعادة اليدوية:\n"
            "1. أغلق البرنامج\n"
            "2. انسخ ملف النسخة الاحتياطية من مجلد backups\n"
            "3. استبدل ملف business_system.db في مجلد database\n"
            "4. أعد تشغيل البرنامج"
        )

    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            if not self.backup_manager:
                QMessageBox.warning(self, "تحذير", "مدير النسخ الاحتياطي غير متاح")
                return

            reply = QMessageBox.question(
                self,
                "تأكيد التنظيف",
                "هل تريد حذف النسخ الاحتياطية القديمة؟\n\nسيتم الاحتفاظ بآخر 30 نسخة فقط.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.backup_manager.cleanup_old_backups()
                QMessageBox.information(self, "نجح", "تم تنظيف النسخ الاحتياطية القديمة بنجاح")
                self.safe_load_backup_list()

        except Exception as e:
            print(f"خطأ في تنظيف النسخ الاحتياطية: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تنظيف النسخ الاحتياطية:\n{str(e)}")

    def export_backup(self):
        """تصدير نسخة احتياطية"""
        try:
            current_row = self.backups_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار نسخة احتياطية للتصدير")
                return

            QMessageBox.information(
                self,
                "تصدير النسخة الاحتياطية",
                "يمكنك العثور على النسخ الاحتياطية في مجلد 'backups' داخل مجلد البرنامج.\n\n"
                "يمكنك نسخها إلى أي مكان آخر للحفظ الآمن."
            )

        except Exception as e:
            print(f"خطأ في تصدير النسخة الاحتياطية: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تصدير النسخة الاحتياطية:\n{str(e)}")

    def show_backup_details(self, backup_info):
        """عرض تفاصيل النسخة الاحتياطية"""
        try:
            details = f"""
تفاصيل النسخة الاحتياطية:

التاريخ والوقت: {backup_info.get('timestamp', 'غير محدد')}
النوع: {backup_info.get('type', 'غير محدد')}
الحالة: {backup_info.get('status', 'غير محدد')}
الحجم: {self.format_file_size(backup_info.get('size', 0))}
المسار: {backup_info.get('path', 'غير محدد')}
"""

            QMessageBox.information(self, "تفاصيل النسخة الاحتياطية", details)

        except Exception as e:
            print(f"خطأ في عرض تفاصيل النسخة الاحتياطية: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عرض التفاصيل:\n{str(e)}")

    def load_logs(self):
        """تحميل السجلات"""
        try:
            log_file = os.path.join("backups", 'backup.log')
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    logs = f.read()
                self.logs_text.setText(logs)

                # التمرير إلى النهاية
                cursor = self.logs_text.textCursor()
                cursor.movePosition(cursor.End)
                self.logs_text.setTextCursor(cursor)
            else:
                self.logs_text.setText("لا توجد سجلات متاحة")

        except Exception as e:
            print(f"خطأ في تحميل السجلات: {e}")
            self.logs_text.setText(f"خطأ في تحميل السجلات: {str(e)}")

    def clear_logs(self):
        """مسح السجلات"""
        try:
            reply = QMessageBox.question(
                self,
                "تأكيد المسح",
                "هل تريد مسح جميع السجلات؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                log_file = os.path.join("backups", 'backup.log')
                if os.path.exists(log_file):
                    open(log_file, 'w').close()  # مسح محتوى الملف

                self.logs_text.clear()
                QMessageBox.information(self, "نجح", "تم مسح السجلات بنجاح")

        except Exception as e:
            print(f"خطأ في مسح السجلات: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في مسح السجلات:\n{str(e)}")

    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            if not self.backup_manager:
                return

            config = self.backup_manager.config

            self.auto_backup_enabled.setChecked(config.get("auto_backup_enabled", True))
            self.backup_interval.setValue(config.get("backup_interval_hours", 6))
            self.backup_on_startup.setChecked(config.get("backup_on_startup", False))
            self.backup_on_shutdown.setChecked(config.get("backup_on_shutdown", False))
            self.max_backups.setValue(config.get("max_backups", 30))
            self.compress_backups.setChecked(config.get("compress_backups", True))
            self.backup_database.setChecked(config.get("backup_database", True))
            self.backup_config.setChecked(config.get("backup_config", True))
            self.backup_reports.setChecked(config.get("backup_reports", True))

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            if not self.backup_manager:
                QMessageBox.warning(self, "تحذير", "مدير النسخ الاحتياطي غير متاح")
                return

            self.backup_manager.config.update({
                "auto_backup_enabled": self.auto_backup_enabled.isChecked(),
                "backup_interval_hours": self.backup_interval.value(),
                "backup_on_startup": self.backup_on_startup.isChecked(),
                "backup_on_shutdown": self.backup_on_shutdown.isChecked(),
                "max_backups": self.max_backups.value(),
                "compress_backups": self.compress_backups.isChecked(),
                "backup_database": self.backup_database.isChecked(),
                "backup_config": self.backup_config.isChecked(),
                "backup_reports": self.backup_reports.isChecked()
            })

            self.backup_manager.save_config()
            QMessageBox.information(self, "نجح", "تم حفظ الإعدادات بنجاح")

        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ الإعدادات:\n{str(e)}")

    def update_backup_progress(self, message):
        """تحديث تقدم النسخ الاحتياطي"""
        try:
            self.status_label.setText(message)
            self.statusBar().showMessage(message)
        except Exception as e:
            print(f"خطأ في تحديث التقدم: {e}")

    def backup_finished(self, success, result, button=None):
        """انتهاء النسخ الاحتياطي"""
        try:
            self.progress_bar.setVisible(False)

            # إعادة تفعيل الزر
            if button:
                button.setEnabled(True)

            if success:
                self.status_label.setText("تم إنشاء النسخة الاحتياطية بنجاح ✅")
                QMessageBox.information(self, "نجح", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{os.path.basename(result) if result else 'غير محدد'}")
                self.safe_load_backup_list()
            else:
                self.status_label.setText("فشل في إنشاء النسخة الاحتياطية ❌")
                QMessageBox.critical(self, "خطأ", f"فشل في إنشاء النسخة الاحتياطية:\n{result}")

        except Exception as e:
            print(f"خطأ في معالجة انتهاء النسخ الاحتياطي: {e}")
            if button:
                button.setEnabled(True)

    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        try:
            # إيقاف المؤقت إذا كان موجوداً
            if hasattr(self, 'refresh_timer'):
                self.refresh_timer.stop()
        except Exception as e:
            print(f"خطأ في إغلاق النافذة: {e}")

        event.accept()

    def update_backup_progress(self, message):
        """تحديث تقدم النسخ الاحتياطي"""
        self.status_label.setText(message)
        self.statusBar().showMessage(message)

    def backup_finished(self, success, result, button=None):
        """انتهاء النسخ الاحتياطي"""
        try:
            self.progress_bar.setVisible(False)

            # إعادة تفعيل الزر
            if button:
                button.setEnabled(True)

            if success:
                self.status_label.setText("تم إنشاء النسخة الاحتياطية بنجاح ✅")
                QMessageBox.information(self, "نجح", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{os.path.basename(result) if result else 'غير محدد'}")

                # تحديث القائمة
                try:
                    self.load_backup_list()
                except Exception as e:
                    print(f"خطأ في تحديث القائمة: {e}")
            else:
                self.status_label.setText("فشل في إنشاء النسخة الاحتياطية ❌")
                QMessageBox.critical(self, "خطأ", f"فشل في إنشاء النسخة الاحتياطية:\n{result}")

        except Exception as e:
            print(f"خطأ في معالجة انتهاء النسخ الاحتياطي: {e}")
            if button:
                button.setEnabled(True)

    def show_backup_details(self, backup_info):
        """عرض تفاصيل النسخة الاحتياطية"""
        details = f"""
تفاصيل النسخة الاحتياطية:

التاريخ والوقت: {backup_info.get('timestamp', 'غير محدد')}
النوع: {backup_info.get('type', 'غير محدد')}
الحالة: {backup_info.get('status', 'غير محدد')}
الحجم: {self.format_file_size(backup_info.get('size', 0))}
المسار: {backup_info.get('path', 'غير محدد')}

الملفات المضمنة:
"""

        files = backup_info.get('files', [])
        if files:
            for file_path in files:
                details += f"• {os.path.basename(file_path)}\n"
        else:
            details += "لا توجد معلومات عن الملفات"

        QMessageBox.information(self, "تفاصيل النسخة الاحتياطية", details)

    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        # إيقاف المؤقت
        if hasattr(self, 'refresh_timer'):
            self.refresh_timer.stop()

        event.accept()
