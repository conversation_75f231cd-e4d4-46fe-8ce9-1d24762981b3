#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة إدارة المنتجات والمخزون - نظام نقاط البيع والمحاسبة
Products and Inventory Management Window - POS and Accounting System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                            QDialog, QFormLayout, QMessageBox, QHeaderView,
                            QAbstractItemView, QFrame, QGroupBox, QGridLayout,
                            QTextEdit, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from utils.config import Config
# from ui.simple_search import SimpleSearchWidget  # تعطيل مؤقت
import uuid
from datetime import datetime

class ProductsWindow(QWidget):
    """نافذة إدارة المنتجات والمخزون"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        self.load_products()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة المنتجات والمخزون")
        self.setGeometry(100, 100, 1000, 700)

        # تطبيق التصميم العصري
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                font-family: 'Segoe UI', Arial, sans-serif;
                color: #2c3e50;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f1f3f4);
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px 15px;
                font-size: 13px;
                font-weight: 500;
                color: #2c3e50;
                min-height: 25px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-color: #667eea;
                color: white;
            }
            QTableWidget {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f0f0f0;
                selection-background-color: #667eea;
            }
            QTableWidget::item:selected {
                background-color: #667eea;
                color: white;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: black;
                padding: 10px;
                border: none;
                font-weight: 600;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                background-color: white;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #667eea;
                outline: none;
            }
        """)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        add_btn = QPushButton("إضافة منتج جديد")
        add_btn.clicked.connect(self.add_product)
        toolbar_layout.addWidget(add_btn)
        
        edit_btn = QPushButton("تعديل المنتج")
        edit_btn.clicked.connect(self.edit_product)
        toolbar_layout.addWidget(edit_btn)
        
        delete_btn = QPushButton("حذف المنتج")
        delete_btn.clicked.connect(self.delete_product)
        toolbar_layout.addWidget(delete_btn)
        
        toolbar_layout.addStretch()
        
        # البحث
        search_label = QLabel("البحث:")
        toolbar_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث بالاسم أو الباركود...")
        self.search_input.textChanged.connect(self.search_products)
        toolbar_layout.addWidget(self.search_input)

        main_layout.addLayout(toolbar_layout)
        
        # جدول المنتجات
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(10)
        self.products_table.setHorizontalHeaderLabels([
            "الرقم", "اسم المنتج", "الباركود", "التصنيف", "الوحدة",
            "سعر الشراء", "سعر الجملة", "سعر نص الجملة", "سعر القطاعي",
            "الكمية الحالية"
        ])
        
        # تنسيق الجدول
        header = self.products_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.products_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.products_table.setAlternatingRowColors(True)
        self.products_table.doubleClicked.connect(self.edit_product)
        
        main_layout.addWidget(self.products_table)
        
        # شريط الحالة
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("جاري التحميل...")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # تنبيهات المخزون
        self.low_stock_label = QLabel()
        self.low_stock_label.setStyleSheet("color: red; font-weight: bold;")
        status_layout.addWidget(self.low_stock_label)
        
        main_layout.addLayout(status_layout)
        
        self.setLayout(main_layout)
        
    def load_products(self):
        """تحميل المنتجات من قاعدة البيانات"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, name, barcode, description, unit, purchase_price,
                   wholesale_price, semi_wholesale_price, retail_price,
                   current_stock, min_stock_alert
            FROM products
            WHERE is_active = 1
            ORDER BY name
        ''')
        
        products = cursor.fetchall()
        conn.close()
        
        # تحديث الجدول
        self.products_table.setRowCount(len(products))
        
        low_stock_count = 0
        
        for row, product in enumerate(products):
            self.products_table.setItem(row, 0, QTableWidgetItem(str(product['id'])))
            self.products_table.setItem(row, 1, QTableWidgetItem(product['name']))
            self.products_table.setItem(row, 2, QTableWidgetItem(product['barcode'] or ''))
            self.products_table.setItem(row, 3, QTableWidgetItem(product['description'] or ''))
            self.products_table.setItem(row, 4, QTableWidgetItem(product['unit'] or ''))
            self.products_table.setItem(row, 5, QTableWidgetItem(f"{product['purchase_price']:.2f}"))
            self.products_table.setItem(row, 6, QTableWidgetItem(f"{product['wholesale_price']:.2f}"))
            self.products_table.setItem(row, 7, QTableWidgetItem(f"{product['semi_wholesale_price']:.2f}"))
            self.products_table.setItem(row, 8, QTableWidgetItem(f"{product['retail_price']:.2f}"))

            # تلوين الكمية حسب المخزون
            stock_item = QTableWidgetItem(str(product['current_stock']))
            if product['current_stock'] <= product['min_stock_alert']:
                stock_item.setBackground(QColor(255, 200, 200))  # أحمر فاتح
                low_stock_count += 1
            self.products_table.setItem(row, 9, stock_item)
            
        # تحديث شريط الحالة
        self.status_label.setText(f"إجمالي المنتجات: {len(products)}")
        
        if low_stock_count > 0:
            self.low_stock_label.setText(f"تنبيه: {low_stock_count} منتج تحت الحد الأدنى!")
        else:
            self.low_stock_label.setText("")
            
    def search_products(self):
        """البحث في المنتجات"""
        search_text = self.search_input.text().lower()
        
        for row in range(self.products_table.rowCount()):
            show_row = False
            
            # البحث في الاسم والباركود
            name_item = self.products_table.item(row, 2)
            barcode_item = self.products_table.item(row, 1)
            
            if name_item and search_text in name_item.text().lower():
                show_row = True
            elif barcode_item and search_text in barcode_item.text().lower():
                show_row = True
                
            self.products_table.setRowHidden(row, not show_row)
            
    def add_product(self):
        """إضافة منتج جديد"""
        dialog = ProductDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_products()
            
    def edit_product(self):
        """تعديل منتج"""
        current_row = self.products_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج للتعديل")
            return
            
        product_id = int(self.products_table.item(current_row, 0).text())
        dialog = ProductDialog(self.db_manager, product_id, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_products()
            
    def delete_product(self):
        """حذف منتج"""
        current_row = self.products_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج للحذف")
            return
            
        product_name = self.products_table.item(current_row, 2).text()
        
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   f"هل تريد حذف المنتج '{product_name}'؟\n"
                                   "سيتم إلغاء تفعيل المنتج فقط.",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            product_id = int(self.products_table.item(current_row, 0).text())
            
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE products 
                SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (product_id,))
            
            conn.commit()
            conn.close()
            
            self.load_products()
            QMessageBox.information(self, "نجح", "تم حذف المنتج بنجاح")


class ProductDialog(QDialog):
    """نافذة حوار إضافة/تعديل المنتجات"""

    def __init__(self, db_manager, product_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.product_id = product_id
        self.init_ui()

        if product_id:
            self.load_product_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        title = "تعديل المنتج" if self.product_id else "إضافة منتج جديد"
        self.setWindowTitle(title)
        self.setFixedSize(500, 600)

        layout = QVBoxLayout()

        # معلومات أساسية
        basic_group = QGroupBox("المعلومات الأساسية")
        basic_layout = QFormLayout()

        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("اسم المنتج")
        basic_layout.addRow("اسم المنتج *:", self.name_input)

        self.barcode_input = QLineEdit()
        self.barcode_input.setPlaceholderText("الباركود (اختياري)")
        basic_layout.addRow("الباركود:", self.barcode_input)

        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(80)
        self.description_input.setPlaceholderText("وصف المنتج")
        basic_layout.addRow("الوصف:", self.description_input)

        self.unit_input = QLineEdit()
        self.unit_input.setText("قطعة")
        basic_layout.addRow("الوحدة:", self.unit_input)

        basic_group.setLayout(basic_layout)
        layout.addWidget(basic_group)

        # الأسعار
        prices_group = QGroupBox("الأسعار")
        prices_layout = QFormLayout()

        self.purchase_price_input = QDoubleSpinBox()
        self.purchase_price_input.setMaximum(999999.99)
        self.purchase_price_input.setDecimals(2)
        prices_layout.addRow("سعر الشراء *:", self.purchase_price_input)

        self.wholesale_price_input = QDoubleSpinBox()
        self.wholesale_price_input.setMaximum(999999.99)
        self.wholesale_price_input.setDecimals(2)
        prices_layout.addRow("سعر الجملة *:", self.wholesale_price_input)

        self.semi_wholesale_price_input = QDoubleSpinBox()
        self.semi_wholesale_price_input.setMaximum(999999.99)
        self.semi_wholesale_price_input.setDecimals(2)
        prices_layout.addRow("سعر نص الجملة *:", self.semi_wholesale_price_input)

        self.retail_price_input = QDoubleSpinBox()
        self.retail_price_input.setMaximum(999999.99)
        self.retail_price_input.setDecimals(2)
        prices_layout.addRow("سعر القطاعي *:", self.retail_price_input)

        prices_group.setLayout(prices_layout)
        layout.addWidget(prices_group)

        # المخزون
        stock_group = QGroupBox("إدارة المخزون")
        stock_layout = QFormLayout()

        self.current_stock_input = QSpinBox()
        self.current_stock_input.setMaximum(999999)
        stock_layout.addRow("الكمية الحالية:", self.current_stock_input)

        self.min_stock_input = QSpinBox()
        self.min_stock_input.setMaximum(999999)
        self.min_stock_input.setValue(5)
        stock_layout.addRow("الحد الأدنى للتنبيه:", self.min_stock_input)

        stock_group.setLayout(stock_layout)
        layout.addWidget(stock_group)

        # الأزرار
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("حفظ")
        save_btn.clicked.connect(self.save_product)
        buttons_layout.addWidget(save_btn)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

        self.setLayout(layout)

    def load_product_data(self):
        """تحميل بيانات المنتج للتعديل"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('SELECT * FROM products WHERE id = ?', (self.product_id,))
        product = cursor.fetchone()
        conn.close()

        if product:
            self.name_input.setText(product['name'])
            self.barcode_input.setText(product['barcode'] or '')
            self.description_input.setPlainText(product['description'] or '')
            self.unit_input.setText(product['unit'])
            self.purchase_price_input.setValue(product['purchase_price'])
            self.wholesale_price_input.setValue(product['wholesale_price'])
            self.semi_wholesale_price_input.setValue(product['semi_wholesale_price'])
            self.retail_price_input.setValue(product['retail_price'])
            self.current_stock_input.setValue(product['current_stock'])
            self.min_stock_input.setValue(product['min_stock_alert'])

    def save_product(self):
        """حفظ المنتج"""
        # التحقق من البيانات المطلوبة
        if not self.name_input.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المنتج")
            return

        if self.purchase_price_input.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال سعر شراء صحيح")
            return

        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        try:
            if self.product_id:
                # تحديث منتج موجود
                cursor.execute('''
                    UPDATE products
                    SET name=?, barcode=?, description=?, unit=?,
                        purchase_price=?, wholesale_price=?, semi_wholesale_price=?, retail_price=?,
                        current_stock=?, min_stock_alert=?, updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                ''', (
                    self.name_input.text().strip(),
                    self.barcode_input.text().strip() or None,
                    self.description_input.toPlainText().strip() or None,
                    self.unit_input.text().strip(),
                    self.purchase_price_input.value(),
                    self.wholesale_price_input.value(),
                    self.semi_wholesale_price_input.value(),
                    self.retail_price_input.value(),
                    self.current_stock_input.value(),
                    self.min_stock_input.value(),
                    self.product_id
                ))
            else:
                # إضافة منتج جديد
                cursor.execute('''
                    INSERT INTO products
                    (name, barcode, description, unit, purchase_price, wholesale_price,
                     semi_wholesale_price, retail_price, current_stock, min_stock_alert)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    self.name_input.text().strip(),
                    self.barcode_input.text().strip() or None,
                    self.description_input.toPlainText().strip() or None,
                    self.unit_input.text().strip(),
                    self.purchase_price_input.value(),
                    self.wholesale_price_input.value(),
                    self.semi_wholesale_price_input.value(),
                    self.retail_price_input.value(),
                    self.current_stock_input.value(),
                    self.min_stock_input.value()
                ))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم حفظ المنتج بنجاح")
            self.accept()

        except Exception as e:
            conn.close()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ المنتج:\n{str(e)}")

    def apply_advanced_search(self, criteria):
        """تطبيق البحث المتقدم"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # بناء الاستعلام
            query = """
                SELECT id, name, barcode, description, unit, purchase_price,
                       wholesale_price, semi_wholesale_price, retail_price,
                       current_stock, min_stock_alert
                FROM products
                WHERE is_active = 1
            """
            params = []

            # إضافة شروط البحث
            if 'name' in criteria:
                query += " AND name LIKE ?"
                params.append(f"%{criteria['name']}%")

            if 'code' in criteria:
                query += " AND barcode LIKE ?"
                params.append(f"%{criteria['code']}%")

            if 'price_min' in criteria:
                query += " AND retail_price >= ?"
                params.append(criteria['price_min'])

            if 'price_max' in criteria:
                query += " AND retail_price <= ?"
                params.append(criteria['price_max'])

            # شروط حالة المخزون
            if 'stock_status' in criteria:
                status = criteria['stock_status']
                if status == "available":
                    query += " AND current_stock > min_stock_alert"
                elif status == "out_of_stock":
                    query += " AND current_stock = 0"
                elif status == "low_stock":
                    query += " AND current_stock > 0 AND current_stock <= min_stock_alert"

            query += " ORDER BY name"

            cursor.execute(query, params)
            products = cursor.fetchall()

            # تحديث الجدول
            self.update_products_table(products)

            conn.close()

        except Exception as e:
            print(f"خطأ في البحث المتقدم: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في البحث المتقدم:\n{str(e)}")

    def update_products_table(self, products):
        """تحديث جدول المنتجات"""
        self.products_table.setRowCount(len(products))

        for row, product in enumerate(products):
            self.products_table.setItem(row, 0, QTableWidgetItem(str(product['id'])))
            self.products_table.setItem(row, 1, QTableWidgetItem(product['name']))
            self.products_table.setItem(row, 2, QTableWidgetItem(product['barcode'] or ''))
            self.products_table.setItem(row, 3, QTableWidgetItem(product['description'] or ''))
            self.products_table.setItem(row, 4, QTableWidgetItem(product['unit']))
            self.products_table.setItem(row, 5, QTableWidgetItem(f"{product['purchase_price']:.2f}"))
            self.products_table.setItem(row, 6, QTableWidgetItem(f"{product['wholesale_price']:.2f}"))
            self.products_table.setItem(row, 7, QTableWidgetItem(f"{product['semi_wholesale_price']:.2f}"))
            self.products_table.setItem(row, 8, QTableWidgetItem(f"{product['retail_price']:.2f}"))

            # تلوين المخزون حسب الحالة
            stock_item = QTableWidgetItem(f"{product['current_stock']:.0f}")
            if product['current_stock'] == 0:
                stock_item.setBackground(QColor(255, 200, 200))  # أحمر - نفد المخزون
            elif product['current_stock'] <= product['min_stock_alert']:
                stock_item.setBackground(QColor(255, 255, 200))  # أصفر - مخزون منخفض
            else:
                stock_item.setBackground(QColor(200, 255, 200))  # أخضر - متوفر

            self.products_table.setItem(row, 9, stock_item)
