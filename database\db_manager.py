#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات - نظام نقاط البيع والمحاسبة
Database Manager - POS and Accounting System
"""

import sqlite3
import hashlib
from datetime import datetime

class DatabaseManager:
    """مدير قاعدة البيانات الرئيسي"""
    
    def __init__(self, db_path="pos_database.db"):
        self.db_path = db_path
        self.init_database()
        # تحديث قاعدة البيانات للإصدارات الجديدة
        self.update_database_schema()
        # تهيئة الحسابات المالية فقط إذا لم تكن موجودة
        self._initialize_financial_accounts_if_needed()
        
    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn

    def execute_query(self, query, params=None, fetch_one=False, fetch_all=False):
        """دالة مساعدة لتنفيذ الاستعلامات وتقليل التكرار"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            result = None
            if fetch_one:
                result = cursor.fetchone()
            elif fetch_all:
                result = cursor.fetchall()

            conn.commit()
            conn.close()
            return result

        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {str(e)}")
            if 'conn' in locals():
                try:
                    conn.rollback()
                    conn.close()
                except:
                    pass
            return None
        
    def init_database(self):
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT NOT NULL CHECK (role IN ('admin', 'cashier')),
                phone TEXT,
                email TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول التصنيفات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المنتجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                barcode TEXT UNIQUE,
                name TEXT NOT NULL,
                description TEXT,
                category_id INTEGER,
                purchase_price REAL NOT NULL DEFAULT 0,
                wholesale_price REAL NOT NULL DEFAULT 0,
                semi_wholesale_price REAL NOT NULL DEFAULT 0,
                retail_price REAL NOT NULL DEFAULT 0,
                current_stock INTEGER DEFAULT 0,
                min_stock_alert INTEGER DEFAULT 5,
                unit TEXT DEFAULT 'قطعة',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES categories (id)
            )
        ''')
        
        # جدول العملاء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                email TEXT,
                customer_type TEXT DEFAULT 'retail' CHECK (customer_type IN ('retail', 'wholesale', 'semi_wholesale')),
                credit_limit REAL DEFAULT 0,
                current_balance REAL DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الموردين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                address TEXT,
                email TEXT,
                current_balance REAL DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                invoice_type TEXT NOT NULL CHECK (invoice_type IN ('sale', 'purchase')),
                customer_id INTEGER,
                supplier_id INTEGER,
                user_id INTEGER NOT NULL,
                total_amount REAL NOT NULL DEFAULT 0,
                discount_amount REAL DEFAULT 0,
                tax_amount REAL DEFAULT 0,
                final_amount REAL NOT NULL DEFAULT 0,
                paid_amount REAL DEFAULT 0,
                remaining_amount REAL DEFAULT 0,
                payment_method TEXT DEFAULT 'cash' CHECK (payment_method IN ('cash', 'bank_transfer', 'credit', 'partial')),
                invoice_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                due_date TIMESTAMP,
                notes TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # جدول تفاصيل الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity REAL NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                discount_amount REAL DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')

        # جدول المدفوعات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER,
                customer_id INTEGER,
                supplier_id INTEGER,
                amount REAL NOT NULL,
                payment_method TEXT NOT NULL CHECK (payment_method IN ('cash', 'bank_transfer')),
                payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                reference_number TEXT,
                notes TEXT,
                user_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id),
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # إنشاء جداول الحسابات المالية

        # جدول الحسابات البنكية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bank_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_name TEXT NOT NULL,
                bank_name TEXT NOT NULL,
                account_number TEXT,
                current_balance REAL DEFAULT 0,
                account_type TEXT DEFAULT 'checking' CHECK (account_type IN ('checking', 'savings', 'business')),
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول التحويلات البنكية - إنشاء آمن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bank_transfers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bank_account_id INTEGER DEFAULT 1,
                customer_id INTEGER,
                supplier_id INTEGER,
                amount REAL NOT NULL DEFAULT 0,
                transfer_type TEXT DEFAULT 'in',
                transfer_date TEXT DEFAULT (date('now')),
                reference_number TEXT,
                bank_name TEXT,
                notes TEXT,
                linked_invoice_id INTEGER,
                user_id INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # إنشاء جدول الحسابات البنكية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bank_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_name TEXT NOT NULL DEFAULT 'الحساب الرئيسي',
                account_number TEXT,
                bank_name TEXT DEFAULT 'البنك الافتراضي',
                balance REAL DEFAULT 0,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # إنشاء حساب افتراضي إذا لم يوجد
        try:
            cursor.execute('SELECT COUNT(*) FROM bank_accounts')
            if cursor.fetchone()[0] == 0:
                cursor.execute('''
                    INSERT INTO bank_accounts (account_name, bank_name, balance, is_active)
                    VALUES ('الحساب الرئيسي', 'البنك الافتراضي', 0, 1)
                ''')
        except:
            pass  # تجاهل الأخطاء في هذا الجزء



        # جدول مرتجعات المبيعات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales_returns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                return_number TEXT UNIQUE NOT NULL,
                return_date TEXT NOT NULL,
                original_invoice_id INTEGER NOT NULL,
                customer_id INTEGER,
                total_amount REAL NOT NULL DEFAULT 0,
                notes TEXT,
                user_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (original_invoice_id) REFERENCES invoices (id),
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # جدول تفاصيل مرتجعات المبيعات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales_return_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                return_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity REAL NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                reason TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (return_id) REFERENCES sales_returns (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')





        # جدول مرتجعات المشتريات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS purchase_returns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                return_number TEXT UNIQUE NOT NULL,
                return_date TEXT NOT NULL,
                original_invoice_id INTEGER NOT NULL,
                supplier_id INTEGER,
                total_amount REAL NOT NULL DEFAULT 0,
                notes TEXT,
                user_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (original_invoice_id) REFERENCES invoices (id),
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # جدول تفاصيل مرتجعات المشتريات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS purchase_return_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                return_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity REAL NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                reason TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (return_id) REFERENCES purchase_returns (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')

        # جدول المرتجعات (الجدول القديم - للتوافق مع الإصدارات السابقة)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS returns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                return_number TEXT UNIQUE NOT NULL,
                original_invoice_id INTEGER NOT NULL,
                return_type TEXT NOT NULL CHECK (return_type IN ('sale_return', 'purchase_return')),
                total_amount REAL NOT NULL DEFAULT 0,
                return_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                reason TEXT,
                user_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (original_invoice_id) REFERENCES invoices (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # جدول تفاصيل المرتجعات (الجدول القديم - للتوافق مع الإصدارات السابقة)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS return_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                return_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity REAL NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (return_id) REFERENCES returns (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')

        # جدول حركة المخزون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                movement_type TEXT NOT NULL CHECK (movement_type IN ('in', 'out', 'adjustment')),
                quantity REAL NOT NULL,
                reference_type TEXT CHECK (reference_type IN ('invoice', 'return', 'adjustment')),
                reference_id INTEGER,
                notes TEXT,
                movement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                user_id INTEGER NOT NULL,
                FOREIGN KEY (product_id) REFERENCES products (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # جدول أنواع المصاريف
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS expense_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول المصاريف
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS expenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                expense_number TEXT UNIQUE NOT NULL,
                category_id INTEGER NOT NULL,
                amount REAL NOT NULL,
                description TEXT,
                expense_date DATE NOT NULL,
                payment_method TEXT DEFAULT 'cash' CHECK (payment_method IN ('cash', 'bank_transfer')),
                reference_number TEXT,
                receipt_image TEXT,
                user_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES expense_categories (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # جدول رأس المال
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS capital_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_type TEXT NOT NULL CHECK (transaction_type IN ('initial_capital', 'capital_increase', 'capital_decrease', 'withdrawal')),
                amount REAL NOT NULL,
                description TEXT,
                transaction_date DATE NOT NULL,
                reference_number TEXT,
                user_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # جدول الحسابات المالية (البنك، النقدية، الخزينة)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS financial_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_type TEXT NOT NULL CHECK (account_type IN ('bank', 'cash', 'treasury')),
                account_name TEXT NOT NULL,
                current_balance REAL DEFAULT 0,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول حركات الحسابات المالية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS account_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_id INTEGER NOT NULL,
                movement_type TEXT NOT NULL CHECK (movement_type IN ('in', 'out', 'transfer')),
                amount REAL NOT NULL,
                reference_type TEXT,
                reference_id INTEGER,
                description TEXT,
                movement_date DATE NOT NULL,
                user_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (account_id) REFERENCES financial_accounts (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # جدول التحويلات بين الحسابات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS account_transfers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                from_account_id INTEGER NOT NULL,
                to_account_id INTEGER NOT NULL,
                amount REAL NOT NULL,
                description TEXT,
                transfer_date DATE NOT NULL,
                user_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (from_account_id) REFERENCES financial_accounts (id),
                FOREIGN KEY (to_account_id) REFERENCES financial_accounts (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # جدول إعدادات النظام
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT UNIQUE NOT NULL,
                setting_value TEXT,
                description TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول بيانات الشركة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS company_info (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_name TEXT NOT NULL,
                company_arabic_name TEXT,
                commercial_registration TEXT,
                tax_id TEXT,
                address TEXT,
                phone TEXT,
                mobile TEXT,
                email TEXT,
                website TEXT,
                logo_path TEXT,
                manager_name TEXT,
                manager_phone TEXT,
                manager_email TEXT,
                bank_name TEXT,
                bank_account TEXT,
                iban TEXT,
                swift_code TEXT,
                currency TEXT DEFAULT 'جنيه',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # إنشاء فهارس لتحسين الأداء
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_invoice_items_invoice_id ON invoice_items(invoice_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_invoice_items_product_id ON invoice_items(product_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_movements_product_id ON stock_movements(product_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_movements_reference ON stock_movements(reference_type, reference_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_account_movements_account_id ON account_movements(account_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_invoices_type_date ON invoices(invoice_type, invoice_date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode)')

        conn.commit()
        conn.close()

        # إنشاء المستخدم الافتراضي
        self.create_default_admin()

        # إنشاء البيانات الافتراضية
        self.create_default_data()

        # تحديث مخطط قاعدة البيانات
        self.update_database_schema()
        
    def create_default_admin(self):
        """إنشاء مستخدم المدير الافتراضي"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # التحقق من وجود مستخدم مدير
        cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'admin'")
        admin_count = cursor.fetchone()[0]
        
        if admin_count == 0:
            # إنشاء مستخدم مدير افتراضي
            password_hash = self.hash_password("admin123")
            cursor.execute('''
                INSERT INTO users (username, password_hash, full_name, role, phone)
                VALUES (?, ?, ?, ?, ?)
            ''', ("admin", password_hash, "شادي الطيب", "admin", "01008379651"))
            
        conn.commit()
        conn.close()
        
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
        
    def verify_password(self, password, password_hash):
        """التحقق من كلمة المرور"""
        return self.hash_password(password) == password_hash
        
    def authenticate_user(self, username, password):
        """مصادقة المستخدم"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM users 
            WHERE username = ? AND is_active = 1
        ''', (username,))
        
        user = cursor.fetchone()
        conn.close()
        
        if user and self.verify_password(password, user['password_hash']):
            return dict(user)
        return None

    def create_default_data(self):
        """إنشاء البيانات الافتراضية للنظام"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # إنشاء أنواع المصاريف الافتراضية
        default_expense_categories = [
            ('إيجار المحل', 'إيجار المحل التجاري الشهري'),
            ('فواتير الكهرباء', 'فواتير استهلاك الكهرباء'),
            ('فواتير المياه', 'فواتير استهلاك المياه'),
            ('رواتب الموظفين', 'رواتب ومكافآت الموظفين'),
            ('مصاريف النقل', 'مصاريف النقل والمواصلات'),
            ('مصاريف الصيانة', 'صيانة المعدات والأجهزة'),
            ('مصاريف التسويق', 'مصاريف الإعلان والتسويق'),
            ('مصاريف إدارية', 'مصاريف إدارية متنوعة'),
            ('مصاريف أخرى', 'مصاريف متنوعة أخرى')
        ]

        for category_name, description in default_expense_categories:
            cursor.execute('''
                INSERT OR IGNORE INTO expense_categories (name, description)
                VALUES (?, ?)
            ''', (category_name, description))

        # إنشاء حساب بنكي افتراضي
        cursor.execute('''
            INSERT OR IGNORE INTO bank_accounts (id, account_name, bank_name, account_number, current_balance)
            VALUES (1, 'الحساب الرئيسي', 'البنك الرئيسي', '*********', 0)
        ''')

        # إنشاء إعدادات النظام الافتراضية
        default_settings = [
            ('initial_capital_set', 'false', 'هل تم تحديد رأس المال الأولي'),
            ('initial_capital_amount', '0', 'مبلغ رأس المال الأولي'),
            ('company_start_date', '', 'تاريخ بداية عمل الشركة'),
            ('expense_number_prefix', 'EXP', 'بادئة رقم المصروف'),
            ('capital_number_prefix', 'CAP', 'بادئة رقم معاملة رأس المال'),
            ('default_bank_account_id', '1', 'معرف الحساب البنكي الافتراضي')
        ]

        for key, value, description in default_settings:
            cursor.execute('''
                INSERT OR IGNORE INTO system_settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            ''', (key, value, description))

        conn.commit()
        conn.close()

    def get_setting(self, key, default_value=None):
        """الحصول على قيمة إعداد من النظام"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('SELECT setting_value FROM system_settings WHERE setting_key = ?', (key,))
        result = cursor.fetchone()
        conn.close()

        return result['setting_value'] if result else default_value

    def set_setting(self, key, value):
        """تحديث قيمة إعداد في النظام"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            INSERT OR REPLACE INTO system_settings (setting_key, setting_value, updated_at)
            VALUES (?, ?, CURRENT_TIMESTAMP)
        ''', (key, value))

        conn.commit()
        conn.close()

    def get_current_capital(self):
        """حساب رأس المال الحالي"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT
                SUM(CASE WHEN transaction_type IN ('initial_capital', 'capital_increase') THEN amount ELSE 0 END) -
                SUM(CASE WHEN transaction_type IN ('capital_decrease', 'withdrawal') THEN amount ELSE 0 END) as current_capital
            FROM capital_transactions
        ''')

        result = cursor.fetchone()
        conn.close()

        return result['current_capital'] or 0

    def transfer_between_accounts(self, from_account_id, to_account_id, amount, description=None, user_id=1):
        """تحويل مبلغ بين الحسابات المالية"""
        try:
            # التحقق من توفر الرصيد
            from_balance = self.get_account_balance(from_account_id)
            if from_balance < amount:
                return False

            # خصم من الحساب المرسل
            self.update_account_balance(
                from_account_id, amount, 'subtract', 'transfer',
                to_account_id, f'تحويل إلى حساب آخر - {description or ""}', user_id
            )

            # إضافة للحساب المستقبل
            self.update_account_balance(
                to_account_id, amount, 'add', 'transfer',
                from_account_id, f'تحويل من حساب آخر - {description or ""}', user_id
            )

            return True

        except Exception as e:
            print(f"خطأ في التحويل بين الحسابات: {str(e)}")
            return False

    def get_customer_last_invoice_balance(self, customer_id):
        """الحصول على الرصيد الصحيح للعميل (آخر فاتورة - المدفوعات بعدها)"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # الحصول على آخر فاتورة
        cursor.execute('''
            SELECT id, remaining_amount, invoice_date, created_at
            FROM invoices
            WHERE customer_id = ? AND invoice_type = 'sale' AND is_active = 1
            ORDER BY invoice_date DESC, created_at DESC
            LIMIT 1
        ''', (customer_id,))

        last_invoice = cursor.fetchone()

        if not last_invoice:
            conn.close()
            return 0

        # حساب المدفوعات بعد آخر فاتورة
        cursor.execute('''
            SELECT COALESCE(SUM(amount), 0) as total_payments
            FROM payments
            WHERE customer_id = ?
            AND (payment_date > ? OR (payment_date = ? AND created_at > ?))
        ''', (
            customer_id,
            last_invoice['invoice_date'],
            last_invoice['invoice_date'],
            last_invoice['created_at']
        ))

        payments_after = cursor.fetchone()['total_payments']
        conn.close()

        # الرصيد = المتبقي من آخر فاتورة - المدفوعات بعدها
        current_balance = last_invoice['remaining_amount'] - payments_after

        return max(0, current_balance)  # لا يمكن أن يكون الرصيد سالب

    def get_bank_account_balance(self, account_id):
        """الحصول على رصيد الحساب البنكي"""
        result = self.execute_query(
            'SELECT balance FROM bank_accounts WHERE id = ?',
            (account_id,),
            fetch_one=True
        )
        if result:
            balance = result[0] if isinstance(result, tuple) else result['balance']
            return float(balance) if balance is not None else 0.0
        return 0.0

    def update_bank_account_balance(self, account_id, amount, operation='add'):
        """تحديث رصيد الحساب البنكي"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # التأكد من وجود الحساب البنكي
            cursor.execute('SELECT id, balance FROM bank_accounts WHERE id = ?', (account_id,))
            account = cursor.fetchone()

            if not account:
                # إنشاء حساب جديد إذا لم يوجد
                cursor.execute('''
                    INSERT INTO bank_accounts (id, account_name, balance, is_active)
                    VALUES (?, 'حساب تلقائي', 0, 1)
                ''', (account_id,))
                current_balance = 0
            else:
                current_balance = float(account[1]) if account[1] else 0

            # تحديث الرصيد
            if operation == 'add':
                new_balance = current_balance + amount
            else:  # subtract
                new_balance = current_balance - amount

            cursor.execute('UPDATE bank_accounts SET balance = ? WHERE id = ?', (new_balance, account_id))
            conn.commit()
            conn.close()

        except Exception:
            if 'conn' in locals():
                try:
                    conn.rollback()
                    conn.close()
                except:
                    pass
            # لا نرفع الخطأ لتجنب كسر التطبيق
            pass

    def get_default_bank_account_id(self):
        """الحصول على معرف الحساب البنكي الافتراضي"""
        result = self.execute_query(
            'SELECT id FROM bank_accounts WHERE is_active = 1 ORDER BY id LIMIT 1',
            fetch_one=True
        )
        if result:
            return result[0] if isinstance(result, tuple) else result['id']
        return 1  # قيمة افتراضية آمنة

    def record_bank_transfer_from_invoice(self, invoice_id, amount, reference_number=None):
        """تسجيل تحويل بنكي من فاتورة وتحديث رصيد البنك"""
        print(f"Debug: Starting bank transfer recording for invoice {invoice_id}, amount {amount}")

        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من وجود جدول bank_accounts
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='bank_accounts'")
            if not cursor.fetchone():
                print("Debug: bank_accounts table doesn't exist, creating it...")
                self.create_tables()  # إعادة إنشاء الجداول

            # الحصول على بيانات الفاتورة
            cursor.execute('''
                SELECT customer_id, supplier_id, invoice_type, user_id
                FROM invoices WHERE id = ?
            ''', (invoice_id,))
            invoice = cursor.fetchone()

            if not invoice:
                raise Exception("الفاتورة غير موجودة")

            print(f"Debug: Invoice found: {invoice}")

            # الحصول على الحساب البنكي الافتراضي
            default_account_id = self.get_default_bank_account_id()
            print(f"Debug: Default bank account ID: {default_account_id}")

            # تحديد نوع التحويل
            transfer_type = 'in' if invoice['invoice_type'] == 'sale' else 'out'
            print(f"Debug: Transfer type: {transfer_type}")

            # إدراج التحويل البنكي
            cursor.execute('''
                INSERT INTO bank_transfers
                (bank_account_id, customer_id, supplier_id, amount, transfer_type,
                 reference_number, notes, user_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                default_account_id,
                invoice['customer_id'],
                invoice['supplier_id'],
                amount,
                transfer_type,
                reference_number,
                f"تحويل من فاتورة رقم {invoice_id}",
                invoice['user_id']
            ))

            print("Debug: Bank transfer inserted successfully")

            # تحديث رصيد الحساب البنكي
            old_balance = self.get_bank_account_balance(default_account_id)
            print(f"Debug: Old balance: {old_balance}")

            if transfer_type == 'in':
                self.update_bank_account_balance(default_account_id, amount, 'add')
            else:
                self.update_bank_account_balance(default_account_id, amount, 'subtract')

            new_balance = self.get_bank_account_balance(default_account_id)
            print(f"Debug: New balance: {new_balance}")

            conn.commit()
            print("Debug: Transaction committed successfully")
            return True

        except Exception as e:
            print(f"Debug: Error in record_bank_transfer_from_invoice: {str(e)}")
            conn.rollback()
            raise e
        finally:
            conn.close()

    def _initialize_financial_accounts_if_needed(self):
        """تهيئة الحسابات المالية الافتراضية فقط إذا لم تكن موجودة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من وجود الحسابات
            cursor.execute('SELECT COUNT(*) as count FROM financial_accounts')
            count = cursor.fetchone()['count']

            if count == 0:
                # إنشاء الحسابات الافتراضية
                accounts = [
                    ('bank', 'حساب البنك'),
                    ('cash', 'النقدية'),
                    ('treasury', 'الخزينة')
                ]

                for account_type, account_name in accounts:
                    cursor.execute('''
                        INSERT INTO financial_accounts (account_type, account_name, current_balance)
                        VALUES (?, ?, 0)
                    ''', (account_type, account_name))

                conn.commit()
        except Exception as e:
            print(f"خطأ في تهيئة الحسابات المالية: {str(e)}")
        finally:
            conn.close()

    def initialize_financial_accounts(self):
        """تهيئة الحسابات المالية الافتراضية (للاستدعاء اليدوي)"""
        self._initialize_financial_accounts_if_needed()

    def get_financial_accounts(self):
        """الحصول على جميع الحسابات المالية"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM financial_accounts
            WHERE is_active = 1
            ORDER BY account_type
        ''')

        accounts = cursor.fetchall()
        conn.close()
        return accounts

    def get_account_balance(self, account_id):
        """الحصول على رصيد حساب معين"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT current_balance FROM financial_accounts
            WHERE id = ?
        ''', (account_id,))

        result = cursor.fetchone()
        conn.close()
        return result['current_balance'] if result else 0

    def get_account_by_type(self, account_type):
        """الحصول على حساب بنوعه"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM financial_accounts
            WHERE account_type = ? AND is_active = 1
            LIMIT 1
        ''', (account_type,))

        account = cursor.fetchone()
        conn.close()
        return account

    def update_account_balance(self, account_id, amount, operation='add', reference_type=None, reference_id=None, description=None, user_id=1):
        """تحديث رصيد حساب مالي"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            if operation == 'add':
                cursor.execute('''
                    UPDATE financial_accounts
                    SET current_balance = current_balance + ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (amount, account_id))
                movement_type = 'in'
            elif operation == 'subtract':
                cursor.execute('''
                    UPDATE financial_accounts
                    SET current_balance = current_balance - ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (amount, account_id))
                movement_type = 'out'

            # تسجيل حركة الحساب
            cursor.execute('''
                INSERT INTO account_movements
                (account_id, movement_type, amount, reference_type, reference_id, description, movement_date, user_id)
                VALUES (?, ?, ?, ?, ?, ?, DATE('now'), ?)
            ''', (account_id, movement_type, amount, reference_type, reference_id, description, user_id))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            conn.rollback()
            conn.close()
            raise e

    def transfer_between_accounts(self, from_account_id, to_account_id, amount, description=None, user_id=1):
        """تحويل مبلغ بين الحسابات"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من وجود رصيد كافي
            cursor.execute('SELECT current_balance FROM financial_accounts WHERE id = ?', (from_account_id,))
            from_balance = cursor.fetchone()['current_balance']

            if from_balance < amount:
                raise Exception("الرصيد غير كافي للتحويل")

            # خصم من الحساب المرسل
            cursor.execute('''
                UPDATE financial_accounts
                SET current_balance = current_balance - ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (amount, from_account_id))

            # إضافة للحساب المستقبل
            cursor.execute('''
                UPDATE financial_accounts
                SET current_balance = current_balance + ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (amount, to_account_id))

            # تسجيل التحويل
            cursor.execute('''
                INSERT INTO account_transfers
                (from_account_id, to_account_id, amount, description, transfer_date, user_id)
                VALUES (?, ?, ?, ?, DATE('now'), ?)
            ''', (from_account_id, to_account_id, amount, description, user_id))

            transfer_id = cursor.lastrowid

            # تسجيل حركات الحسابات
            cursor.execute('''
                INSERT INTO account_movements
                (account_id, movement_type, amount, reference_type, reference_id, description, movement_date, user_id)
                VALUES (?, 'transfer', ?, 'transfer', ?, ?, DATE('now'), ?)
            ''', (from_account_id, amount, transfer_id, f"تحويل إلى حساب {to_account_id}", user_id))

            cursor.execute('''
                INSERT INTO account_movements
                (account_id, movement_type, amount, reference_type, reference_id, description, movement_date, user_id)
                VALUES (?, 'transfer', ?, 'transfer', ?, ?, DATE('now'), ?)
            ''', (to_account_id, amount, transfer_id, f"تحويل من حساب {from_account_id}", user_id))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            conn.rollback()
            conn.close()
            raise e

    def update_database_schema(self):
        """تحديث مخطط قاعدة البيانات للإصدارات الجديدة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من وجود عمود linked_invoice_id في جدول bank_transfers
            cursor.execute("PRAGMA table_info(bank_transfers)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'linked_invoice_id' not in columns:
                print("إضافة عمود linked_invoice_id إلى جدول bank_transfers...")
                cursor.execute('''
                    ALTER TABLE bank_transfers
                    ADD COLUMN linked_invoice_id INTEGER
                    REFERENCES invoices(id)
                ''')
                print("تم إضافة العمود بنجاح")

            # إنشاء جداول فواتير الشراء المنفصلة
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS purchase_invoices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_number TEXT UNIQUE NOT NULL,
                    invoice_date TEXT NOT NULL,
                    due_date TEXT,
                    supplier_id INTEGER,
                    subtotal REAL NOT NULL DEFAULT 0,
                    tax_amount REAL DEFAULT 0,
                    total_amount REAL NOT NULL DEFAULT 0,
                    paid_amount REAL DEFAULT 0,
                    payment_method TEXT DEFAULT 'cash',
                    notes TEXT,
                    user_id INTEGER NOT NULL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS purchase_invoice_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    quantity REAL NOT NULL,
                    unit_price REAL NOT NULL,
                    discount REAL DEFAULT 0,
                    total_price REAL NOT NULL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (invoice_id) REFERENCES purchase_invoices (id) ON DELETE CASCADE,
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            ''')
            
            # إنشاء جدول بيانات الشركة إذا لم يكن موجوداً
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS company_info (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    company_name TEXT NOT NULL,
                    company_arabic_name TEXT,
                    commercial_registration TEXT,
                    tax_id TEXT,
                    address TEXT,
                    phone TEXT,
                    mobile TEXT,
                    email TEXT,
                    website TEXT,
                    logo_path TEXT,
                    manager_name TEXT,
                    manager_phone TEXT,
                    manager_email TEXT,
                    bank_name TEXT,
                    bank_account TEXT,
                    iban TEXT,
                    swift_code TEXT,
                    currency TEXT DEFAULT 'جنيه',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            conn.commit()

        except Exception as e:
            print(f"خطأ في تحديث قاعدة البيانات: {str(e)}")
            conn.rollback()
        finally:
            conn.close()
            
    def get_company_info(self):
        """الحصول على بيانات الشركة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # التأكد من وجود الجدول أولاً
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='company_info'")
            if not cursor.fetchone():
                # إنشاء الجدول إذا لم يكن موجوداً
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS company_info (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        company_name TEXT NOT NULL,
                        company_arabic_name TEXT,
                        commercial_registration TEXT,
                        tax_id TEXT,
                        address TEXT,
                        phone TEXT,
                        mobile TEXT,
                        email TEXT,
                        website TEXT,
                        logo_path TEXT,
                        manager_name TEXT,
                        manager_phone TEXT,
                        manager_email TEXT,
                        bank_name TEXT,
                        bank_account TEXT,
                        iban TEXT,
                        swift_code TEXT,
                        currency TEXT DEFAULT 'جنيه',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                conn.commit()
            
            cursor.execute('SELECT * FROM company_info ORDER BY id DESC LIMIT 1')
            result = cursor.fetchone()
            conn.close()
            return result
        except Exception as e:
            print(f"خطأ في الحصول على بيانات الشركة: {str(e)}")
            conn.close()
            return None
            
    def save_company_info(self, company_data):
        """حفظ بيانات الشركة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # التأكد من وجود الجدول أولاً
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='company_info'")
            if not cursor.fetchone():
                # إنشاء الجدول إذا لم يكن موجوداً
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS company_info (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        company_name TEXT NOT NULL,
                        company_arabic_name TEXT,
                        commercial_registration TEXT,
                        tax_id TEXT,
                        address TEXT,
                        phone TEXT,
                        mobile TEXT,
                        email TEXT,
                        website TEXT,
                        logo_path TEXT,
                        manager_name TEXT,
                        manager_phone TEXT,
                        manager_email TEXT,
                        bank_name TEXT,
                        bank_account TEXT,
                        iban TEXT,
                        swift_code TEXT,
                        currency TEXT DEFAULT 'جنيه',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                conn.commit()
            
            # التحقق من وجود بيانات سابقة
            cursor.execute('SELECT COUNT(*) FROM company_info')
            count = cursor.fetchone()[0]
            
            if count > 0:
                # تحديث البيانات الموجودة
                cursor.execute('''
                    UPDATE company_info SET
                    company_name = ?,
                    company_arabic_name = ?,
                    commercial_registration = ?,
                    tax_id = ?,
                    address = ?,
                    phone = ?,
                    mobile = ?,
                    email = ?,
                    website = ?,
                    logo_path = ?,
                    manager_name = ?,
                    manager_phone = ?,
                    manager_email = ?,
                    bank_name = ?,
                    bank_account = ?,
                    iban = ?,
                    swift_code = ?,
                    currency = ?,
                    updated_at = CURRENT_TIMESTAMP
                    WHERE id = (SELECT id FROM company_info ORDER BY id DESC LIMIT 1)
                ''', (
                    company_data['company_name'],
                    company_data.get('company_arabic_name', ''),
                    company_data.get('commercial_registration', ''),
                    company_data.get('tax_id', ''),
                    company_data.get('address', ''),
                    company_data.get('phone', ''),
                    company_data.get('mobile', ''),
                    company_data.get('email', ''),
                    company_data.get('website', ''),
                    company_data.get('logo_path', ''),
                    company_data.get('manager_name', ''),
                    company_data.get('manager_phone', ''),
                    company_data.get('manager_email', ''),
                    company_data.get('bank_name', ''),
                    company_data.get('bank_account', ''),
                    company_data.get('iban', ''),
                    company_data.get('swift_code', ''),
                    company_data.get('currency', 'جنيه')
                ))
            else:
                # إنشاء بيانات جديدة
                cursor.execute('''
                    INSERT INTO company_info (
                        company_name, company_arabic_name, commercial_registration,
                        tax_id, address, phone, mobile, email, website, logo_path,
                        manager_name, manager_phone, manager_email, bank_name,
                        bank_account, iban, swift_code, currency
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    company_data['company_name'],
                    company_data.get('company_arabic_name', ''),
                    company_data.get('commercial_registration', ''),
                    company_data.get('tax_id', ''),
                    company_data.get('address', ''),
                    company_data.get('phone', ''),
                    company_data.get('mobile', ''),
                    company_data.get('email', ''),
                    company_data.get('website', ''),
                    company_data.get('logo_path', ''),
                    company_data.get('manager_name', ''),
                    company_data.get('manager_phone', ''),
                    company_data.get('manager_email', ''),
                    company_data.get('bank_name', ''),
                    company_data.get('bank_account', ''),
                    company_data.get('iban', ''),
                    company_data.get('swift_code', ''),
                    company_data.get('currency', 'جنيه')
                ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            conn.rollback()
            conn.close()
            print(f"خطأ في حفظ بيانات الشركة: {str(e)}")
            return False
