"""
ألوان مريحة للعين - نظام الطيب للتجارة والتوزيع
"""

class ComfortableColors:
    """فئة تحتوي على الألوان المريحة للعين"""
    
    # الألوان الأساسية
    PRIMARY = "#007bff"          # أزرق هادئ
    SECONDARY = "#6c757d"        # رمادي متوسط
    SUCCESS = "#28a745"          # أخضر هادئ
    INFO = "#17a2b8"            # أزرق فاتح
    WARNING = "#ffc107"          # أصفر هادئ
    DANGER = "#dc3545"           # أحمر هادئ
    
    # ألوان الخلفية
    BACKGROUND_LIGHT = "#ffffff"     # أبيض نقي
    BACKGROUND_SOFT = "#f8f9fa"      # أبيض مائل للرمادي
    BACKGROUND_MUTED = "#e9ecef"     # رمادي فاتح جداً
    
    # ألوان النصوص
    TEXT_PRIMARY = "#2c3e50"         # رمادي داكن
    TEXT_SECONDARY = "#6c757d"       # رمادي متوسط
    TEXT_MUTED = "#adb5bd"          # رمادي فاتح
    
    # ألوان الحدود
    BORDER_LIGHT = "#dee2e6"         # حدود فاتحة
    BORDER_MEDIUM = "#ced4da"        # حدود متوسطة
    BORDER_DARK = "#adb5bd"          # حدود داكنة
    
    # ألوان الأزرار
    BUTTON_SALES = "#28a745"         # أخضر للمبيعات
    BUTTON_PURCHASES = "#6f42c1"     # بنفسجي للمشتريات
    BUTTON_INVENTORY = "#007bff"     # أزرق للمخزون
    BUTTON_SERVICES = "#fd7e14"      # برتقالي للخدمات
    BUTTON_CUSTOMERS = "#20c997"     # أخضر فاتح للعملاء
    BUTTON_REPORTS = "#17a2b8"       # أزرق فاتح للتقارير
    BUTTON_SETTINGS = "#6c757d"      # رمادي للإعدادات
    BUTTON_LOGOUT = "#dc3545"        # أحمر للخروج
    
    # ألوان التدرج
    GRADIENT_HEADER_START = "#e3f2fd"    # أزرق فاتح جداً
    GRADIENT_HEADER_END = "#bbdefb"      # أزرق فاتح
    
    GRADIENT_SIDEBAR_START = "#f8f9fa"   # أبيض مائل للرمادي
    GRADIENT_SIDEBAR_END = "#e9ecef"     # رمادي فاتح
    
    GRADIENT_CARD_START = "rgba(255, 255, 255, 0.95)"  # أبيض شفاف
    GRADIENT_CARD_END = "rgba(248, 249, 250, 0.9)"     # رمادي شفاف
    
    # ألوان الحالة
    STATUS_ACTIVE = "#28a745"        # أخضر للحالة النشطة
    STATUS_INACTIVE = "#6c757d"      # رمادي للحالة غير النشطة
    STATUS_PENDING = "#ffc107"       # أصفر للحالة المعلقة
    STATUS_ERROR = "#dc3545"         # أحمر للأخطاء
    
    # ألوان الإشعارات
    NOTIFICATION_SUCCESS = "#d4edda"     # أخضر فاتح
    NOTIFICATION_INFO = "#d1ecf1"        # أزرق فاتح
    NOTIFICATION_WARNING = "#fff3cd"     # أصفر فاتح
    NOTIFICATION_ERROR = "#f8d7da"       # أحمر فاتح
    
    @classmethod
    def get_button_color(cls, button_type):
        """الحصول على لون الزر حسب النوع"""
        colors = {
            'sales': cls.BUTTON_SALES,
            'purchases': cls.BUTTON_PURCHASES,
            'inventory': cls.BUTTON_INVENTORY,
            'services': cls.BUTTON_SERVICES,
            'customers': cls.BUTTON_CUSTOMERS,
            'reports': cls.BUTTON_REPORTS,
            'settings': cls.BUTTON_SETTINGS,
            'logout': cls.BUTTON_LOGOUT
        }
        return colors.get(button_type, cls.PRIMARY)
    
    @classmethod
    def get_status_color(cls, status):
        """الحصول على لون الحالة"""
        colors = {
            'active': cls.STATUS_ACTIVE,
            'inactive': cls.STATUS_INACTIVE,
            'pending': cls.STATUS_PENDING,
            'error': cls.STATUS_ERROR
        }
        return colors.get(status, cls.TEXT_SECONDARY)
    
    @classmethod
    def get_notification_color(cls, notification_type):
        """الحصول على لون الإشعار"""
        colors = {
            'success': cls.NOTIFICATION_SUCCESS,
            'info': cls.NOTIFICATION_INFO,
            'warning': cls.NOTIFICATION_WARNING,
            'error': cls.NOTIFICATION_ERROR
        }
        return colors.get(notification_type, cls.NOTIFICATION_INFO)

# ستايلات CSS جاهزة
COMFORTABLE_STYLES = {
    'main_window': f"""
        QWidget {{
            font-family: 'Segoe UI', 'Tahoma', Arial;
            background-color: {ComfortableColors.BACKGROUND_SOFT};
            color: {ComfortableColors.TEXT_PRIMARY};
        }}
    """,
    
    'sidebar': f"""
        QFrame {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {ComfortableColors.GRADIENT_SIDEBAR_START}, 
                stop:1 {ComfortableColors.GRADIENT_SIDEBAR_END});
            border-right: 1px solid {ComfortableColors.BORDER_LIGHT};
        }}
    """,
    
    'header': f"""
        QFrame {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {ComfortableColors.GRADIENT_HEADER_START}, 
                stop:1 {ComfortableColors.GRADIENT_HEADER_END});
            border-radius: 15px;
            border: 1px solid #e1f5fe;
        }}
    """,
    
    'card': f"""
        QFrame {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {ComfortableColors.GRADIENT_CARD_START}, 
                stop:1 {ComfortableColors.GRADIENT_CARD_END});
            border-radius: 15px;
            border: 2px solid {{color}};
        }}
    """,
    
    'button_primary': f"""
        QPushButton {{
            background-color: {{color}};
            color: white;
            border: none;
            border-radius: 8px;
            padding: 15px;
            font-size: 14px;
            font-weight: bold;
            text-align: left;
            margin: 2px;
        }}
        QPushButton:hover {{
            background-color: {ComfortableColors.TEXT_SECONDARY};
            color: white;
        }}
        QPushButton:pressed {{
            background-color: #343a40;
        }}
    """,
    
    'button_secondary': f"""
        QPushButton {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {ComfortableColors.GRADIENT_CARD_START}, 
                stop:1 {ComfortableColors.GRADIENT_CARD_END});
            color: {{color}};
            border: 2px solid {{color}};
            border-radius: 12px;
            font-size: 14px;
            font-weight: bold;
        }}
        QPushButton:hover {{
            background: {{color}};
            color: white;
        }}
    """
}

def apply_comfortable_theme(widget):
    """تطبيق الثيم المريح للعين على الويدجت"""
    widget.setStyleSheet(COMFORTABLE_STYLES['main_window'])
