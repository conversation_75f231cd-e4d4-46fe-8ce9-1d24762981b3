# -*- mode: python ; coding: utf-8 -*-
import os

# تحديد مسار التطبيق
app_path = os.path.abspath('.')

a = Analysis(
    ['main.py'],
    pathex=[app_path],
    binaries=[],
    datas=[
        ('config', 'config'),
        ('assets', 'assets'),
        ('ui', 'ui'),
        ('database', 'database'),
        ('utils', 'utils'),
        ('icon.ico', '.'),
        ('icon.png', '.'),
        ('requirements.txt', '.'),
        ('version_info.txt', '.'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'PyQt5.QtPrintSupport',
        'sqlite3',
        'csv',
        'datetime',
        'json',
        'os',
        'sys',
        'hashlib',
        'random',
        'string',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'scipy',
    ],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='نظام_الطيب_للتجارة_والتوزيع',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico',
    version='version_info.txt',
)
