"""
مدير النظام المالي المتكامل
إدارة رأس المال، الخزينة، وحساب البنك
"""

import sqlite3
from datetime import datetime


class FinancialManager:
    """مدير النظام المالي"""
    
    def __init__(self, db_path):
        self.db_path = db_path
        self.create_tables()
    
    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        return sqlite3.connect(self.db_path)
    
    def create_tables(self):
        """إنشاء الجداول المطلوبة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول رأس المال
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS capital (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date_added TEXT NOT NULL,
                transaction_type TEXT NOT NULL,  -- إضافة / سحب
                amount REAL NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الخزينة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS cash_box (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_date TEXT NOT NULL,
                transaction_type TEXT NOT NULL,  -- إيراد / مصروف
                source TEXT NOT NULL,  -- مبيعات، مرتجع، مورد، تحويل بنكي، إلخ
                amount REAL NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول حساب البنك
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bank_account (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_date TEXT NOT NULL,
                transaction_type TEXT NOT NULL,  -- إيداع / سحب / تحويل داخلي
                source TEXT NOT NULL,  -- عميل، مورد، رأس مال، سحب للخزنة
                amount REAL NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    # ==================== رأس المال ====================
    
    def add_capital_transaction(self, transaction_type, amount, description=""):
        """إضافة معاملة رأس مال"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO capital (date_added, transaction_type, amount, description)
            VALUES (?, ?, ?, ?)
        ''', (
            datetime.now().strftime('%Y-%m-%d'),
            transaction_type,
            amount,
            description
        ))
        
        conn.commit()
        conn.close()
    
    def get_capital_balance(self):
        """الحصول على رصيد رأس المال الحالي"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT 
                SUM(CASE WHEN transaction_type = 'إضافة' THEN amount ELSE 0 END) -
                SUM(CASE WHEN transaction_type = 'سحب' THEN amount ELSE 0 END) as balance
            FROM capital
        ''')
        
        result = cursor.fetchone()
        conn.close()
        
        return result[0] if result[0] else 0.0
    
    def get_capital_transactions(self, start_date=None, end_date=None, transaction_type=None):
        """الحصول على معاملات رأس المال"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = "SELECT * FROM capital WHERE 1=1"
        params = []
        
        if start_date:
            query += " AND date_added >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND date_added <= ?"
            params.append(end_date)
        
        if transaction_type:
            query += " AND transaction_type = ?"
            params.append(transaction_type)
        
        query += " ORDER BY date_added DESC, id DESC"
        
        cursor.execute(query, params)
        results = cursor.fetchall()
        conn.close()
        
        return results
    
    # ==================== الخزينة ====================
    
    def add_cash_transaction(self, transaction_type, source, amount, description=""):
        """إضافة معاملة خزينة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO cash_box (transaction_date, transaction_type, source, amount, description)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            datetime.now().strftime('%Y-%m-%d'),
            transaction_type,
            source,
            amount,
            description
        ))
        
        conn.commit()
        conn.close()
    
    def get_cash_balance(self):
        """الحصول على رصيد الخزينة الحالي"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT 
                SUM(CASE WHEN transaction_type = 'إيراد' THEN amount ELSE 0 END) -
                SUM(CASE WHEN transaction_type = 'مصروف' THEN amount ELSE 0 END) as balance
            FROM cash_box
        ''')
        
        result = cursor.fetchone()
        conn.close()
        
        return result[0] if result[0] else 0.0
    
    def get_cash_transactions(self, start_date=None, end_date=None, transaction_type=None, source=None):
        """الحصول على معاملات الخزينة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = "SELECT * FROM cash_box WHERE 1=1"
        params = []
        
        if start_date:
            query += " AND transaction_date >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND transaction_date <= ?"
            params.append(end_date)
        
        if transaction_type:
            query += " AND transaction_type = ?"
            params.append(transaction_type)
        
        if source:
            query += " AND source = ?"
            params.append(source)
        
        query += " ORDER BY transaction_date DESC, id DESC"
        
        cursor.execute(query, params)
        results = cursor.fetchall()
        conn.close()
        
        return results
    
    # ==================== حساب البنك ====================
    
    def add_bank_transaction(self, transaction_type, source, amount, description=""):
        """إضافة معاملة بنكية"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO bank_account (transaction_date, transaction_type, source, amount, description)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            datetime.now().strftime('%Y-%m-%d'),
            transaction_type,
            source,
            amount,
            description
        ))
        
        conn.commit()
        conn.close()
    
    def get_bank_balance(self):
        """الحصول على رصيد البنك الحالي"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT 
                SUM(CASE WHEN transaction_type = 'إيداع' THEN amount ELSE 0 END) -
                SUM(CASE WHEN transaction_type = 'سحب' THEN amount ELSE 0 END) as balance
            FROM bank_account
        ''')
        
        result = cursor.fetchone()
        conn.close()
        
        return result[0] if result[0] else 0.0
    
    def get_bank_transactions(self, start_date=None, end_date=None, transaction_type=None, source=None):
        """الحصول على المعاملات البنكية"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = "SELECT * FROM bank_account WHERE 1=1"
        params = []
        
        if start_date:
            query += " AND transaction_date >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND transaction_date <= ?"
            params.append(end_date)
        
        if transaction_type:
            query += " AND transaction_type = ?"
            params.append(transaction_type)
        
        if source:
            query += " AND source = ?"
            params.append(source)
        
        query += " ORDER BY transaction_date DESC, id DESC"
        
        cursor.execute(query, params)
        results = cursor.fetchall()
        conn.close()
        
        return results
    
    # ==================== التحويلات المترابطة ====================
    
    def transfer_bank_to_cash(self, amount, description="تحويل من البنك للخزينة"):
        """تحويل من البنك للخزينة"""
        # سحب من البنك
        self.add_bank_transaction("سحب", "تحويل للخزينة", amount, description)
        # إيداع في الخزينة
        self.add_cash_transaction("إيراد", "تحويل بنكي", amount, description)
    
    def transfer_cash_to_bank(self, amount, description="تحويل من الخزينة للبنك"):
        """تحويل من الخزينة للبنك"""
        # سحب من الخزينة
        self.add_cash_transaction("مصروف", "تحويل للبنك", amount, description)
        # إيداع في البنك
        self.add_bank_transaction("إيداع", "تحويل من الخزينة", amount, description)
    
    # ==================== التقارير الموحدة ====================
    
    def get_financial_summary(self):
        """الحصول على ملخص مالي شامل"""
        return {
            'capital_balance': self.get_capital_balance(),
            'cash_balance': self.get_cash_balance(),
            'bank_balance': self.get_bank_balance(),
            'total_assets': self.get_capital_balance() + self.get_cash_balance() + self.get_bank_balance()
        }
