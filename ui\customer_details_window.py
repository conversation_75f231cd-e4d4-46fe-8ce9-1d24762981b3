"""
نافذة تفاصيل العميل - Customer Details Window
عرض تفصيلي شامل لجميع معاملات العميل
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QTabWidget, QLabel, QPushButton, QTableWidget, 
                             QTableWidgetItem, QGroupBox, QHeaderView, QFrame,
                             QScrollArea, QGridLayout, QTextEdit, QDateEdit,
                             QComboBox, QMessageBox, QSplitter)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QColor, QPalette
from datetime import datetime, timedelta
import sqlite3


class CustomerDetailsWindow(QMainWindow):
    """نافذة تفاصيل العميل الشاملة"""
    
    def __init__(self, db_manager, customer_id, customer_name, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.customer_id = customer_id
        self.customer_name = customer_name
        
        self.init_ui()
        self.load_customer_data()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"تفاصيل العميل - {self.customer_name}")
        self.setGeometry(50, 50, 1400, 800)
        
        # تطبيق التصميم
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTabWidget::pane {
                border: 2px solid #3498db;
                background-color: white;
                border-radius: 5px;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
                font-size: 14px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin: 10px;
                padding: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #2c3e50;
                font-size: 16px;
            }
        """)
        
        # إنشاء التبويبات
        self.tabs = QTabWidget()
        
        # تبويب الملخص العام
        self.summary_tab = self.create_summary_tab()
        self.tabs.addTab(self.summary_tab, "📊 الملخص العام")
        
        # تبويب الفواتير
        self.invoices_tab = self.create_invoices_tab()
        self.tabs.addTab(self.invoices_tab, "📋 الفواتير")
        
        # تبويب المدفوعات
        self.payments_tab = self.create_payments_tab()
        self.tabs.addTab(self.payments_tab, "💰 المدفوعات")
        
        # تبويب التحليلات
        self.analytics_tab = self.create_analytics_tab()
        self.tabs.addTab(self.analytics_tab, "📈 التحليلات")
        
        self.setCentralWidget(self.tabs)
        
        # شريط الحالة
        self.statusBar().showMessage(f"عرض تفاصيل العميل: {self.customer_name}")
    
    def create_summary_tab(self):
        """إنشاء تبويب الملخص العام"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # معلومات العميل الأساسية
        customer_info_group = QGroupBox("معلومات العميل")
        customer_info_layout = QGridLayout()
        
        # اسم العميل
        customer_info_layout.addWidget(QLabel("اسم العميل:"), 0, 0)
        self.customer_name_label = QLabel(self.customer_name)
        self.customer_name_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        customer_info_layout.addWidget(self.customer_name_label, 0, 1)
        
        # رقم العميل
        customer_info_layout.addWidget(QLabel("رقم العميل:"), 0, 2)
        self.customer_id_label = QLabel(str(self.customer_id))
        self.customer_id_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        customer_info_layout.addWidget(self.customer_id_label, 0, 3)
        
        # تاريخ أول معاملة
        customer_info_layout.addWidget(QLabel("أول معاملة:"), 1, 0)
        self.first_transaction_label = QLabel("---")
        customer_info_layout.addWidget(self.first_transaction_label, 1, 1)
        
        # تاريخ آخر معاملة
        customer_info_layout.addWidget(QLabel("آخر معاملة:"), 1, 2)
        self.last_transaction_label = QLabel("---")
        customer_info_layout.addWidget(self.last_transaction_label, 1, 3)
        
        customer_info_group.setLayout(customer_info_layout)
        layout.addWidget(customer_info_group)
        
        # الملخص المالي
        financial_summary_group = QGroupBox("الملخص المالي")
        financial_layout = QGridLayout()
        
        # إجمالي المشتريات
        self.total_purchases_card = self.create_summary_card("إجمالي المشتريات", "0.00 جنيه", "#3498db")
        financial_layout.addWidget(self.total_purchases_card, 0, 0)
        
        # إجمالي المدفوعات
        self.total_payments_card = self.create_summary_card("إجمالي المدفوعات", "0.00 جنيه", "#27ae60")
        financial_layout.addWidget(self.total_payments_card, 0, 1)
        
        # الرصيد الحالي
        self.current_balance_card = self.create_summary_card("الرصيد الحالي", "0.00 جنيه", "#e74c3c")
        financial_layout.addWidget(self.current_balance_card, 0, 2)
        
        # عدد الفواتير
        self.invoices_count_card = self.create_summary_card("عدد الفواتير", "0", "#9b59b6")
        financial_layout.addWidget(self.invoices_count_card, 1, 0)
        
        # متوسط الفاتورة
        self.avg_invoice_card = self.create_summary_card("متوسط الفاتورة", "0.00 جنيه", "#f39c12")
        financial_layout.addWidget(self.avg_invoice_card, 1, 1)
        
        # أكبر فاتورة
        self.max_invoice_card = self.create_summary_card("أكبر فاتورة", "0.00 جنيه", "#1abc9c")
        financial_layout.addWidget(self.max_invoice_card, 1, 2)
        
        financial_summary_group.setLayout(financial_layout)
        layout.addWidget(financial_summary_group)
        
        # الأنشطة الأخيرة
        recent_activity_group = QGroupBox("الأنشطة الأخيرة (آخر 10 معاملات)")
        recent_layout = QVBoxLayout()
        
        self.recent_activity_table = QTableWidget()
        self.recent_activity_table.setColumnCount(5)
        self.recent_activity_table.setHorizontalHeaderLabels([
            "التاريخ", "النوع", "رقم الفاتورة", "المبلغ", "طريقة الدفع"
        ])
        self.recent_activity_table.horizontalHeader().setStretchLastSection(True)
        self.recent_activity_table.setAlternatingRowColors(True)
        self.recent_activity_table.setMaximumHeight(300)
        
        recent_layout.addWidget(self.recent_activity_table)
        recent_activity_group.setLayout(recent_layout)
        layout.addWidget(recent_activity_group)
        
        widget.setLayout(layout)
        return widget
    
    def create_invoices_tab(self):
        """إنشاء تبويب الفواتير"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # فلاتر البحث
        filters_group = QGroupBox("فلاتر البحث")
        filters_layout = QHBoxLayout()
        
        # فلتر التاريخ
        filters_layout.addWidget(QLabel("من تاريخ:"))
        self.from_date = QDateEdit()
        self.from_date.setDate(QDate.currentDate().addMonths(-6))
        self.from_date.setCalendarPopup(True)
        filters_layout.addWidget(self.from_date)
        
        filters_layout.addWidget(QLabel("إلى تاريخ:"))
        self.to_date = QDateEdit()
        self.to_date.setDate(QDate.currentDate())
        self.to_date.setCalendarPopup(True)
        filters_layout.addWidget(self.to_date)
        
        # فلتر حالة الدفع
        filters_layout.addWidget(QLabel("حالة الدفع:"))
        self.payment_status_filter = QComboBox()
        self.payment_status_filter.addItems(["الكل", "مدفوعة", "غير مدفوعة", "مدفوعة جزئياً"])
        filters_layout.addWidget(self.payment_status_filter)
        
        # زر البحث
        search_btn = QPushButton("🔍 بحث")
        search_btn.clicked.connect(self.filter_invoices)
        filters_layout.addWidget(search_btn)
        
        # زر إعادة تعيين
        reset_btn = QPushButton("🔄 إعادة تعيين")
        reset_btn.clicked.connect(self.reset_filters)
        filters_layout.addWidget(reset_btn)
        
        filters_layout.addStretch()
        filters_group.setLayout(filters_layout)
        layout.addWidget(filters_group)
        
        # جدول الفواتير
        invoices_group = QGroupBox("فواتير العميل")
        invoices_layout = QVBoxLayout()
        
        self.invoices_table = QTableWidget()
        self.invoices_table.setColumnCount(8)
        self.invoices_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "التاريخ", "الإجمالي", "المدفوع", "المتبقي", 
            "طريقة الدفع", "الحالة", "الإجراءات"
        ])
        self.invoices_table.horizontalHeader().setStretchLastSection(True)
        self.invoices_table.setAlternatingRowColors(True)
        
        invoices_layout.addWidget(self.invoices_table)
        invoices_group.setLayout(invoices_layout)
        layout.addWidget(invoices_group)
        
        widget.setLayout(layout)
        return widget
    
    def create_payments_tab(self):
        """إنشاء تبويب المدفوعات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # ملخص المدفوعات
        payments_summary_group = QGroupBox("ملخص المدفوعات")
        payments_summary_layout = QGridLayout()
        
        # المدفوعات النقدية
        self.cash_payments_card = self.create_summary_card("المدفوعات النقدية", "0.00 جنيه", "#27ae60")
        payments_summary_layout.addWidget(self.cash_payments_card, 0, 0)
        
        # التحويلات البنكية
        self.bank_payments_card = self.create_summary_card("التحويلات البنكية", "0.00 جنيه", "#3498db")
        payments_summary_layout.addWidget(self.bank_payments_card, 0, 1)
        
        # المدفوعات الآجلة
        self.credit_payments_card = self.create_summary_card("المعاملات الآجلة", "0.00 جنيه", "#e74c3c")
        payments_summary_layout.addWidget(self.credit_payments_card, 0, 2)
        
        payments_summary_group.setLayout(payments_summary_layout)
        layout.addWidget(payments_summary_group)
        
        # تفاصيل المدفوعات
        payments_details_group = QGroupBox("تفاصيل المدفوعات")
        payments_details_layout = QVBoxLayout()
        
        self.payments_table = QTableWidget()
        self.payments_table.setColumnCount(6)
        self.payments_table.setHorizontalHeaderLabels([
            "التاريخ", "رقم الفاتورة", "المبلغ", "طريقة الدفع", "الحالة", "ملاحظات"
        ])
        self.payments_table.horizontalHeader().setStretchLastSection(True)
        self.payments_table.setAlternatingRowColors(True)
        
        payments_details_layout.addWidget(self.payments_table)
        payments_details_group.setLayout(payments_details_layout)
        layout.addWidget(payments_details_group)
        
        widget.setLayout(layout)
        return widget
    
    def create_analytics_tab(self):
        """إنشاء تبويب التحليلات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # تحليل الأداء
        performance_group = QGroupBox("تحليل أداء العميل")
        performance_layout = QVBoxLayout()
        
        self.performance_text = QTextEdit()
        self.performance_text.setMaximumHeight(200)
        self.performance_text.setReadOnly(True)
        performance_layout.addWidget(self.performance_text)
        
        performance_group.setLayout(performance_layout)
        layout.addWidget(performance_group)
        
        # الاتجاهات الشهرية
        monthly_trends_group = QGroupBox("الاتجاهات الشهرية")
        monthly_layout = QVBoxLayout()
        
        self.monthly_table = QTableWidget()
        self.monthly_table.setColumnCount(5)
        self.monthly_table.setHorizontalHeaderLabels([
            "الشهر", "عدد الفواتير", "إجمالي المشتريات", "إجمالي المدفوعات", "الرصيد"
        ])
        self.monthly_table.horizontalHeader().setStretchLastSection(True)
        self.monthly_table.setAlternatingRowColors(True)
        
        monthly_layout.addWidget(self.monthly_table)
        monthly_trends_group.setLayout(monthly_layout)
        layout.addWidget(monthly_trends_group)
        
        widget.setLayout(layout)
        return widget
    
    def create_summary_card(self, title, value, color):
        """إنشاء بطاقة ملخص"""
        frame = QFrame()
        frame.setStyleSheet(f"""
            QFrame {{
                border: 2px solid {color};
                border-radius: 8px;
                background-color: white;
                padding: 10px;
                min-height: 80px;
            }}
        """)
        
        layout = QVBoxLayout()
        
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setStyleSheet(f"color: {color}; margin-bottom: 5px;")
        layout.addWidget(title_label)
        
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setFont(QFont("Arial", 16, QFont.Bold))
        value_label.setStyleSheet("color: #2c3e50; margin-top: 5px;")
        layout.addWidget(value_label)
        
        frame.setLayout(layout)
        frame.value_label = value_label  # حفظ مرجع للتحديث
        
        return frame

    def load_customer_data(self):
        """تحميل جميع بيانات العميل"""
        try:
            self.load_summary_data()
            self.load_invoices_data()
            self.load_payments_data()
            self.load_analytics_data()

        except Exception as e:
            print(f"خطأ في تحميل بيانات العميل: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل بيانات العميل:\n{str(e)}")

    def load_summary_data(self):
        """تحميل بيانات الملخص العام"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # إجمالي المشتريات
            cursor.execute("""
                SELECT
                    COUNT(*) as invoice_count,
                    SUM(final_amount) as total_purchases,
                    SUM(paid_amount) as total_payments,
                    SUM(remaining_amount) as current_balance,
                    AVG(final_amount) as avg_invoice,
                    MAX(final_amount) as max_invoice,
                    MIN(invoice_date) as first_transaction,
                    MAX(invoice_date) as last_transaction
                FROM invoices
                WHERE customer_id = ? AND invoice_type = 'sale'
            """, (self.customer_id,))

            result = cursor.fetchone()

            if result and result[0] > 0:
                invoice_count, total_purchases, total_payments, current_balance, avg_invoice, max_invoice, first_transaction, last_transaction = result

                # تحديث البطاقات
                self.total_purchases_card.value_label.setText(f"{total_purchases or 0:,.2f} جنيه")
                self.total_payments_card.value_label.setText(f"{total_payments or 0:,.2f} جنيه")
                self.current_balance_card.value_label.setText(f"{current_balance or 0:,.2f} جنيه")
                self.invoices_count_card.value_label.setText(str(invoice_count or 0))
                self.avg_invoice_card.value_label.setText(f"{avg_invoice or 0:,.2f} جنيه")
                self.max_invoice_card.value_label.setText(f"{max_invoice or 0:,.2f} جنيه")

                # تحديث التواريخ
                if first_transaction:
                    self.first_transaction_label.setText(first_transaction)
                if last_transaction:
                    self.last_transaction_label.setText(last_transaction)

                # تلوين بطاقة الرصيد حسب الحالة
                if current_balance and current_balance > 0:
                    self.current_balance_card.setStyleSheet("""
                        QFrame {
                            border: 2px solid #e74c3c;
                            border-radius: 8px;
                            background-color: #ffebee;
                            padding: 10px;
                            min-height: 80px;
                        }
                    """)
                else:
                    self.current_balance_card.setStyleSheet("""
                        QFrame {
                            border: 2px solid #27ae60;
                            border-radius: 8px;
                            background-color: #e8f5e8;
                            padding: 10px;
                            min-height: 80px;
                        }
                    """)

            # تحميل الأنشطة الأخيرة
            cursor.execute("""
                SELECT
                    invoice_date,
                    'فاتورة مبيعات' as type,
                    invoice_number,
                    final_amount,
                    payment_method
                FROM invoices
                WHERE customer_id = ? AND invoice_type = 'sale'
                ORDER BY invoice_date DESC, id DESC
                LIMIT 10
            """, (self.customer_id,))

            recent_activities = cursor.fetchall()
            self.recent_activity_table.setRowCount(len(recent_activities))

            for row, activity in enumerate(recent_activities):
                date, type_name, invoice_number, amount, payment_method = activity

                self.recent_activity_table.setItem(row, 0, QTableWidgetItem(str(date)))
                self.recent_activity_table.setItem(row, 1, QTableWidgetItem(type_name))
                self.recent_activity_table.setItem(row, 2, QTableWidgetItem(str(invoice_number)))
                self.recent_activity_table.setItem(row, 3, QTableWidgetItem(f"{amount:,.2f} جنيه"))

                # ترجمة طريقة الدفع
                payment_methods = {
                    'cash': 'نقداً',
                    'credit': 'آجل',
                    'bank_transfer': 'تحويل بنكي'
                }
                payment_text = payment_methods.get(payment_method, payment_method)
                self.recent_activity_table.setItem(row, 4, QTableWidgetItem(payment_text))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات الملخص: {e}")

    def load_invoices_data(self):
        """تحميل بيانات الفواتير"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT
                    invoice_number,
                    invoice_date,
                    final_amount,
                    paid_amount,
                    remaining_amount,
                    payment_method,
                    CASE
                        WHEN remaining_amount = 0 THEN 'مدفوعة'
                        WHEN paid_amount = 0 THEN 'غير مدفوعة'
                        ELSE 'مدفوعة جزئياً'
                    END as status
                FROM invoices
                WHERE customer_id = ? AND invoice_type = 'sale'
                ORDER BY invoice_date DESC, id DESC
            """, (self.customer_id,))

            invoices = cursor.fetchall()
            self.invoices_table.setRowCount(len(invoices))

            for row, invoice in enumerate(invoices):
                invoice_number, date, total, paid, remaining, payment_method, status = invoice

                self.invoices_table.setItem(row, 0, QTableWidgetItem(str(invoice_number)))
                self.invoices_table.setItem(row, 1, QTableWidgetItem(str(date)))
                self.invoices_table.setItem(row, 2, QTableWidgetItem(f"{total:,.2f}"))
                self.invoices_table.setItem(row, 3, QTableWidgetItem(f"{paid:,.2f}"))
                self.invoices_table.setItem(row, 4, QTableWidgetItem(f"{remaining:,.2f}"))

                # ترجمة طريقة الدفع
                payment_methods = {
                    'cash': 'نقداً',
                    'credit': 'آجل',
                    'bank_transfer': 'تحويل بنكي'
                }
                payment_text = payment_methods.get(payment_method, payment_method)
                self.invoices_table.setItem(row, 5, QTableWidgetItem(payment_text))

                # تلوين الحالة
                status_item = QTableWidgetItem(status)
                if status == 'مدفوعة':
                    status_item.setBackground(QColor(46, 204, 113))
                    status_item.setForeground(QColor(255, 255, 255))
                elif status == 'غير مدفوعة':
                    status_item.setBackground(QColor(231, 76, 60))
                    status_item.setForeground(QColor(255, 255, 255))
                else:  # مدفوعة جزئياً
                    status_item.setBackground(QColor(243, 156, 18))
                    status_item.setForeground(QColor(255, 255, 255))

                self.invoices_table.setItem(row, 6, status_item)

                # زر الإجراءات
                actions_btn = QPushButton("📋 تفاصيل")
                actions_btn.clicked.connect(lambda checked, inv_num=invoice_number: self.show_invoice_details(inv_num))
                self.invoices_table.setCellWidget(row, 7, actions_btn)

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات الفواتير: {e}")

    def load_payments_data(self):
        """تحميل بيانات المدفوعات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # ملخص المدفوعات حسب النوع
            cursor.execute("""
                SELECT
                    payment_method,
                    SUM(paid_amount) as total_paid
                FROM invoices
                WHERE customer_id = ? AND invoice_type = 'sale' AND paid_amount > 0
                GROUP BY payment_method
            """, (self.customer_id,))

            payment_summary = cursor.fetchall()

            cash_total = 0
            bank_total = 0
            credit_total = 0

            for payment_method, total_paid in payment_summary:
                if payment_method == 'cash':
                    cash_total = total_paid
                elif payment_method == 'bank_transfer':
                    bank_total = total_paid
                elif payment_method == 'credit':
                    credit_total = total_paid

            # تحديث بطاقات المدفوعات
            self.cash_payments_card.value_label.setText(f"{cash_total:,.2f} جنيه")
            self.bank_payments_card.value_label.setText(f"{bank_total:,.2f} جنيه")

            # حساب المعاملات الآجلة (المتبقية)
            cursor.execute("""
                SELECT SUM(remaining_amount)
                FROM invoices
                WHERE customer_id = ? AND invoice_type = 'sale' AND remaining_amount > 0
            """, (self.customer_id,))

            credit_remaining = cursor.fetchone()[0] or 0
            self.credit_payments_card.value_label.setText(f"{credit_remaining:,.2f} جنيه")

            # تفاصيل المدفوعات
            cursor.execute("""
                SELECT
                    invoice_date,
                    invoice_number,
                    paid_amount,
                    payment_method,
                    CASE
                        WHEN remaining_amount = 0 THEN 'مدفوعة بالكامل'
                        WHEN paid_amount = 0 THEN 'غير مدفوعة'
                        ELSE 'مدفوعة جزئياً'
                    END as status,
                    CASE
                        WHEN payment_method = 'cash' THEN 'دفع نقدي فوري'
                        WHEN payment_method = 'bank_transfer' THEN 'تحويل بنكي'
                        WHEN payment_method = 'credit' THEN 'معاملة آجلة'
                        ELSE 'أخرى'
                    END as notes
                FROM invoices
                WHERE customer_id = ? AND invoice_type = 'sale'
                ORDER BY invoice_date DESC
            """, (self.customer_id,))

            payments = cursor.fetchall()
            self.payments_table.setRowCount(len(payments))

            for row, payment in enumerate(payments):
                date, invoice_number, paid_amount, payment_method, status, notes = payment

                self.payments_table.setItem(row, 0, QTableWidgetItem(str(date)))
                self.payments_table.setItem(row, 1, QTableWidgetItem(str(invoice_number)))
                self.payments_table.setItem(row, 2, QTableWidgetItem(f"{paid_amount:,.2f}"))

                # ترجمة طريقة الدفع
                payment_methods = {
                    'cash': 'نقداً',
                    'credit': 'آجل',
                    'bank_transfer': 'تحويل بنكي'
                }
                payment_text = payment_methods.get(payment_method, payment_method)
                self.payments_table.setItem(row, 3, QTableWidgetItem(payment_text))

                self.payments_table.setItem(row, 4, QTableWidgetItem(status))
                self.payments_table.setItem(row, 5, QTableWidgetItem(notes))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات المدفوعات: {e}")

    def load_analytics_data(self):
        """تحميل بيانات التحليلات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # تحليل الأداء
            analysis_text = []

            # معدل الشراء الشهري
            cursor.execute("""
                SELECT
                    COUNT(*) as total_invoices,
                    AVG(final_amount) as avg_amount,
                    SUM(final_amount) as total_amount,
                    MIN(invoice_date) as first_date,
                    MAX(invoice_date) as last_date
                FROM invoices
                WHERE customer_id = ? AND invoice_type = 'sale'
            """, (self.customer_id,))

            result = cursor.fetchone()
            if result and result[0] > 0:
                total_invoices, avg_amount, total_amount, first_date, last_date = result

                # حساب عدد الأشهر
                if first_date and last_date:
                    from datetime import datetime
                    first = datetime.strptime(first_date, '%Y-%m-%d')
                    last = datetime.strptime(last_date, '%Y-%m-%d')
                    months_diff = (last.year - first.year) * 12 + (last.month - first.month) + 1

                    monthly_avg_invoices = total_invoices / months_diff if months_diff > 0 else 0
                    monthly_avg_amount = total_amount / months_diff if months_diff > 0 else 0

                    analysis_text.append(f"📊 تحليل الأداء العام:")
                    analysis_text.append(f"• فترة التعامل: {months_diff} شهر")
                    analysis_text.append(f"• متوسط الفواتير الشهرية: {monthly_avg_invoices:.1f} فاتورة")
                    analysis_text.append(f"• متوسط المشتريات الشهرية: {monthly_avg_amount:,.2f} جنيه")
                    analysis_text.append(f"• متوسط قيمة الفاتورة: {avg_amount:,.2f} جنيه")

            # تحليل طرق الدفع المفضلة
            cursor.execute("""
                SELECT
                    payment_method,
                    COUNT(*) as count,
                    SUM(final_amount) as total
                FROM invoices
                WHERE customer_id = ? AND invoice_type = 'sale'
                GROUP BY payment_method
                ORDER BY count DESC
            """, (self.customer_id,))

            payment_preferences = cursor.fetchall()
            if payment_preferences:
                analysis_text.append(f"\n💳 تفضيلات طرق الدفع:")
                for payment_method, count, total in payment_preferences:
                    payment_names = {
                        'cash': 'النقدي',
                        'credit': 'الآجل',
                        'bank_transfer': 'التحويل البنكي'
                    }
                    method_name = payment_names.get(payment_method, payment_method)
                    percentage = (count / sum(p[1] for p in payment_preferences)) * 100
                    analysis_text.append(f"• {method_name}: {count} فاتورة ({percentage:.1f}%) - {total:,.2f} جنيه")

            # تحليل الاتجاه
            cursor.execute("""
                SELECT
                    strftime('%Y-%m', invoice_date) as month,
                    SUM(final_amount) as monthly_total
                FROM invoices
                WHERE customer_id = ? AND invoice_type = 'sale'
                AND invoice_date >= date('now', '-6 months')
                GROUP BY month
                ORDER BY month
            """, (self.customer_id,))

            monthly_trends = cursor.fetchall()
            if len(monthly_trends) >= 2:
                first_month = monthly_trends[0][1]
                last_month = monthly_trends[-1][1]
                trend = "صاعد 📈" if last_month > first_month else "هابط 📉"
                analysis_text.append(f"\n📈 اتجاه المشتريات (آخر 6 أشهر): {trend}")

            self.performance_text.setText("\n".join(analysis_text))

            # الاتجاهات الشهرية
            cursor.execute("""
                SELECT
                    strftime('%Y-%m', invoice_date) as month,
                    COUNT(*) as invoice_count,
                    SUM(final_amount) as total_purchases,
                    SUM(paid_amount) as total_payments,
                    SUM(remaining_amount) as balance
                FROM invoices
                WHERE customer_id = ? AND invoice_type = 'sale'
                GROUP BY month
                ORDER BY month DESC
                LIMIT 12
            """, (self.customer_id,))

            monthly_data = cursor.fetchall()
            self.monthly_table.setRowCount(len(monthly_data))

            for row, data in enumerate(monthly_data):
                month, invoice_count, total_purchases, total_payments, balance = data

                self.monthly_table.setItem(row, 0, QTableWidgetItem(month))
                self.monthly_table.setItem(row, 1, QTableWidgetItem(str(invoice_count)))
                self.monthly_table.setItem(row, 2, QTableWidgetItem(f"{total_purchases:,.2f}"))
                self.monthly_table.setItem(row, 3, QTableWidgetItem(f"{total_payments:,.2f}"))
                self.monthly_table.setItem(row, 4, QTableWidgetItem(f"{balance:,.2f}"))

            conn.close()

        except Exception as e:
            print(f"خطأ في تحميل بيانات التحليلات: {e}")

    def filter_invoices(self):
        """فلترة الفواتير حسب المعايير المحددة"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            from_date = self.from_date.date().toString('yyyy-MM-dd')
            to_date = self.to_date.date().toString('yyyy-MM-dd')
            status_filter = self.payment_status_filter.currentText()

            # بناء الاستعلام
            query = """
                SELECT
                    invoice_number,
                    invoice_date,
                    final_amount,
                    paid_amount,
                    remaining_amount,
                    payment_method,
                    CASE
                        WHEN remaining_amount = 0 THEN 'مدفوعة'
                        WHEN paid_amount = 0 THEN 'غير مدفوعة'
                        ELSE 'مدفوعة جزئياً'
                    END as status
                FROM invoices
                WHERE customer_id = ? AND invoice_type = 'sale'
                AND DATE(invoice_date) BETWEEN ? AND ?
            """

            params = [self.customer_id, from_date, to_date]

            if status_filter != "الكل":
                if status_filter == "مدفوعة":
                    query += " AND remaining_amount = 0"
                elif status_filter == "غير مدفوعة":
                    query += " AND paid_amount = 0"
                elif status_filter == "مدفوعة جزئياً":
                    query += " AND paid_amount > 0 AND remaining_amount > 0"

            query += " ORDER BY invoice_date DESC"

            cursor.execute(query, params)
            invoices = cursor.fetchall()

            # تحديث الجدول
            self.invoices_table.setRowCount(len(invoices))

            for row, invoice in enumerate(invoices):
                invoice_number, date, total, paid, remaining, payment_method, status = invoice

                self.invoices_table.setItem(row, 0, QTableWidgetItem(str(invoice_number)))
                self.invoices_table.setItem(row, 1, QTableWidgetItem(str(date)))
                self.invoices_table.setItem(row, 2, QTableWidgetItem(f"{total:,.2f}"))
                self.invoices_table.setItem(row, 3, QTableWidgetItem(f"{paid:,.2f}"))
                self.invoices_table.setItem(row, 4, QTableWidgetItem(f"{remaining:,.2f}"))

                # ترجمة طريقة الدفع
                payment_methods = {
                    'cash': 'نقداً',
                    'credit': 'آجل',
                    'bank_transfer': 'تحويل بنكي'
                }
                payment_text = payment_methods.get(payment_method, payment_method)
                self.invoices_table.setItem(row, 5, QTableWidgetItem(payment_text))

                # تلوين الحالة
                status_item = QTableWidgetItem(status)
                if status == 'مدفوعة':
                    status_item.setBackground(QColor(46, 204, 113))
                    status_item.setForeground(QColor(255, 255, 255))
                elif status == 'غير مدفوعة':
                    status_item.setBackground(QColor(231, 76, 60))
                    status_item.setForeground(QColor(255, 255, 255))
                else:  # مدفوعة جزئياً
                    status_item.setBackground(QColor(243, 156, 18))
                    status_item.setForeground(QColor(255, 255, 255))

                self.invoices_table.setItem(row, 6, status_item)

                # زر الإجراءات
                actions_btn = QPushButton("📋 تفاصيل")
                actions_btn.clicked.connect(lambda checked, inv_num=invoice_number: self.show_invoice_details(inv_num))
                self.invoices_table.setCellWidget(row, 7, actions_btn)

            conn.close()
            self.statusBar().showMessage(f"تم العثور على {len(invoices)} فاتورة")

        except Exception as e:
            print(f"خطأ في فلترة الفواتير: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في فلترة الفواتير:\n{str(e)}")

    def reset_filters(self):
        """إعادة تعيين الفلاتر"""
        self.from_date.setDate(QDate.currentDate().addMonths(-6))
        self.to_date.setDate(QDate.currentDate())
        self.payment_status_filter.setCurrentIndex(0)
        self.load_invoices_data()

    def show_invoice_details(self, invoice_number):
        """عرض تفاصيل الفاتورة"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # الحصول على تفاصيل الفاتورة
            cursor.execute("""
                SELECT
                    i.invoice_number,
                    i.invoice_date,
                    i.total_amount,
                    i.discount_amount,
                    i.final_amount,
                    i.paid_amount,
                    i.remaining_amount,
                    i.payment_method,
                    c.name as customer_name
                FROM invoices i
                JOIN customers c ON i.customer_id = c.id
                WHERE i.invoice_number = ? AND i.customer_id = ?
            """, (invoice_number, self.customer_id))

            invoice_data = cursor.fetchone()

            if not invoice_data:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة")
                return

            # الحصول على أصناف الفاتورة
            cursor.execute("""
                SELECT
                    ii.product_id,
                    p.name as product_name,
                    ii.quantity,
                    ii.unit_price,
                    ii.total_price
                FROM invoice_items ii
                JOIN products p ON ii.product_id = p.id
                JOIN invoices i ON ii.invoice_id = i.id
                WHERE i.invoice_number = ? AND i.customer_id = ?
                ORDER BY ii.id
            """, (invoice_number, self.customer_id))

            items_data = cursor.fetchall()

            # إنشاء نص التفاصيل
            inv_num, inv_date, total_amt, discount_amt, final_amt, paid_amt, remaining_amt, payment_method, customer_name = invoice_data

            # تنظيف رقم الفاتورة إذا كان طويلاً جداً
            if len(str(inv_num)) > 20:
                inv_num = str(inv_num)[-10:]  # أخذ آخر 10 أرقام فقط

            # ترجمة طريقة الدفع
            payment_methods = {
                'cash': 'نقداً',
                'credit': 'آجل',
                'bank_transfer': 'تحويل بنكي'
            }
            payment_text = payment_methods.get(payment_method, payment_method)

            details = f"""
📋 تفاصيل الفاتورة رقم: {inv_num}

👤 العميل: {customer_name}
📅 التاريخ: {inv_date}
💰 طريقة الدفع: {payment_text}

💵 المبالغ:
• الإجمالي قبل الخصم: {total_amt:,.2f} جنيه
• الخصم: {discount_amt:,.2f} جنيه
• الإجمالي بعد الخصم: {final_amt:,.2f} جنيه
• المبلغ المدفوع: {paid_amt:,.2f} جنيه
• المبلغ المتبقي: {remaining_amt:,.2f} جنيه

📦 الأصناف:
"""

            if items_data:
                for item in items_data:
                    product_id, product_name, quantity, unit_price, total_price = item
                    details += f"• {product_name}: {quantity} × {unit_price:,.2f} = {total_price:,.2f} جنيه\n"
            else:
                details += "لا توجد أصناف مسجلة لهذه الفاتورة"

            # عرض التفاصيل
            msg = QMessageBox(self)
            msg.setWindowTitle("تفاصيل الفاتورة")
            msg.setText(details)
            msg.setIcon(QMessageBox.Information)
            msg.exec_()

            conn.close()

        except Exception as e:
            print(f"خطأ في عرض تفاصيل الفاتورة: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عرض تفاصيل الفاتورة:\n{str(e)}")
