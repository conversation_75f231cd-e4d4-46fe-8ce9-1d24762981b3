#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء أيقونة للتطبيق
Create Application Icon
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_pos_icon():
    """إنشاء أيقونة نظام نقاط البيع"""
    
    # إنشاء صورة 256x256 (أفضل دقة للأيقونات)
    size = 256
    image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(image)
    
    # خلفية دائرية خضراء
    margin = 20
    circle_size = size - (margin * 2)
    draw.ellipse([margin, margin, margin + circle_size, margin + circle_size], 
                 fill=(76, 175, 80, 255), outline=(56, 142, 60, 255), width=4)
    
    # رمز نقاط البيع في الوسط
    try:
        # محاولة استخدام خط عربي إذا كان متوفراً
        font_size = 80
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        # استخدام الخط الافتراضي
        font = ImageFont.load_default()
    
    # رسم رمز POS
    text = "POS"
    # حساب موقع النص في الوسط
    text_bbox = draw.textbbox((0, 0), text, font=font)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    text_x = (size - text_width) // 2
    text_y = (size - text_height) // 2 - 10
    
    # رسم النص بلون أبيض
    draw.text((text_x, text_y), text, fill=(255, 255, 255, 255), font=font)
    
    # رسم رمز صغير للمال
    money_y = text_y + text_height + 10
    draw.text((size//2 - 20, money_y), "$", fill=(255, 255, 255, 255), font=font)
    
    # حفظ الأيقونة بصيغة ICO
    # تحويل إلى RGB أولاً ثم إلى ICO
    icon_image = Image.new('RGB', (size, size), (255, 255, 255))
    icon_image.paste(image, (0, 0), image)
    
    # إنشاء أحجام متعددة للأيقونة
    icon_sizes = [16, 24, 32, 48, 64, 128, 256]
    icons = []
    
    for icon_size in icon_sizes:
        resized = icon_image.resize((icon_size, icon_size), Image.Resampling.LANCZOS)
        icons.append(resized)
    
    # حفظ الأيقونة
    icons[0].save('icon.ico', format='ICO', sizes=[(size, size) for size in icon_sizes])
    
    print("Icon created successfully: icon.ico")
    
    # إنشاء أيقونة PNG أيضاً
    image.save('icon.png', 'PNG')
    print("PNG icon created: icon.png")

if __name__ == '__main__':
    create_pos_icon()