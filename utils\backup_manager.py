"""
مدير النسخ الاحتياطي التلقائي - Automatic Backup Manager
نسخ احتياطي تلقائي لقاعدة البيانات والملفات المهمة
"""

import os
import shutil
import sqlite3
import zipfile
import time
import threading
from datetime import datetime, timedelta
import json
import logging

# محاولة استيراد schedule، وإذا لم تكن متاحة نستخدم بديل بسيط
try:
    import schedule
except ImportError:
    # بديل بسيط لمكتبة schedule
    class SimpleScheduler:
        def __init__(self):
            self.jobs = []

        def every(self, interval):
            return ScheduleJob(interval, self)

        def run_pending(self):
            current_time = time.time()
            for job in self.jobs:
                if current_time >= job.next_run:
                    job.run()
                    job.next_run = current_time + job.interval

    class ScheduleJob:
        def __init__(self, interval, scheduler):
            self.interval = interval * 3600  # تحويل الساعات إلى ثواني
            self.scheduler = scheduler
            self.job_func = None
            self.next_run = time.time() + self.interval

        @property
        def hours(self):
            return self

        def do(self, job_func):
            self.job_func = job_func
            self.scheduler.jobs.append(self)
            return self

        def run(self):
            if self.job_func:
                self.job_func()

    schedule = SimpleScheduler()


class BackupManager:
    """مدير النسخ الاحتياطي"""
    
    def __init__(self, db_path="database/business_system.db"):
        try:
            self.db_path = db_path
            self.backup_dir = "backups"
            self.config_file = "backup_config.json"
            self.max_backups = 30  # الحد الأقصى للنسخ المحفوظة

            # إنشاء مجلد النسخ الاحتياطي
            os.makedirs(self.backup_dir, exist_ok=True)

            # إعداد نظام السجلات
            self.setup_logging()

            # تحميل الإعدادات
            self.load_config()

            # بدء النسخ التلقائي (بشكل آمن)
            try:
                self.start_auto_backup()
            except Exception as e:
                print(f"تحذير: لم يتم بدء النسخ التلقائي: {e}")

        except Exception as e:
            print(f"خطأ في تهيئة مدير النسخ الاحتياطي: {e}")
            # إعداد قيم افتراضية آمنة
            self.backup_dir = "backups"
            self.config_file = "backup_config.json"
            self.max_backups = 30
            self.config = {
                "auto_backup_enabled": True,
                "backup_interval_hours": 6,
                "backup_on_startup": False,  # تعطيل النسخ عند البدء لتجنب المشاكل
                "backup_on_shutdown": False,
                "max_backups": 30,
                "compress_backups": True,
                "backup_database": True,
                "backup_reports": True,
                "backup_config": True
            }
    
    def setup_logging(self):
        """إعداد نظام السجلات"""
        try:
            log_file = os.path.join(self.backup_dir, 'backup.log')
            logging.basicConfig(
                filename=log_file,
                level=logging.INFO,
                format='%(asctime)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            self.logger = logging.getLogger(__name__)
        except Exception as e:
            print(f"تحذير: لم يتم إعداد نظام السجلات: {e}")
            # إنشاء logger بسيط للطباعة
            class SimpleLogger:
                def info(self, msg): print(f"INFO: {msg}")
                def error(self, msg): print(f"ERROR: {msg}")
                def warning(self, msg): print(f"WARNING: {msg}")
            self.logger = SimpleLogger()
    
    def load_config(self):
        """تحميل إعدادات النسخ الاحتياطي"""
        default_config = {
            "auto_backup_enabled": True,
            "backup_interval_hours": 6,  # كل 6 ساعات
            "backup_on_startup": True,
            "backup_on_shutdown": True,
            "max_backups": 30,
            "compress_backups": True,
            "backup_database": True,
            "backup_reports": True,
            "backup_config": True
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = default_config
                self.save_config()
        except Exception as e:
            self.logger.error(f"خطأ في تحميل إعدادات النسخ الاحتياطي: {e}")
            self.config = default_config
    
    def save_config(self):
        """حفظ إعدادات النسخ الاحتياطي"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"خطأ في حفظ إعدادات النسخ الاحتياطي: {e}")
    
    def create_backup(self, backup_type="manual"):
        """إنشاء نسخة احتياطية مبسطة"""
        try:
            print(f"بدء إنشاء نسخة احتياطية من نوع: {backup_type}")

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{backup_type}_{timestamp}"

            # إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
            os.makedirs(self.backup_dir, exist_ok=True)

            backup_info = {
                "timestamp": timestamp,
                "type": backup_type,
                "files": [],
                "size": 0,
                "status": "in_progress"
            }

            # نسخ قاعدة البيانات فقط (الأهم)
            backup_files = []
            if os.path.exists(self.db_path):
                backup_db_path = os.path.join(self.backup_dir, f"{backup_name}_database.db")
                shutil.copy2(self.db_path, backup_db_path)
                backup_files.append(backup_db_path)
                print(f"تم نسخ قاعدة البيانات إلى: {backup_db_path}")

            # ضغط الملفات إذا كان مفعلاً
            if self.config.get("compress_backups", True) and backup_files:
                zip_path = os.path.join(self.backup_dir, f"{backup_name}.zip")
                try:
                    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                        for file_path in backup_files:
                            zipf.write(file_path, os.path.basename(file_path))

                    # حذف الملفات غير المضغوطة
                    for file_path in backup_files:
                        if os.path.exists(file_path):
                            os.remove(file_path)

                    backup_path = zip_path
                    print(f"تم ضغط النسخة الاحتياطية: {zip_path}")

                except Exception as e:
                    print(f"خطأ في الضغط: {e}")
                    backup_path = backup_files[0] if backup_files else None
            else:
                backup_path = backup_files[0] if backup_files else None

            if backup_path and os.path.exists(backup_path):
                # حساب حجم النسخة الاحتياطية
                backup_info["size"] = os.path.getsize(backup_path)
                backup_info["status"] = "completed"
                backup_info["path"] = backup_path
                backup_info["files"] = [backup_path]

                # حفظ معلومات النسخة الاحتياطية
                try:
                    info_file = os.path.join(self.backup_dir, f"{backup_name}_info.json")
                    with open(info_file, 'w', encoding='utf-8') as f:
                        json.dump(backup_info, f, ensure_ascii=False, indent=2)
                except Exception as e:
                    print(f"تحذير: لم يتم حفظ معلومات النسخة الاحتياطية: {e}")

                # تنظيف النسخ القديمة
                try:
                    self.cleanup_old_backups()
                except Exception as e:
                    print(f"تحذير: لم يتم تنظيف النسخ القديمة: {e}")

                print(f"تم إنشاء نسخة احتياطية بنجاح: {backup_name}")
                self.logger.info(f"تم إنشاء نسخة احتياطية بنجاح: {backup_name}")
                return backup_path
            else:
                print("فشل في إنشاء النسخة الاحتياطية")
                return None

        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            self.logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return None
    
    def backup_database(self, backup_path):
        """نسخ قاعدة البيانات"""
        try:
            if not os.path.exists(self.db_path):
                self.logger.warning("قاعدة البيانات غير موجودة")
                return None
            
            # نسخ ملف قاعدة البيانات الرئيسي
            db_backup_file = os.path.join(backup_path, "business_system.db")
            shutil.copy2(self.db_path, db_backup_file)
            
            # إنشاء نسخة SQL للاستيراد
            sql_backup_file = os.path.join(backup_path, "database_dump.sql")
            self.export_database_to_sql(sql_backup_file)
            
            return db_backup_file
            
        except Exception as e:
            self.logger.error(f"خطأ في نسخ قاعدة البيانات: {e}")
            return None
    
    def export_database_to_sql(self, sql_file):
        """تصدير قاعدة البيانات إلى ملف SQL"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            with open(sql_file, 'w', encoding='utf-8') as f:
                for line in conn.iterdump():
                    f.write(f"{line}\n")
            
            conn.close()
            
        except Exception as e:
            self.logger.error(f"خطأ في تصدير قاعدة البيانات: {e}")
    
    def backup_config_files(self, backup_path):
        """نسخ ملفات الإعدادات"""
        try:
            config_backup_path = os.path.join(backup_path, "config")
            os.makedirs(config_backup_path, exist_ok=True)
            
            config_files = []
            
            # نسخ ملفات الإعدادات
            config_patterns = ["*.json", "*.ini", "*.cfg", "config.py"]
            
            for pattern in config_patterns:
                import glob
                for file_path in glob.glob(pattern):
                    if os.path.isfile(file_path):
                        dest_path = os.path.join(config_backup_path, os.path.basename(file_path))
                        shutil.copy2(file_path, dest_path)
                        config_files.append(dest_path)
            
            # نسخ مجلد utils إذا كان موجوداً
            if os.path.exists("utils"):
                utils_backup = os.path.join(config_backup_path, "utils")
                shutil.copytree("utils", utils_backup, ignore=shutil.ignore_patterns('__pycache__'))
                config_files.append(utils_backup)
            
            return config_files
            
        except Exception as e:
            self.logger.error(f"خطأ في نسخ ملفات الإعدادات: {e}")
            return []
    
    def backup_reports(self, backup_path):
        """نسخ التقارير المحفوظة"""
        try:
            reports_files = []
            
            # نسخ مجلد التقارير إذا كان موجوداً
            if os.path.exists("reports"):
                reports_backup = os.path.join(backup_path, "reports")
                shutil.copytree("reports", reports_backup)
                reports_files.append(reports_backup)
            
            # نسخ ملفات السجلات
            if os.path.exists("logs"):
                logs_backup = os.path.join(backup_path, "logs")
                shutil.copytree("logs", logs_backup)
                reports_files.append(logs_backup)
            
            return reports_files
            
        except Exception as e:
            self.logger.error(f"خطأ في نسخ التقارير: {e}")
            return []
    
    def compress_backup(self, source_path, zip_path):
        """ضغط النسخة الاحتياطية"""
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(source_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_path = os.path.relpath(file_path, source_path)
                        zipf.write(file_path, arc_path)
            
        except Exception as e:
            self.logger.error(f"خطأ في ضغط النسخة الاحتياطية: {e}")
    
    def get_backup_size(self, backup_path):
        """حساب حجم النسخة الاحتياطية"""
        try:
            if os.path.isfile(backup_path):
                return os.path.getsize(backup_path)
            else:
                total_size = 0
                for dirpath, dirnames, filenames in os.walk(backup_path):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        total_size += os.path.getsize(filepath)
                return total_size
        except Exception as e:
            self.logger.error(f"خطأ في حساب حجم النسخة الاحتياطية: {e}")
            return 0
    
    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            # الحصول على قائمة النسخ الاحتياطية
            backups = []
            for file in os.listdir(self.backup_dir):
                if file.startswith("backup_") and (file.endswith(".zip") or os.path.isdir(os.path.join(self.backup_dir, file))):
                    file_path = os.path.join(self.backup_dir, file)
                    creation_time = os.path.getctime(file_path)
                    backups.append((file_path, creation_time))
            
            # ترتيب النسخ حسب التاريخ (الأحدث أولاً)
            backups.sort(key=lambda x: x[1], reverse=True)
            
            # حذف النسخ الزائدة
            max_backups = self.config.get("max_backups", 30)
            for backup_path, _ in backups[max_backups:]:
                try:
                    if os.path.isfile(backup_path):
                        os.remove(backup_path)
                    else:
                        shutil.rmtree(backup_path)
                    
                    # حذف ملف المعلومات المرتبط
                    info_file = backup_path.replace(".zip", "_info.json")
                    if os.path.exists(info_file):
                        os.remove(info_file)
                    
                    self.logger.info(f"تم حذف النسخة الاحتياطية القديمة: {os.path.basename(backup_path)}")
                    
                except Exception as e:
                    self.logger.error(f"خطأ في حذف النسخة الاحتياطية القديمة: {e}")
            
        except Exception as e:
            self.logger.error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {e}")
    
    def start_auto_backup(self):
        """بدء النسخ الاحتياطي التلقائي (مبسط)"""
        try:
            if not self.config.get("auto_backup_enabled", True):
                print("النسخ التلقائي معطل")
                return

            print("تم تفعيل النسخ الاحتياطي التلقائي")

            # نسخة احتياطية عند بدء التشغيل (إذا كان مفعلاً)
            if self.config.get("backup_on_startup", False):
                print("إنشاء نسخة احتياطية عند بدء التشغيل...")
                threading.Thread(target=lambda: self.create_backup("startup"), daemon=True).start()

        except Exception as e:
            print(f"خطأ في بدء النسخ التلقائي: {e}")
    
    def auto_backup_job(self):
        """مهمة النسخ الاحتياطي التلقائي"""
        self.create_backup("auto")
    
    def run_scheduler(self):
        """تشغيل جدولة المهام"""
        while True:
            schedule.run_pending()
            time.sleep(60)  # فحص كل دقيقة
    
    def create_shutdown_backup(self):
        """إنشاء نسخة احتياطية عند الإغلاق"""
        if self.config.get("backup_on_shutdown", True):
            self.create_backup("shutdown")
    
    def get_backup_list(self):
        """الحصول على قائمة النسخ الاحتياطية"""
        try:
            backups = []
            for file in os.listdir(self.backup_dir):
                if file.endswith("_info.json"):
                    info_path = os.path.join(self.backup_dir, file)
                    try:
                        with open(info_path, 'r', encoding='utf-8') as f:
                            backup_info = json.load(f)
                            backups.append(backup_info)
                    except Exception as e:
                        self.logger.error(f"خطأ في قراءة معلومات النسخة الاحتياطية: {e}")
            
            # ترتيب النسخ حسب التاريخ (الأحدث أولاً)
            backups.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
            return backups
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على قائمة النسخ الاحتياطية: {e}")
            return []
    
    def restore_backup(self, backup_path):
        """استعادة نسخة احتياطية"""
        try:
            # هذه الوظيفة تحتاج تنفيذ حذر لتجنب فقدان البيانات
            # سيتم تنفيذها في واجهة منفصلة
            self.logger.info(f"طلب استعادة النسخة الاحتياطية: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            return False
