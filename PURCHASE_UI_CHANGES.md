# تحديث تصميم واجهات المشتريات

## الهدف
تحديث تصميم جميع واجهات المشتريات لتصبح طبق الأصل من تصميم فواتير البيع

## التغييرات التي تم إجراؤها

### 1. ملف `ui/purchase_invoice.py`
- تم تحديث تصميم النافذة الرئيسية لتتطابق مع فاتورة البيع
- تم تطبيق الألوان الخضراء المستخدمة في فاتورة البيع:
  - لون الخلفية: `#c8e6c9`
  - لون الإطارات: `#e8f5e8`
  - لون الحدود: `#81c784` و `#a5d6a7`
  - لون النصوص: `#2e7d32`
  - لون الأزرار: `#66bb6a`
- تم إزالة التصميم المخصص للأزرار الفردية واستخدام التصميم الموحد
- تم تحديث تصميم العنوان والعناصر الأخرى

### 2. ملف `ui/purchases_main.py`
- تم تحديث تصميم النافذة الرئيسية لإدارة المشتريات
- تم توحيد ألوان جميع الأزرار لتكون باللون الأخضر `#66bb6a`
- تم تحديث تصميم الإطارات والعناوين
- تم تحديث دالة `darken_color()` لتتناسب مع الألوان الجديدة

### 3. ملف `ui/purchase_invoices_list.py`
- تم تحديث تصميم نافذة عرض فواتير الشراء
- تم تطبيق نفس الألوان والتصميم المستخدم في فواتير البيع
- تم تحديث العنوان والنصوص

### 4. ملف `ui/purchase_returns.py`
- تم تحديث تصميم نافذة مرتجعات المشتريات
- تم تطبيق نفس الألوان والتصميم الموحد

### 5. ملف `ui/purchase_reports.py`
- تم تحديث تصميم نافذة تقارير المشتريات
- تم تطبيق نفس الألوان والتصميم الموحد

### 6. ملف `ui/purchase_invoice_new.py`
- تم تحديث تصميم فاتورة الشراء الجديدة بالكامل
- تم تغيير التصميم من الألوان الزرقاء إلى الألوان الخضراء
- تم تحديث:
  - ألوان الخلفية والإطارات
  - ألوان الأزرار وحالات التمرير والضغط
  - ألوان الجداول والعناوين
  - ألوان النصوص والتسميات
  - ألوان صناديق التحديد (CheckBox)

## الألوان المستخدمة
- **لون الخلفية الرئيسي**: `#c8e6c9`
- **لون خلفية الإطارات**: `#e8f5e8`
- **لون الحدود الأساسي**: `#81c784`
- **لون الحدود الثانوي**: `#a5d6a7`
- **لون النصوص**: `#2e7d32`
- **لون الأزرار**: `#66bb6a`
- **لون الأزرار عند التمرير**: `#4caf50`
- **لون الأزرار عند الضغط**: `#388e3c`
- **لون عناوين الجداول**: `#81c784`
- **لون صفوف الجداول المتناوبة**: `#f1f8e9`

## فوائد التحديث
1. **توحيد التصميم**: جميع واجهات المشتريات تستخدم نفس التصميم
2. **سهولة الاستخدام**: نفس الألوان والتخطيط المألوف من فواتير البيع
3. **المظهر المهني**: تصميم موحد ومتسق في جميع أنحاء التطبيق
4. **تجربة مستخدم محسنة**: لا حاجة لتعلم واجهات مختلفة

## اختبار التحديث
تم إنشاء ملف `test_purchase_ui.py` لاختبار التحديثات الجديدة

## التوافق
جميع التحديثات متوافقة مع الكود الحالي ولا تؤثر على الوظائف الأساسية