#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة معاينة الفاتورة - مطابقة للتصميم المطلوب
Invoice Preview Window - Matching Required Design
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QHeaderView, QGroupBox, QGridLayout, QMessageBox,
                            QFrame, QWidget)
from PyQt5.QtCore import Qt

class InvoicePreview(QDialog):
    """نافذة معاينة الفاتورة بالتصميم المطلوب"""
    
    def __init__(self, db_manager, invoice_id, invoice_type='sale', parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.invoice_id = invoice_id
        self.invoice_type = invoice_type
        self.invoice_data = None
        self.invoice_items = []
        
        self.init_ui()
        self.load_invoice_data()
        self.display_invoice()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("📋 معاينة الفاتورة")
        self.setGeometry(150, 100, 900, 500)  # تصغير الطول للنصف تقريباً
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QLabel {
                font-family: 'Arial';
                font-size: 11px;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #4caf50;
                border-radius: 6px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 8px 0 8px;
                color: #2e7d32;
                font-size: 12px;
            }
        """)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # المحتوى الرئيسي بدون تمرير لإظهار كل شيء في صورة واحدة
        content_widget = QWidget()
        layout = QVBoxLayout(content_widget)

        # رأس الفاتورة
        self.create_header(layout)

        # معلومات الفاتورة
        self.create_info_section(layout)

        # جدول الأصناف
        self.create_items_table(layout)

        # الإجماليات
        self.create_totals_section(layout)

        main_layout.addWidget(content_widget)
        
        # أزرار العمليات
        self.create_buttons(main_layout)
        
        self.setLayout(main_layout)
        
    def create_header(self, layout):
        """إنشاء رأس الفاتورة"""
        header_group = QGroupBox()
        header_group.setStyleSheet("QGroupBox { border: none; }")
        header_layout = QVBoxLayout()

        # معلومات الشركة
        company_name = QLabel("شركة الطيب للتجارة والتوزيع")
        company_name.setAlignment(Qt.AlignCenter)
        company_name.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2e7d32;
            margin: 2px;
            padding: 4px;
        """)
        header_layout.addWidget(company_name)

        # أرقام التليفون
        phone_numbers = QLabel("01008379651 - 01284860988")
        phone_numbers.setAlignment(Qt.AlignCenter)
        phone_numbers.setStyleSheet("""
            font-size: 12px;
            color: #666;
            margin: 1px;
            padding: 2px;
        """)
        header_layout.addWidget(phone_numbers)

        # نوع الفاتورة
        self.invoice_title_label = QLabel()
        self.invoice_title_label.setAlignment(Qt.AlignCenter)
        self.invoice_title_label.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #2e7d32;
            background-color: #e8f5e8;
            padding: 6px;
            border: 2px solid #4caf50;
            border-radius: 6px;
            margin: 3px;
        """)
        header_layout.addWidget(self.invoice_title_label)

        header_group.setLayout(header_layout)
        layout.addWidget(header_group)
        
    def create_info_section(self, layout):
        """إنشاء قسم معلومات الفاتورة"""
        info_group = QGroupBox()
        info_group.setStyleSheet("QGroupBox { border: 1px solid #ddd; background-color: white; }")
        info_layout = QGridLayout()
        info_layout.setSpacing(8)

        # العمود الأيمن - معلومات أساسية
        right_frame = QFrame()
        right_frame.setStyleSheet("QFrame { border: none; }")
        right_layout = QGridLayout(right_frame)

        right_labels = ["التاريخ:", "رقم البيان:", "العميل:", "العنوان:"]
        self.right_values = []

        for i, label_text in enumerate(right_labels):
            label = QLabel(label_text)
            label.setStyleSheet("""
                font-weight: bold;
                color: #333;
                padding: 3px 2px;
                text-align: right;
                font-size: 10px;
            """)
            label.setAlignment(Qt.AlignRight)
            right_layout.addWidget(label, i, 0)

            value = QLabel()
            value.setStyleSheet("""
                padding: 3px 6px;
                border-bottom: 1px dotted #999;
                background-color: #fafafa;
                min-width: 100px;
                font-size: 10px;
            """)
            right_layout.addWidget(value, i, 1)
            self.right_values.append(value)

        # العمود الأيسر - معلومات إضافية
        left_frame = QFrame()
        left_frame.setStyleSheet("QFrame { border: none; }")
        left_layout = QGridLayout(left_frame)

        # عنوان القسم
        self.section_title = QLabel()
        self.section_title.setStyleSheet("""
            font-weight: bold;
            color: #2e7d32;
            padding: 4px;
            text-align: center;
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 3px;
            font-size: 10px;
        """)
        self.section_title.setAlignment(Qt.AlignCenter)
        left_layout.addWidget(self.section_title, 0, 0, 1, 2)

        left_labels = ["المستخدم:", "كود العميل:", "تليفون:"]
        self.left_values = []

        for i, label_text in enumerate(left_labels):
            label = QLabel(label_text)
            label.setStyleSheet("""
                font-weight: bold;
                color: #333;
                padding: 3px 2px;
                text-align: right;
                font-size: 10px;
            """)
            label.setAlignment(Qt.AlignRight)
            left_layout.addWidget(label, i+1, 0)

            value = QLabel()
            value.setStyleSheet("""
                padding: 3px 6px;
                border-bottom: 1px dotted #999;
                background-color: #fafafa;
                min-width: 80px;
                font-size: 10px;
            """)
            left_layout.addWidget(value, i+1, 1)
            self.left_values.append(value)

        # إضافة الإطارات إلى التخطيط الرئيسي
        info_layout.addWidget(right_frame, 0, 0)
        info_layout.addWidget(left_frame, 0, 1)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
    def create_items_table(self, layout):
        """إنشاء جدول الأصناف"""
        items_group = QGroupBox("أصناف الفاتورة")
        items_layout = QVBoxLayout()

        self.items_table = QTableWidget()
        self.items_table.setColumnCount(6)
        self.items_table.setHorizontalHeaderLabels([
            "م", "الصنف", "الوحدة", "الكمية", "السعر", "الإجمالي"
        ])

        # تنسيق الجدول ليطابق الصورة
        self.items_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #4caf50;
                background-color: white;
                alternate-background-color: #f0f8f0;
                border: 2px solid #4caf50;
                font-size: 10px;
            }
            QTableWidget::item {
                padding: 6px;
                border: 1px solid #4caf50;
                text-align: center;
            }
            QHeaderView::section {
                background-color: #4caf50;
                color: white;
                padding: 8px;
                font-weight: bold;
                border: 1px solid #2e7d32;
                font-size: 11px;
            }
        """)

        self.items_table.setAlternatingRowColors(True)

        # إزالة أشرطة التمرير لإظهار كل الأصناف
        self.items_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.items_table.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # تعديل عرض الأعمدة
        header = self.items_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # رقم
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # الصنف
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # الوحدة
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # الكمية
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # السعر
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # الإجمالي

        self.items_table.setColumnWidth(0, 40)   # م
        self.items_table.setColumnWidth(2, 60)   # الوحدة
        self.items_table.setColumnWidth(3, 60)   # الكمية
        self.items_table.setColumnWidth(4, 80)   # السعر
        self.items_table.setColumnWidth(5, 90)   # الإجمالي

        items_layout.addWidget(self.items_table)
        items_group.setLayout(items_layout)
        layout.addWidget(items_group)
        
    def create_totals_section(self, layout):
        """إنشاء قسم الإجماليات بتصميم مضغوط"""
        # إنشاء تخطيط عمودي للإجماليات
        totals_main_layout = QVBoxLayout()

        # قسم الإجماليات الرئيسية
        main_totals_layout = QHBoxLayout()

        # الإجماليات الأساسية في صف واحد
        totals_data = [
            ("إجمالي الكمية:", "total_qty"),
            ("الإجمالي قبل الخصم:", "subtotal"),
            ("خصم عام:", "discount"),
            ("صافي الفاتورة:", "final_total")
        ]

        self.totals_labels = {}

        for label_text, key in totals_data:
            # إنشاء مجموعة لكل إجمالي
            total_widget = QWidget()
            total_layout = QVBoxLayout(total_widget)
            total_layout.setSpacing(2)
            total_layout.setContentsMargins(5, 5, 5, 5)

            # العنوان
            title_label = QLabel(label_text)
            title_label.setStyleSheet("""
                font-weight: bold;
                color: #2e7d32;
                font-size: 10px;
                text-align: center;
                padding: 2px;
            """)
            title_label.setAlignment(Qt.AlignCenter)
            total_layout.addWidget(title_label)

            # القيمة
            value_label = QLabel("0.00")
            value_label.setStyleSheet("""
                font-weight: bold;
                font-size: 12px;
                color: #1976d2;
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 4px;
                text-align: center;
            """)
            value_label.setAlignment(Qt.AlignCenter)
            total_layout.addWidget(value_label)

            self.totals_labels[key] = value_label
            main_totals_layout.addWidget(total_widget)

        totals_main_layout.addLayout(main_totals_layout)

        # قسم التفاصيل الإضافية في صف ثاني
        details_layout = QHBoxLayout()

        details_data = [
            ("الرصيد السابق:", "previous_balance"),
            ("إجمالي:", "total"),
            ("المدفوع:", "paid"),
            ("الرصيد الحالي:", "remaining")
        ]

        for label_text, key in details_data:
            # إنشاء مجموعة لكل تفصيل
            detail_widget = QWidget()
            detail_layout = QVBoxLayout(detail_widget)
            detail_layout.setSpacing(2)
            detail_layout.setContentsMargins(5, 5, 5, 5)

            # العنوان
            title_label = QLabel(label_text)
            title_label.setStyleSheet("""
                font-weight: bold;
                color: #666;
                font-size: 9px;
                text-align: center;
                padding: 2px;
            """)
            title_label.setAlignment(Qt.AlignCenter)
            detail_layout.addWidget(title_label)

            # القيمة
            value_label = QLabel("0.00")
            value_label.setStyleSheet("""
                font-size: 10px;
                color: #333;
                background-color: #fafafa;
                border: 1px solid #eee;
                border-radius: 2px;
                padding: 3px;
                text-align: center;
            """)
            value_label.setAlignment(Qt.AlignCenter)
            detail_layout.addWidget(value_label)

            self.totals_labels[key] = value_label
            details_layout.addWidget(detail_widget)

        totals_main_layout.addLayout(details_layout)
        layout.addLayout(totals_main_layout)
        
    def create_buttons(self, layout):
        """إنشاء أزرار العمليات"""
        buttons_layout = QHBoxLayout()
        
        # زر الطباعة
        print_btn = QPushButton("🖨️ طباعة")
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #4caf50;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 6px;
                font-size: 12px;
                border: none;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        print_btn.clicked.connect(self.print_invoice)
        buttons_layout.addWidget(print_btn)
        
        # زر إغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 6px;
                font-size: 12px;
                border: none;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)
        
        layout.addLayout(buttons_layout)
        
    def load_invoice_data(self):
        """تحميل بيانات الفاتورة"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # تحميل بيانات الفاتورة الأساسية
            if self.invoice_type == 'sale':
                cursor.execute('''
                    SELECT i.*, c.name as customer_name, c.phone as customer_phone,
                           c.address as customer_address, c.id as customer_code
                    FROM invoices i
                    LEFT JOIN customers c ON i.customer_id = c.id
                    WHERE i.id = ? AND i.invoice_type = 'sale'
                ''', (self.invoice_id,))
            else:
                cursor.execute('''
                    SELECT i.*, s.name as supplier_name, s.phone as supplier_phone,
                           s.address as supplier_address, s.id as supplier_code
                    FROM invoices i
                    LEFT JOIN suppliers s ON i.supplier_id = s.id
                    WHERE i.id = ? AND i.invoice_type = 'purchase'
                ''', (self.invoice_id,))
            
            self.invoice_data = cursor.fetchone()
            
            # تحميل أصناف الفاتورة
            cursor.execute('''
                SELECT ii.*, p.name as product_name, p.unit
                FROM invoice_items ii
                JOIN products p ON ii.product_id = p.id
                WHERE ii.invoice_id = ?
                ORDER BY ii.id
            ''', (self.invoice_id,))
            
            self.invoice_items = cursor.fetchall()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل بيانات الفاتورة:\n{str(e)}")
        finally:
            conn.close()
    
    def display_invoice(self):
        """عرض بيانات الفاتورة"""
        if not self.invoice_data:
            return
            
        try:
            # تحديث العنوان
            invoice_title = "فاتورة مبيعات" if self.invoice_type == 'sale' else "فاتورة مشتريات"
            self.invoice_title_label.setText(invoice_title)

            # تحديث عنوان القسم
            section_title = "بيان مبيعات نقدي" if self.invoice_type == 'sale' else "بيان مشتريات نقدي"
            self.section_title.setText(section_title)
            
            # تحديث المعلومات الأساسية
            entity_name = self.invoice_data['customer_name'] if self.invoice_type == 'sale' and self.invoice_data['customer_name'] else 'عميل نقدي'
            if self.invoice_type == 'purchase':
                entity_name = self.invoice_data['supplier_name'] if self.invoice_data['supplier_name'] else 'مورد نقدي'
            
            self.right_values[0].setText(str(self.invoice_data['invoice_date']))
            self.right_values[1].setText(str(self.invoice_data['invoice_number']))
            self.right_values[2].setText(entity_name)

            # عرض العنوان الفعلي
            if self.invoice_type == 'sale':
                entity_address = self.invoice_data['customer_address'] if self.invoice_data['customer_address'] else "غير محدد"
            else:
                entity_address = self.invoice_data['supplier_address'] if self.invoice_data['supplier_address'] else "غير محدد"

            self.right_values[3].setText(entity_address)
            
            # المعلومات الإضافية
            self.left_values[0].setText("المدير")  # المستخدم

            # عرض رقم العميل/المورد الفعلي
            if self.invoice_type == 'sale':
                entity_code = str(self.invoice_data['customer_code']) if self.invoice_data['customer_code'] else "نقدي"
                entity_phone = self.invoice_data['customer_phone'] if self.invoice_data['customer_phone'] else "غير محدد"
            else:
                entity_code = str(self.invoice_data['supplier_code']) if self.invoice_data['supplier_code'] else "نقدي"
                entity_phone = self.invoice_data['supplier_phone'] if self.invoice_data['supplier_phone'] else "غير محدد"

            self.left_values[1].setText(entity_code)  # كود العميل/المورد
            self.left_values[2].setText(entity_phone)  # التليفون
            
            # تحديث جدول الأصناف
            self.items_table.setRowCount(len(self.invoice_items))

            for i, item in enumerate(self.invoice_items):
                # رقم متسلسل
                self.items_table.setItem(i, 0, QTableWidgetItem(str(i + 1)))

                # اسم الصنف
                product_name = item['product_name'] if item['product_name'] else 'غير محدد'
                self.items_table.setItem(i, 1, QTableWidgetItem(product_name))

                # الوحدة
                unit = item['unit'] if item['unit'] else 'قطعة'
                self.items_table.setItem(i, 2, QTableWidgetItem(unit))

                # الكمية
                quantity = item['quantity'] if item['quantity'] else 0
                self.items_table.setItem(i, 3, QTableWidgetItem(str(quantity)))

                # السعر
                unit_price = item['unit_price'] if item['unit_price'] else 0
                self.items_table.setItem(i, 4, QTableWidgetItem(f"{unit_price:.2f}"))

                # الإجمالي
                total_price = item['total_price'] if item['total_price'] else 0
                self.items_table.setItem(i, 5, QTableWidgetItem(f"{total_price:.2f}"))

                # توسيط النص
                for col in range(6):
                    if self.items_table.item(i, col):
                        self.items_table.item(i, col).setTextAlignment(Qt.AlignCenter)

            # تعديل حجم الجدول ليناسب عدد الأصناف
            self.resize_table_to_content()
            
            # تحديث الإجماليات
            total_quantity = sum(item['quantity'] if item['quantity'] else 0 for item in self.invoice_items)

            # تحديث الإجماليات الجديدة
            self.totals_labels['total_qty'].setText(str(total_quantity))
            self.totals_labels['subtotal'].setText(f"{self.invoice_data['total_amount']:.2f}")
            self.totals_labels['discount'].setText(f"{self.invoice_data['discount_amount']:.2f}")
            self.totals_labels['final_total'].setText(f"{self.invoice_data['final_amount']:.2f}")

            # التفاصيل الإضافية
            self.totals_labels['previous_balance'].setText("0.00")
            self.totals_labels['total'].setText(f"{self.invoice_data['total_amount']:.2f}")
            self.totals_labels['paid'].setText(f"{self.invoice_data['paid_amount']:.2f}")
            self.totals_labels['remaining'].setText(f"{self.invoice_data['remaining_amount']:.2f}")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عرض بيانات الفاتورة:\n{str(e)}")

    def resize_table_to_content(self):
        """تعديل حجم الجدول ليناسب المحتوى"""
        try:
            # حساب الارتفاع المطلوب للجدول
            row_count = self.items_table.rowCount()
            header_height = self.items_table.horizontalHeader().height()
            row_height = 28  # ارتفاع كل صف (أصغر)

            # الارتفاع الإجمالي = ارتفاع الرأس + (عدد الصفوف × ارتفاع الصف) + هامش
            total_height = header_height + (row_count * row_height) + 8

            # تحديد الحد الأقصى للارتفاع (أصغر للنافذة المصغرة)
            max_height = 300
            final_height = min(total_height, max_height)

            # تطبيق الارتفاع الجديد
            self.items_table.setFixedHeight(final_height)

            # تعديل ارتفاع الصفوف
            for i in range(row_count):
                self.items_table.setRowHeight(i, row_height)

        except Exception as e:
            print(f"خطأ في تعديل حجم الجدول: {e}")

    def print_invoice(self):
        """طباعة الفاتورة"""
        try:
            from ui.professional_printer import ProfessionalPrinter
            printer_dialog = ProfessionalPrinter(self.db_manager, self.invoice_id, self.invoice_type, self)
            printer_dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في فتح نافذة الطباعة:\n{str(e)}")
