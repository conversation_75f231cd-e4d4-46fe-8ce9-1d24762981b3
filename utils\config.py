#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات النظام - نظام نقاط البيع والمحاسبة
System Configuration - POS and Accounting System
"""

class Config:
    """إعدادات النظام"""
    
    # معلومات الشركة
    COMPANY_NAME = "الطيب للتجارة والتوزيع"
    COMPANY_OWNER = "شادي الطيب"
    COMPANY_ADDRESS = "القاهرة"
    COMPANY_PHONE1 = "***********"
    COMPANY_PHONE2 = "***********"
    
    # إعدادات قاعدة البيانات
    DATABASE_PATH = "pos_database.db"
    
    # إعدادات الواجهة
    WINDOW_TITLE = f"نظام نقاط البيع والمحاسبة - {COMPANY_NAME}"
    WINDOW_WIDTH = 1200
    WINDOW_HEIGHT = 800
    
    # إعدادات الخط
    FONT_FAMILY = "Arial"
    FONT_SIZE = 12
    
    # إعدادات الفواتير
    INVOICE_PREFIX_SALE = "S"
    INVOICE_PREFIX_PURCHASE = "P"
    RETURN_PREFIX_SALE = "SR"
    RETURN_PREFIX_PURCHASE = "PR"
    
    # أنواع العملاء
    CUSTOMER_TYPES = {
        'retail': 'قطاعي',
        'wholesale': 'جملة',
        'semi_wholesale': 'نص جملة'
    }
    
    # طرق الدفع
    PAYMENT_METHODS = {
        'cash': 'نقدي',
        'bank_transfer': 'تحويل بنكي',
        'credit': 'آجل',
        'partial': 'جزئي'
    }
    
    # أنواع الحركة في المخزون
    STOCK_MOVEMENT_TYPES = {
        'in': 'إدخال',
        'out': 'إخراج',
        'adjustment': 'تعديل'
    }
    
    # الألوان
    COLORS = {
        'primary': '#2E86AB',
        'secondary': '#A23B72',
        'success': '#F18F01',
        'danger': '#C73E1D',
        'warning': '#F4A261',
        'info': '#264653',
        'light': '#F8F9FA',
        'dark': '#212529'
    }
    
    # مسارات التقارير
    REPORTS_DIR = "reports"
    EXPORTS_DIR = "exports"
    
    @classmethod
    def get_company_header(cls):
        """الحصول على ترويسة الشركة للفواتير والتقارير"""
        return {
            'name': cls.COMPANY_NAME,
            'owner': cls.COMPANY_OWNER,
            'address': cls.COMPANY_ADDRESS,
            'phone1': cls.COMPANY_PHONE1,
            'phone2': cls.COMPANY_PHONE2
        }
