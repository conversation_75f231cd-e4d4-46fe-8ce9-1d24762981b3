"""
واجهة النظام المالي المتكامل
إدارة رأس المال، الخزينة، وحساب البنك
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QTab<PERSON>idget, QLabel, QPushButton, QTableWidget,
                             QTableWidgetItem, QGroupBox, QFormLayout,
                             QDoubleSpinBox, QComboBox, QDateEdit, QLineEdit,
                             QMessageBox, QHeaderView, QDialog)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont
from database.financial_manager import FinancialManager
import os


class FinancialManagementWindow(QMainWindow):
    """نافذة النظام المالي المتكامل"""
    
    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        
        # إنشاء مدير النظام المالي
        db_path = 'pos_system.db'
        try:
            self.financial_manager = FinancialManager(db_path)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في قاعدة البيانات: {str(e)}")
            return
        
        self.init_ui()
        self.load_data()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("النظام المالي المتكامل")
        self.setGeometry(100, 100, 1000, 700)
        
        # الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # عرض الأرصدة الحالية
        self.create_balance_summary(main_layout)
        
        # التبويبات
        self.create_tabs(main_layout)
    
    def create_balance_summary(self, layout):
        """إنشاء عرض ملخص الأرصدة"""
        summary_group = QGroupBox("ملخص الأرصدة الحالية")
        summary_layout = QHBoxLayout()
        
        # رصيد رأس المال
        self.capital_label = QLabel("رأس المال: 0.00 جنيه")
        self.capital_label.setStyleSheet("font-weight: bold; color: blue; padding: 10px; border: 1px solid blue;")
        summary_layout.addWidget(self.capital_label)
        
        # رصيد الخزينة
        self.cash_label = QLabel("الخزينة: 0.00 جنيه")
        self.cash_label.setStyleSheet("font-weight: bold; color: green; padding: 10px; border: 1px solid green;")
        summary_layout.addWidget(self.cash_label)
        
        # رصيد البنك
        self.bank_label = QLabel("البنك: 0.00 جنيه")
        self.bank_label.setStyleSheet("font-weight: bold; color: orange; padding: 10px; border: 1px solid orange;")
        summary_layout.addWidget(self.bank_label)
        
        # الإجمالي
        self.total_label = QLabel("الإجمالي: 0.00 جنيه")
        self.total_label.setStyleSheet("font-weight: bold; color: red; padding: 10px; border: 2px solid red;")
        summary_layout.addWidget(self.total_label)
        
        summary_group.setLayout(summary_layout)
        layout.addWidget(summary_group)
    
    def create_tabs(self, layout):
        """إنشاء التبويبات"""
        self.tabs = QTabWidget()
        
        # تبويب رأس المال
        self.capital_tab = self.create_capital_tab()
        self.tabs.addTab(self.capital_tab, "رأس المال")
        
        # تبويب الخزينة
        self.cash_tab = self.create_cash_tab()
        self.tabs.addTab(self.cash_tab, "الخزينة")
        
        # تبويب البنك
        self.bank_tab = self.create_bank_tab()
        self.tabs.addTab(self.bank_tab, "البنك")
        
        # تبويب التحويلات
        self.transfers_tab = self.create_transfers_tab()
        self.tabs.addTab(self.transfers_tab, "التحويلات")
        
        layout.addWidget(self.tabs)
    
    def create_capital_tab(self):
        """إنشاء تبويب رأس المال"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        add_capital_btn = QPushButton("إضافة رأس مال")
        add_capital_btn.clicked.connect(self.add_capital)
        buttons_layout.addWidget(add_capital_btn)
        
        withdraw_capital_btn = QPushButton("سحب من رأس المال")
        withdraw_capital_btn.clicked.connect(self.withdraw_capital)
        buttons_layout.addWidget(withdraw_capital_btn)
        
        layout.addLayout(buttons_layout)
        
        # جدول المعاملات
        self.capital_table = QTableWidget()
        self.capital_table.setColumnCount(5)
        self.capital_table.setHorizontalHeaderLabels(["التاريخ", "نوع المعاملة", "المبلغ", "الوصف", "تاريخ الإنشاء"])
        self.capital_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.capital_table)
        
        widget.setLayout(layout)
        return widget
    
    def create_cash_tab(self):
        """إنشاء تبويب الخزينة"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        add_income_btn = QPushButton("إضافة إيراد")
        add_income_btn.clicked.connect(self.add_cash_income)
        buttons_layout.addWidget(add_income_btn)
        
        add_expense_btn = QPushButton("إضافة مصروف")
        add_expense_btn.clicked.connect(self.add_cash_expense)
        buttons_layout.addWidget(add_expense_btn)
        
        layout.addLayout(buttons_layout)
        
        # جدول المعاملات
        self.cash_table = QTableWidget()
        self.cash_table.setColumnCount(6)
        self.cash_table.setHorizontalHeaderLabels(["التاريخ", "نوع المعاملة", "المصدر", "المبلغ", "الوصف", "تاريخ الإنشاء"])
        self.cash_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.cash_table)
        
        widget.setLayout(layout)
        return widget
    
    def create_bank_tab(self):
        """إنشاء تبويب البنك"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        add_deposit_btn = QPushButton("إضافة إيداع")
        add_deposit_btn.clicked.connect(self.add_bank_deposit)
        buttons_layout.addWidget(add_deposit_btn)
        
        add_withdrawal_btn = QPushButton("إضافة سحب")
        add_withdrawal_btn.clicked.connect(self.add_bank_withdrawal)
        buttons_layout.addWidget(add_withdrawal_btn)
        
        layout.addLayout(buttons_layout)
        
        # جدول المعاملات
        self.bank_table = QTableWidget()
        self.bank_table.setColumnCount(6)
        self.bank_table.setHorizontalHeaderLabels(["التاريخ", "نوع المعاملة", "المصدر", "المبلغ", "الوصف", "تاريخ الإنشاء"])
        self.bank_table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.bank_table)
        
        widget.setLayout(layout)
        return widget
    
    def create_transfers_tab(self):
        """إنشاء تبويب التحويلات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # أزرار التحويلات
        buttons_layout = QHBoxLayout()
        
        bank_to_cash_btn = QPushButton("تحويل من البنك للخزينة")
        bank_to_cash_btn.clicked.connect(self.transfer_bank_to_cash)
        buttons_layout.addWidget(bank_to_cash_btn)
        
        cash_to_bank_btn = QPushButton("تحويل من الخزينة للبنك")
        cash_to_bank_btn.clicked.connect(self.transfer_cash_to_bank)
        buttons_layout.addWidget(cash_to_bank_btn)
        
        layout.addLayout(buttons_layout)
        
        # معلومات التحويلات
        info_label = QLabel("استخدم هذا القسم لتحويل الأموال بين الخزينة والبنك")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("padding: 20px; font-size: 14px; color: gray;")
        layout.addWidget(info_label)
        
        widget.setLayout(layout)
        return widget
    
    def load_data(self):
        """تحميل البيانات"""
        self.update_balances()
        self.load_capital_transactions()
        self.load_cash_transactions()
        self.load_bank_transactions()
    
    def update_balances(self):
        """تحديث الأرصدة"""
        try:
            summary = self.financial_manager.get_financial_summary()
            
            self.capital_label.setText(f"رأس المال: {summary['capital_balance']:,.2f} جنيه")
            self.cash_label.setText(f"الخزينة: {summary['cash_balance']:,.2f} جنيه")
            self.bank_label.setText(f"البنك: {summary['bank_balance']:,.2f} جنيه")
            self.total_label.setText(f"الإجمالي: {summary['total_assets']:,.2f} جنيه")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحديث الأرصدة: {str(e)}")
    
    def load_capital_transactions(self):
        """تحميل معاملات رأس المال"""
        try:
            transactions = self.financial_manager.get_capital_transactions()
            self.populate_table(self.capital_table, transactions, 5)
        except Exception as e:
            print(f"خطأ في تحميل معاملات رأس المال: {str(e)}")
    
    def load_cash_transactions(self):
        """تحميل معاملات الخزينة"""
        try:
            transactions = self.financial_manager.get_cash_transactions()
            self.populate_table(self.cash_table, transactions, 6)
        except Exception as e:
            print(f"خطأ في تحميل معاملات الخزينة: {str(e)}")
    
    def load_bank_transactions(self):
        """تحميل المعاملات البنكية"""
        try:
            transactions = self.financial_manager.get_bank_transactions()
            self.populate_table(self.bank_table, transactions, 6)
        except Exception as e:
            print(f"خطأ في تحميل المعاملات البنكية: {str(e)}")
    
    def populate_table(self, table, data, columns):
        """ملء الجدول بالبيانات"""
        table.setRowCount(len(data))
        for row, record in enumerate(data):
            for col in range(columns):
                if col < len(record):
                    item = QTableWidgetItem(str(record[col + 1]))  # تخطي العمود الأول (ID)
                    table.setItem(row, col, item)
    

    # ==================== وظائف رأس المال ====================

    def add_capital(self):
        """إضافة رأس مال"""
        dialog = TransactionDialog("إضافة رأس مال", ["المبلغ", "الوصف"])
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            try:
                amount = float(data["المبلغ"])
                description = data["الوصف"]

                self.financial_manager.add_capital_transaction("إضافة", amount, description)
                self.load_data()
                QMessageBox.information(self, "تم", f"تم إضافة {amount:,.2f} جنيه لرأس المال")
            except ValueError:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ صحيح")

    def withdraw_capital(self):
        """سحب من رأس المال"""
        dialog = TransactionDialog("سحب من رأس المال", ["المبلغ", "الوصف"])
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            try:
                amount = float(data["المبلغ"])
                description = data["الوصف"]

                current_balance = self.financial_manager.get_capital_balance()
                if amount > current_balance:
                    QMessageBox.warning(self, "خطأ", f"المبلغ أكبر من الرصيد المتاح ({current_balance:,.2f} جنيه)")
                    return

                self.financial_manager.add_capital_transaction("سحب", amount, description)
                self.load_data()
                QMessageBox.information(self, "تم", f"تم سحب {amount:,.2f} جنيه من رأس المال")
            except ValueError:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ صحيح")

    # ==================== وظائف الخزينة ====================

    def add_cash_income(self):
        """إضافة إيراد للخزينة"""
        dialog = CashTransactionDialog("إضافة إيراد للخزينة")
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            try:
                amount = float(data["المبلغ"])
                source = data["المصدر"]
                description = data["الوصف"]

                self.financial_manager.add_cash_transaction("إيراد", source, amount, description)
                self.load_data()
                QMessageBox.information(self, "تم", f"تم إضافة إيراد {amount:,.2f} جنيه للخزينة")
            except ValueError:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ صحيح")

    def add_cash_expense(self):
        """إضافة مصروف للخزينة"""
        dialog = CashTransactionDialog("إضافة مصروف للخزينة")
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            try:
                amount = float(data["المبلغ"])
                source = data["المصدر"]
                description = data["الوصف"]

                current_balance = self.financial_manager.get_cash_balance()
                if amount > current_balance:
                    QMessageBox.warning(self, "خطأ", f"المبلغ أكبر من رصيد الخزينة ({current_balance:,.2f} جنيه)")
                    return

                self.financial_manager.add_cash_transaction("مصروف", source, amount, description)
                self.load_data()
                QMessageBox.information(self, "تم", f"تم إضافة مصروف {amount:,.2f} جنيه للخزينة")
            except ValueError:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ صحيح")

    # ==================== وظائف البنك ====================

    def add_bank_deposit(self):
        """إضافة إيداع بنكي"""
        dialog = BankTransactionDialog("إضافة إيداع بنكي")
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            try:
                amount = float(data["المبلغ"])
                source = data["المصدر"]
                description = data["الوصف"]

                self.financial_manager.add_bank_transaction("إيداع", source, amount, description)
                self.load_data()
                QMessageBox.information(self, "تم", f"تم إيداع {amount:,.2f} جنيه في البنك")
            except ValueError:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ صحيح")

    def add_bank_withdrawal(self):
        """إضافة سحب بنكي"""
        dialog = BankTransactionDialog("إضافة سحب بنكي")
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            try:
                amount = float(data["المبلغ"])
                source = data["المصدر"]
                description = data["الوصف"]

                current_balance = self.financial_manager.get_bank_balance()
                if amount > current_balance:
                    QMessageBox.warning(self, "خطأ", f"المبلغ أكبر من رصيد البنك ({current_balance:,.2f} جنيه)")
                    return

                self.financial_manager.add_bank_transaction("سحب", source, amount, description)
                self.load_data()
                QMessageBox.information(self, "تم", f"تم سحب {amount:,.2f} جنيه من البنك")
            except ValueError:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ صحيح")

    # ==================== وظائف التحويلات ====================

    def transfer_bank_to_cash(self):
        """تحويل من البنك للخزينة"""
        dialog = TransactionDialog("تحويل من البنك للخزينة", ["المبلغ", "الوصف"])
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            try:
                amount = float(data["المبلغ"])
                description = data["الوصف"] or "تحويل من البنك للخزينة"

                current_balance = self.financial_manager.get_bank_balance()
                if amount > current_balance:
                    QMessageBox.warning(self, "خطأ", f"المبلغ أكبر من رصيد البنك ({current_balance:,.2f} جنيه)")
                    return

                self.financial_manager.transfer_bank_to_cash(amount, description)
                self.load_data()
                QMessageBox.information(self, "تم", f"تم تحويل {amount:,.2f} جنيه من البنك للخزينة")
            except ValueError:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ صحيح")

    def transfer_cash_to_bank(self):
        """تحويل من الخزينة للبنك"""
        dialog = TransactionDialog("تحويل من الخزينة للبنك", ["المبلغ", "الوصف"])
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            try:
                amount = float(data["المبلغ"])
                description = data["الوصف"] or "تحويل من الخزينة للبنك"

                current_balance = self.financial_manager.get_cash_balance()
                if amount > current_balance:
                    QMessageBox.warning(self, "خطأ", f"المبلغ أكبر من رصيد الخزينة ({current_balance:,.2f} جنيه)")
                    return

                self.financial_manager.transfer_cash_to_bank(amount, description)
                self.load_data()
                QMessageBox.information(self, "تم", f"تم تحويل {amount:,.2f} جنيه من الخزينة للبنك")
            except ValueError:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ صحيح")


# ==================== نوافذ الحوار ====================

class TransactionDialog(QDialog):
    """حوار المعاملات البسيط"""

    def __init__(self, title, fields, parent=None):
        super().__init__(parent)
        self.fields = fields
        self.inputs = {}
        self.init_ui(title)

    def init_ui(self, title):
        self.setWindowTitle(title)
        self.setFixedSize(300, 200)

        layout = QVBoxLayout()
        form_layout = QFormLayout()

        for field in self.fields:
            if field == "المبلغ":
                input_widget = QDoubleSpinBox()
                input_widget.setRange(0, *********)
                input_widget.setDecimals(2)
                input_widget.setSuffix(" جنيه")
            else:
                input_widget = QLineEdit()

            self.inputs[field] = input_widget
            form_layout.addRow(f"{field}:", input_widget)

        layout.addLayout(form_layout)

        # الأزرار
        buttons_layout = QHBoxLayout()

        ok_btn = QPushButton("موافق")
        ok_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(ok_btn)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def get_data(self):
        data = {}
        for field, input_widget in self.inputs.items():
            if isinstance(input_widget, QDoubleSpinBox):
                data[field] = input_widget.value()
            else:
                data[field] = input_widget.text()
        return data


class CashTransactionDialog(QDialog):
    """حوار معاملات الخزينة"""

    def __init__(self, title, parent=None):
        super().__init__(parent)
        self.init_ui(title)

    def init_ui(self, title):
        self.setWindowTitle(title)
        self.setFixedSize(350, 250)

        layout = QVBoxLayout()
        form_layout = QFormLayout()

        # المبلغ
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0, *********)
        self.amount_input.setDecimals(2)
        self.amount_input.setSuffix(" جنيه")
        form_layout.addRow("المبلغ:", self.amount_input)

        # المصدر
        self.source_combo = QComboBox()
        self.source_combo.addItems([
            "مبيعات", "مرتجع", "مورد", "تحويل بنكي",
            "مصروفات إدارية", "مصروفات تشغيلية", "أخرى"
        ])
        self.source_combo.setEditable(True)
        form_layout.addRow("المصدر:", self.source_combo)

        # الوصف
        self.description_input = QLineEdit()
        form_layout.addRow("الوصف:", self.description_input)

        layout.addLayout(form_layout)

        # الأزرار
        buttons_layout = QHBoxLayout()

        ok_btn = QPushButton("موافق")
        ok_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(ok_btn)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def get_data(self):
        return {
            "المبلغ": self.amount_input.value(),
            "المصدر": self.source_combo.currentText(),
            "الوصف": self.description_input.text()
        }


class BankTransactionDialog(QDialog):
    """حوار المعاملات البنكية"""

    def __init__(self, title, parent=None):
        super().__init__(parent)
        self.init_ui(title)

    def init_ui(self, title):
        self.setWindowTitle(title)
        self.setFixedSize(350, 250)

        layout = QVBoxLayout()
        form_layout = QFormLayout()

        # المبلغ
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0, *********)
        self.amount_input.setDecimals(2)
        self.amount_input.setSuffix(" جنيه")
        form_layout.addRow("المبلغ:", self.amount_input)

        # المصدر
        self.source_combo = QComboBox()
        self.source_combo.addItems([
            "عميل", "مورد", "رأس مال", "سحب للخزينة",
            "تحويل خارجي", "فوائد", "رسوم بنكية", "أخرى"
        ])
        self.source_combo.setEditable(True)
        form_layout.addRow("المصدر:", self.source_combo)

        # الوصف
        self.description_input = QLineEdit()
        form_layout.addRow("الوصف:", self.description_input)

        layout.addLayout(form_layout)

        # الأزرار
        buttons_layout = QHBoxLayout()

        ok_btn = QPushButton("موافق")
        ok_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(ok_btn)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def get_data(self):
        return {
            "المبلغ": self.amount_input.value(),
            "المصدر": self.source_combo.currentText(),
            "الوصف": self.description_input.text()
        }
