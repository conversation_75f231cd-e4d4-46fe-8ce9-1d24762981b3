---
description: Repository Information Overview
alwaysApply: true
---

# POS System Information

## Summary
A comprehensive Point of Sale (POS) and accounting system for retail businesses, developed by <PERSON><PERSON><PERSON>. The system provides complete management for sales, purchases, inventory, customers, suppliers, and financial operations with a modern Arabic interface.

## Structure
- **ui/**: User interface components (30+ modules for different windows)
- **database/**: Database management and operations
- **utils/**: Utility functions and configuration
- **config/**: Application configuration files
- **backups/**: Backup files and configurations
- **release/**: Distribution packages for end-users
- **assets/**: Application assets and resources

## Language & Runtime
**Language**: Python
**Version**: Python 3.8+ required
**Build System**: PyInstaller
**Package Manager**: pip

## Dependencies
**Main Dependencies**:
- PyQt5 (5.15.9) - GUI framework
- SQLite3 - Database (built-in)
- mysql-connector-python (8.2.0) - Database connector
- python-barcode (>=0.13.1) - Barcode generation
- qrcode (>=7.4.2) - QR code generation
- reportlab (4.0.4) - PDF report generation
- pandas (2.0.3) - Data analysis

**Development Dependencies**:
- pyinstaller (>=6.0.0) - For creating executable
- cryptography (>=3.4.8) - Security features
- bcrypt (>=4.0.1) - Password hashing

## Build & Installation
```bash
# Install requirements
pip install -r requirements.txt

# For barcode functionality
pip install -r requirements_barcode.txt

# Build executable
pyinstaller --clean POS_System.spec
```

## Main Entry Points
**Primary Application**: main.py
**Modern Interface**: modern_app.py
**Batch Launchers**:
- build_exe.bat - Creates executable
- install_requirements.bat - Installs dependencies
- build_simple.bat - Simplified build

## Database
**Type**: SQLite (local file-based)
**Schema**: Comprehensive with 15+ tables including:
- users, products, customers, suppliers
- invoices, invoice_items, payments
- bank_accounts, bank_transfers
- sales_returns, purchase_returns

## Features
- **Sales Management**: Invoicing, customer tracking
- **Purchase Management**: Supplier orders, inventory updates
- **Inventory Control**: Stock tracking, alerts
- **Financial Management**: Expenses, banking, transfers
- **Reporting**: Comprehensive business analytics
- **Multi-user**: Role-based access control
- **Barcode Generation**: Product labeling
- **Automatic Backups**: Data protection

## Configuration
**Main Config**: config/app_config.json
**Backup Settings**: backup_config.json
**Default Login**: admin/admin