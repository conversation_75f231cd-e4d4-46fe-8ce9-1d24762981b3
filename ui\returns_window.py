#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة المرتجعات - نظام نقاط البيع والمحاسبة
Returns Window - POS and Accounting System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QComboBox, QDialog, QFormLayout,
                            QMessageBox, QHeaderView, QAbstractItemView,
                            QFrame, QGroupBox, QSpinBox, QDoubleSpinBox,
                            QDateEdit, QTextEdit, QTabWidget, QGridLayout)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QColor
try:
    from utils.config import Config
except ImportError:
    # في حالة عدم وجود ملف Config، استخدم قيم افتراضية
    class Config:
        RETURN_PREFIX_SALE = "SR"
        RETURN_PREFIX_PURCHASE = "PR"
from datetime import datetime
import uuid

class ReturnsWindow(QWidget):
    """نافذة المرتجعات"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data

        try:
            self.init_ui()
            self.load_returns()
        except Exception as e:
            print(f"خطأ في تهيئة نافذة المرتجعات: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تهيئة نافذة المرتجعات:\n{str(e)}")
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة المرتجعات")
        self.setGeometry(100, 100, 1200, 700)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # التبويبات
        tabs = QTabWidget()
        
        # تبويب مرتجع المبيعات
        sales_returns_tab = QWidget()
        self.setup_sales_returns_tab(sales_returns_tab)
        tabs.addTab(sales_returns_tab, "مرتجع المبيعات")
        
        # تبويب مرتجع المشتريات (للمدير فقط)
        if self.user_data['role'] == 'admin':
            purchase_returns_tab = QWidget()
            self.setup_purchase_returns_tab(purchase_returns_tab)
            tabs.addTab(purchase_returns_tab, "مرتجع المشتريات")
        
        main_layout.addWidget(tabs)
        self.setLayout(main_layout)

        # التأكد من وجود جداول المرتجعات
        self.ensure_returns_tables()
        
    def setup_sales_returns_tab(self, tab):
        """إعداد تبويب مرتجع المبيعات"""
        layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        new_return_btn = QPushButton("مرتجع جديد")
        new_return_btn.clicked.connect(self.new_sales_return)
        toolbar_layout.addWidget(new_return_btn)
        
        view_return_btn = QPushButton("عرض المرتجع")
        view_return_btn.clicked.connect(self.view_sales_return)
        toolbar_layout.addWidget(view_return_btn)
        
        toolbar_layout.addStretch()
        
        # البحث
        search_label = QLabel("البحث:")
        toolbar_layout.addWidget(search_label)
        
        self.sales_search_input = QLineEdit()
        self.sales_search_input.setPlaceholderText("رقم المرتجع أو الفاتورة الأصلية...")
        self.sales_search_input.textChanged.connect(self.search_sales_returns)
        toolbar_layout.addWidget(self.sales_search_input)
        
        layout.addLayout(toolbar_layout)
        
        # جدول مرتجع المبيعات
        self.sales_returns_table = QTableWidget()
        self.sales_returns_table.setColumnCount(6)
        self.sales_returns_table.setHorizontalHeaderLabels([
            "رقم المرتجع", "التاريخ", "الفاتورة الأصلية", 
            "العميل", "المبلغ", "السبب"
        ])
        
        # تنسيق الجدول
        header = self.sales_returns_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.sales_returns_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.sales_returns_table.setAlternatingRowColors(True)
        self.sales_returns_table.doubleClicked.connect(self.view_sales_return)
        
        layout.addWidget(self.sales_returns_table)
        
        # شريط الحالة
        status_layout = QHBoxLayout()
        
        self.sales_status_label = QLabel("جاري التحميل...")
        status_layout.addWidget(self.sales_status_label)
        
        status_layout.addStretch()
        
        self.sales_stats_label = QLabel()
        self.sales_stats_label.setStyleSheet("color: blue; font-weight: bold;")
        status_layout.addWidget(self.sales_stats_label)
        
        layout.addLayout(status_layout)
        
        tab.setLayout(layout)
        
    def setup_purchase_returns_tab(self, tab):
        """إعداد تبويب مرتجع المشتريات"""
        layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        new_return_btn = QPushButton("مرتجع جديد")
        new_return_btn.clicked.connect(self.new_purchase_return)
        toolbar_layout.addWidget(new_return_btn)
        
        view_return_btn = QPushButton("عرض المرتجع")
        view_return_btn.clicked.connect(self.view_purchase_return)
        toolbar_layout.addWidget(view_return_btn)
        
        toolbar_layout.addStretch()
        
        # البحث
        search_label = QLabel("البحث:")
        toolbar_layout.addWidget(search_label)
        
        self.purchase_search_input = QLineEdit()
        self.purchase_search_input.setPlaceholderText("رقم المرتجع أو الفاتورة الأصلية...")
        self.purchase_search_input.textChanged.connect(self.search_purchase_returns)
        toolbar_layout.addWidget(self.purchase_search_input)
        
        layout.addLayout(toolbar_layout)
        
        # جدول مرتجع المشتريات
        self.purchase_returns_table = QTableWidget()
        self.purchase_returns_table.setColumnCount(6)
        self.purchase_returns_table.setHorizontalHeaderLabels([
            "رقم المرتجع", "التاريخ", "الفاتورة الأصلية", 
            "المورد", "المبلغ", "السبب"
        ])
        
        # تنسيق الجدول
        header = self.purchase_returns_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.purchase_returns_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.purchase_returns_table.setAlternatingRowColors(True)
        self.purchase_returns_table.doubleClicked.connect(self.view_purchase_return)
        
        layout.addWidget(self.purchase_returns_table)
        
        # شريط الحالة
        status_layout = QHBoxLayout()
        
        self.purchase_status_label = QLabel("جاري التحميل...")
        status_layout.addWidget(self.purchase_status_label)
        
        status_layout.addStretch()
        
        self.purchase_stats_label = QLabel()
        self.purchase_stats_label.setStyleSheet("color: blue; font-weight: bold;")
        status_layout.addWidget(self.purchase_stats_label)
        
        layout.addLayout(status_layout)
        
        tab.setLayout(layout)
        
    def load_returns(self):
        """تحميل المرتجعات من قاعدة البيانات"""
        self.load_sales_returns()
        if self.user_data['role'] == 'admin':
            self.load_purchase_returns()
            
    def load_sales_returns(self):
        """تحميل مرتجع المبيعات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT sr.*, i.invoice_number, c.name as customer_name
                FROM sales_returns sr
                LEFT JOIN invoices i ON sr.original_invoice_id = i.id
                LEFT JOIN customers c ON sr.customer_id = c.id
                ORDER BY sr.return_date DESC
            ''')

            returns = cursor.fetchall()
            conn.close()

            # تحديث الجدول
            if hasattr(self, 'sales_returns_table'):
                self.sales_returns_table.setRowCount(len(returns))

            total_amount = 0

            for row, return_item in enumerate(returns):
                if hasattr(self, 'sales_returns_table'):
                    # إنشاء عنصر مع معرف المرتجع
                    return_number_item = QTableWidgetItem(return_item['return_number'])
                    return_number_item.setData(Qt.UserRole, return_item['id'])
                    self.sales_returns_table.setItem(row, 0, return_number_item)

                    self.sales_returns_table.setItem(row, 1, QTableWidgetItem(return_item['return_date']))
                    self.sales_returns_table.setItem(row, 2, QTableWidgetItem(return_item['invoice_number'] or ''))
                    self.sales_returns_table.setItem(row, 3, QTableWidgetItem(return_item['customer_name'] or 'عميل نقدي'))
                    self.sales_returns_table.setItem(row, 4, QTableWidgetItem(f"{return_item['total_amount']:.2f}"))
                    self.sales_returns_table.setItem(row, 5, QTableWidgetItem(return_item['notes'] or ''))

                total_amount += return_item['total_amount']

            # تحديث شريط الحالة
            if hasattr(self, 'sales_status_label'):
                self.sales_status_label.setText(f"إجمالي مرتجع المبيعات: {len(returns)}")
            if hasattr(self, 'sales_stats_label'):
                self.sales_stats_label.setText(f"إجمالي المبلغ: {total_amount:.2f} جنيه")

        except Exception as e:
            print(f"خطأ في تحميل مرتجع المبيعات: {str(e)}")
            # في حالة الخطأ، تهيئة الجدول فارغ
            if hasattr(self, 'sales_returns_table'):
                self.sales_returns_table.setRowCount(0)
        
    def load_purchase_returns(self):
        """تحميل مرتجع المشتريات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT pr.*, i.invoice_number, s.name as supplier_name
                FROM purchase_returns pr
                LEFT JOIN invoices i ON pr.original_invoice_id = i.id
                LEFT JOIN suppliers s ON pr.supplier_id = s.id
                ORDER BY pr.return_date DESC
            ''')

            returns = cursor.fetchall()
            conn.close()

            # تحديث الجدول
            if hasattr(self, 'purchase_returns_table'):
                self.purchase_returns_table.setRowCount(len(returns))

            total_amount = 0

            for row, return_item in enumerate(returns):
                if hasattr(self, 'purchase_returns_table'):
                    # إنشاء عنصر مع معرف المرتجع
                    return_number_item = QTableWidgetItem(return_item['return_number'])
                    return_number_item.setData(Qt.UserRole, return_item['id'])
                    self.purchase_returns_table.setItem(row, 0, return_number_item)

                    self.purchase_returns_table.setItem(row, 1, QTableWidgetItem(return_item['return_date']))
                    self.purchase_returns_table.setItem(row, 2, QTableWidgetItem(return_item['invoice_number'] or ''))
                    self.purchase_returns_table.setItem(row, 3, QTableWidgetItem(return_item['supplier_name'] or ''))
                    self.purchase_returns_table.setItem(row, 4, QTableWidgetItem(f"{return_item['total_amount']:.2f}"))
                    self.purchase_returns_table.setItem(row, 5, QTableWidgetItem(return_item['notes'] or ''))

                total_amount += return_item['total_amount']

            # تحديث شريط الحالة
            if hasattr(self, 'purchase_status_label'):
                self.purchase_status_label.setText(f"إجمالي مرتجع المشتريات: {len(returns)}")
            if hasattr(self, 'purchase_stats_label'):
                self.purchase_stats_label.setText(f"إجمالي المبلغ: {total_amount:.2f} جنيه")

        except Exception as e:
            print(f"خطأ في تحميل مرتجع المشتريات: {str(e)}")
            # في حالة الخطأ، تهيئة الجدول فارغ
            if hasattr(self, 'purchase_returns_table'):
                self.purchase_returns_table.setRowCount(0)
        
    def search_sales_returns(self):
        """البحث في مرتجع المبيعات"""
        search_text = self.sales_search_input.text().lower()
        
        for row in range(self.sales_returns_table.rowCount()):
            show_row = False
            
            # البحث في رقم المرتجع والفاتورة الأصلية
            return_number_item = self.sales_returns_table.item(row, 0)
            invoice_number_item = self.sales_returns_table.item(row, 2)
            
            if return_number_item and search_text in return_number_item.text().lower():
                show_row = True
            elif invoice_number_item and search_text in invoice_number_item.text().lower():
                show_row = True
                
            self.sales_returns_table.setRowHidden(row, not show_row)
            
    def search_purchase_returns(self):
        """البحث في مرتجع المشتريات"""
        search_text = self.purchase_search_input.text().lower()
        
        for row in range(self.purchase_returns_table.rowCount()):
            show_row = False
            
            # البحث في رقم المرتجع والفاتورة الأصلية
            return_number_item = self.purchase_returns_table.item(row, 0)
            invoice_number_item = self.purchase_returns_table.item(row, 2)
            
            if return_number_item and search_text in return_number_item.text().lower():
                show_row = True
            elif invoice_number_item and search_text in invoice_number_item.text().lower():
                show_row = True
                
            self.purchase_returns_table.setRowHidden(row, not show_row)
            
    def new_sales_return(self):
        """إنشاء مرتجع مبيعات جديد"""
        try:
            # التأكد من تهيئة قاعدة البيانات أولاً
            self.ensure_returns_tables()

            dialog = SalesReturnDialog(self.db_manager, self.user_data, parent=self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_sales_returns()
        except Exception as e:
            print(f"خطأ في إنشاء مرتجع مبيعات: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في إنشاء مرتجع مبيعات:\n{str(e)}\n\nيرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني.")
            
    def new_purchase_return(self):
        """إنشاء مرتجع مشتريات جديد"""
        try:
            # التأكد من تهيئة قاعدة البيانات أولاً
            self.ensure_returns_tables()

            dialog = PurchaseReturnDialog(self.db_manager, self.user_data, parent=self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_purchase_returns()
        except Exception as e:
            print(f"خطأ في إنشاء مرتجع مشتريات: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في إنشاء مرتجع مشتريات:\n{str(e)}\n\nيرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني.")
            
    def view_sales_return(self):
        """عرض مرتجع مبيعات"""
        current_row = self.sales_returns_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مرتجع للعرض")
            return

        return_id = self.sales_returns_table.item(current_row, 0).data(Qt.UserRole)
        if return_id:
            dialog = ViewReturnDialog(self.db_manager, return_id, 'sales', self.user_data, self)
            dialog.exec_()
        
    def view_purchase_return(self):
        """عرض مرتجع مشتريات"""
        current_row = self.purchase_returns_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مرتجع للعرض")
            return

        return_id = self.purchase_returns_table.item(current_row, 0).data(Qt.UserRole)
        if return_id:
            dialog = ViewReturnDialog(self.db_manager, return_id, 'purchase', self.user_data, self)
            dialog.exec_()

    def ensure_returns_tables(self):
        """التأكد من وجود جداول المرتجعات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # جدول مرتجعات المبيعات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sales_returns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    return_number TEXT UNIQUE NOT NULL,
                    return_date TEXT NOT NULL,
                    original_invoice_id INTEGER NOT NULL,
                    customer_id INTEGER,
                    total_amount REAL NOT NULL DEFAULT 0,
                    notes TEXT,
                    user_id INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (original_invoice_id) REFERENCES invoices (id),
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')

            # جدول تفاصيل مرتجعات المبيعات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sales_return_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    return_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    quantity REAL NOT NULL,
                    unit_price REAL NOT NULL,
                    total_price REAL NOT NULL,
                    reason TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (return_id) REFERENCES sales_returns (id) ON DELETE CASCADE,
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            ''')

            # جدول مرتجعات المشتريات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS purchase_returns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    return_number TEXT UNIQUE NOT NULL,
                    return_date TEXT NOT NULL,
                    original_invoice_id INTEGER NOT NULL,
                    supplier_id INTEGER,
                    total_amount REAL NOT NULL DEFAULT 0,
                    notes TEXT,
                    user_id INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (original_invoice_id) REFERENCES invoices (id),
                    FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')

            # جدول تفاصيل مرتجعات المشتريات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS purchase_return_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    return_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    quantity REAL NOT NULL,
                    unit_price REAL NOT NULL,
                    total_price REAL NOT NULL,
                    reason TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (return_id) REFERENCES purchase_returns (id) ON DELETE CASCADE,
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            ''')

            conn.commit()
            conn.close()
            print("✅ تم التأكد من وجود جداول المرتجعات")

        except Exception as e:
            print(f"خطأ في التأكد من جداول المرتجعات: {str(e)}")


class SalesReturnDialog(QDialog):
    """نافذة حوار مرتجع المبيعات"""
    
    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        self.return_items = []
        self.original_invoice = None
        
        # Import Config and datetime locally
        try:
            from utils.config import Config
            self.Config = Config
        except ImportError:
            class Config:
                RETURN_PREFIX_SALE = "SR"
                RETURN_PREFIX_PURCHASE = "PR"
            self.Config = Config
            
        from datetime import datetime
        self.datetime = datetime

        try:
            self.init_ui()
        except Exception as e:
            print(f"خطأ في تهيئة نافذة مرتجع المبيعات: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تهيئة النافذة:\n{str(e)}")

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("مرتجع مبيعات جديد")
        self.setFixedSize(800, 600)

        layout = QVBoxLayout()

        # معلومات المرتجع
        info_group = QGroupBox("معلومات المرتجع")
        info_layout = QFormLayout()

        # رقم المرتجع
        self.return_number_input = QLineEdit()
        self.return_number_input.setText(self.generate_return_number())
        self.return_number_input.setReadOnly(True)
        info_layout.addRow("رقم المرتجع:", self.return_number_input)

        # تاريخ المرتجع
        self.return_date = QDateEdit()
        self.return_date.setDate(QDate.currentDate())
        self.return_date.setCalendarPopup(True)
        info_layout.addRow("تاريخ المرتجع:", self.return_date)

        # الفاتورة الأصلية
        invoice_layout = QHBoxLayout()
        self.invoice_input = QLineEdit()
        self.invoice_input.setPlaceholderText("رقم الفاتورة الأصلية")
        invoice_layout.addWidget(self.invoice_input)

        search_invoice_btn = QPushButton("بحث")
        search_invoice_btn.clicked.connect(self.search_invoice)
        invoice_layout.addWidget(search_invoice_btn)

        info_layout.addRow("الفاتورة الأصلية:", invoice_layout)

        # العميل
        self.customer_label = QLabel("لم يتم تحديد العميل")
        info_layout.addRow("العميل:", self.customer_label)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        # أصناف المرتجع
        items_group = QGroupBox("أصناف المرتجع")
        items_layout = QVBoxLayout()

        # جدول الأصناف
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(6)
        self.items_table.setHorizontalHeaderLabels([
            "الصنف", "الكمية الأصلية", "الكمية المرتجعة", "السعر", "الإجمالي", "السبب"
        ])
        self.items_table.horizontalHeader().setStretchLastSection(True)
        items_layout.addWidget(self.items_table)

        items_group.setLayout(items_layout)
        layout.addWidget(items_group)

        # الإجماليات
        totals_group = QGroupBox("الإجماليات")
        totals_layout = QFormLayout()

        self.total_amount_label = QLabel("0.00")
        totals_layout.addRow("إجمالي المرتجع:", self.total_amount_label)

        totals_group.setLayout(totals_layout)
        layout.addWidget(totals_group)

        # ملاحظات
        notes_group = QGroupBox("ملاحظات")
        notes_layout = QVBoxLayout()

        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("ملاحظات إضافية...")
        notes_layout.addWidget(self.notes_input)

        notes_group.setLayout(notes_layout)
        layout.addWidget(notes_group)

        # الأزرار
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("حفظ المرتجع")
        save_btn.clicked.connect(self.save_return)
        buttons_layout.addWidget(save_btn)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def generate_return_number(self):
        """توليد رقم مرتجع جديد"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            today = self.datetime.now().strftime("%Y%m%d")

            # التأكد من وجود جدول sales_returns
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sales_returns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    return_number TEXT UNIQUE NOT NULL,
                    return_date TEXT NOT NULL,
                    original_invoice_id INTEGER NOT NULL,
                    customer_id INTEGER,
                    total_amount REAL NOT NULL DEFAULT 0,
                    notes TEXT,
                    user_id INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (original_invoice_id) REFERENCES invoices (id),
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')

            cursor.execute('''
                SELECT COUNT(*) FROM sales_returns
                WHERE return_number LIKE ?
            ''', (f"{self.Config.RETURN_PREFIX_SALE}{today}%",))

            count = cursor.fetchone()[0] + 1
            conn.close()

            return f"{self.Config.RETURN_PREFIX_SALE}{today}{count:03d}"

        except Exception as e:
            print(f"خطأ في توليد رقم المرتجع: {str(e)}")
            # في حالة الخطأ، إرجاع رقم افتراضي
            today = self.datetime.now().strftime("%Y%m%d")
            return f"{self.Config.RETURN_PREFIX_SALE}{today}001"

    def search_invoice(self):
        """البحث عن الفاتورة الأصلية"""
        invoice_number = self.invoice_input.text().strip()
        if not invoice_number:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم الفاتورة")
            return

        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        # البحث عن الفاتورة
        cursor.execute('''
            SELECT i.*, c.name as customer_name
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE i.invoice_number = ? AND i.invoice_type = 'sale'
        ''', (invoice_number,))

        invoice = cursor.fetchone()

        if not invoice:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة")
            conn.close()
            return

        # تحميل أصناف الفاتورة
        cursor.execute('''
            SELECT ii.*, p.name as product_name
            FROM invoice_items ii
            JOIN products p ON ii.product_id = p.id
            WHERE ii.invoice_id = ?
        ''', (invoice['id'],))

        items = cursor.fetchall()
        conn.close()

        # تحديث الواجهة
        self.original_invoice = invoice
        self.customer_label.setText(invoice['customer_name'] or "عميل غير محدد")

        # تحميل الأصناف في الجدول
        self.load_invoice_items(items)

    def load_invoice_items(self, items):
        """تحميل أصناف الفاتورة في الجدول"""
        self.items_table.setRowCount(len(items))

        for row, item in enumerate(items):
            # اسم الصنف
            self.items_table.setItem(row, 0, QTableWidgetItem(item['product_name']))

            # الكمية الأصلية
            self.items_table.setItem(row, 1, QTableWidgetItem(str(item['quantity'])))

            # الكمية المرتجعة (قابلة للتعديل)
            return_qty = QSpinBox()
            return_qty.setMaximum(int(item['quantity']))
            return_qty.valueChanged.connect(self.calculate_totals)
            self.items_table.setCellWidget(row, 2, return_qty)

            # السعر
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{item['unit_price']:.2f}"))

            # الإجمالي (سيتم حسابه)
            self.items_table.setItem(row, 4, QTableWidgetItem("0.00"))

            # السبب (قابل للتعديل)
            reason_combo = QComboBox()
            reason_combo.addItems([
                "عيب في المنتج",
                "منتج خاطئ",
                "تلف أثناء النقل",
                "عدم رضا العميل",
                "انتهاء صلاحية",
                "أخرى"
            ])
            self.items_table.setCellWidget(row, 5, reason_combo)

            # حفظ معرف الصنف
            item_widget = QTableWidgetItem()
            item_widget.setData(Qt.UserRole, {
                'product_id': item['product_id'],
                'unit_price': item['unit_price'],
                'original_quantity': item['quantity']
            })
            self.items_table.setItem(row, 0, item_widget)

    def calculate_totals(self):
        """حساب الإجماليات"""
        total = 0.0

        for row in range(self.items_table.rowCount()):
            return_qty_widget = self.items_table.cellWidget(row, 2)
            if return_qty_widget:
                return_qty = return_qty_widget.value()
                price_item = self.items_table.item(row, 3)
                if price_item:
                    price = float(price_item.text())
                    row_total = return_qty * price

                    # تحديث إجمالي الصف
                    self.items_table.setItem(row, 4, QTableWidgetItem(f"{row_total:.2f}"))
                    total += row_total

        self.total_amount_label.setText(f"{total:.2f}")

    def save_return(self):
        """حفظ المرتجع"""
        if not self.original_invoice:
            QMessageBox.warning(self, "خطأ", "يجب تحديد الفاتورة الأصلية أولاً")
            return

        # التحقق من وجود أصناف للإرجاع
        has_returns = False
        for row in range(self.items_table.rowCount()):
            return_qty_widget = self.items_table.cellWidget(row, 2)
            if return_qty_widget and return_qty_widget.value() > 0:
                has_returns = True
                break

        if not has_returns:
            QMessageBox.warning(self, "خطأ", "يجب تحديد كمية للإرجاع لصنف واحد على الأقل")
            return

        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # إدراج المرتجع الرئيسي
            cursor.execute('''
                INSERT INTO sales_returns
                (return_number, return_date, original_invoice_id, customer_id,
                 total_amount, notes, user_id, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
            ''', (
                self.return_number_input.text(),
                self.return_date.date().toString('yyyy-MM-dd'),
                self.original_invoice['id'],
                self.original_invoice['customer_id'],
                float(self.total_amount_label.text()),
                self.notes_input.toPlainText(),
                self.user_data['id']
            ))

            return_id = cursor.lastrowid

            # إدراج أصناف المرتجع
            for row in range(self.items_table.rowCount()):
                return_qty_widget = self.items_table.cellWidget(row, 2)
                reason_widget = self.items_table.cellWidget(row, 5)

                if return_qty_widget and return_qty_widget.value() > 0:
                    item_data = self.items_table.item(row, 0).data(Qt.UserRole)

                    cursor.execute('''
                        INSERT INTO sales_return_items
                        (return_id, product_id, quantity, unit_price, total_price, reason)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        return_id,
                        item_data['product_id'],
                        return_qty_widget.value(),
                        item_data['unit_price'],
                        return_qty_widget.value() * item_data['unit_price'],
                        reason_widget.currentText()
                    ))

                    # تحديث المخزون (إضافة الكمية المرتجعة)
                    cursor.execute('''
                        UPDATE products
                        SET stock_quantity = stock_quantity + ?
                        WHERE id = ?
                    ''', (return_qty_widget.value(), item_data['product_id']))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم حفظ المرتجع بنجاح")
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ المرتجع:\n{str(e)}")

class PurchaseReturnDialog(QDialog):
    """نافذة حوار مرتجع المشتريات"""

    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("مرتجع مشتريات جديد")
        self.setFixedSize(800, 600)

        layout = QVBoxLayout()

        # معلومات المرتجع
        info_group = QGroupBox("معلومات المرتجع")
        info_layout = QFormLayout()

        # رقم المرتجع
        self.return_number_input = QLineEdit()
        self.return_number_input.setText(self.generate_return_number())
        self.return_number_input.setReadOnly(True)
        info_layout.addRow("رقم المرتجع:", self.return_number_input)

        # تاريخ المرتجع
        self.return_date = QDateEdit()
        self.return_date.setDate(QDate.currentDate())
        self.return_date.setCalendarPopup(True)
        info_layout.addRow("تاريخ المرتجع:", self.return_date)

        # الفاتورة الأصلية
        invoice_layout = QHBoxLayout()
        self.invoice_input = QLineEdit()
        self.invoice_input.setPlaceholderText("رقم فاتورة المشتريات الأصلية")
        invoice_layout.addWidget(self.invoice_input)

        search_invoice_btn = QPushButton("بحث")
        search_invoice_btn.clicked.connect(self.search_invoice)
        invoice_layout.addWidget(search_invoice_btn)

        info_layout.addRow("الفاتورة الأصلية:", invoice_layout)

        # المورد
        self.supplier_label = QLabel("لم يتم تحديد المورد")
        info_layout.addRow("المورد:", self.supplier_label)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        # أصناف المرتجع
        items_group = QGroupBox("أصناف المرتجع")
        items_layout = QVBoxLayout()

        # جدول الأصناف
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(6)
        self.items_table.setHorizontalHeaderLabels([
            "الصنف", "الكمية الأصلية", "الكمية المرتجعة", "السعر", "الإجمالي", "السبب"
        ])
        self.items_table.horizontalHeader().setStretchLastSection(True)
        items_layout.addWidget(self.items_table)

        items_group.setLayout(items_layout)
        layout.addWidget(items_group)

        # الإجماليات
        totals_group = QGroupBox("الإجماليات")
        totals_layout = QFormLayout()

        self.total_amount_label = QLabel("0.00")
        totals_layout.addRow("إجمالي المرتجع:", self.total_amount_label)

        totals_group.setLayout(totals_layout)
        layout.addWidget(totals_group)

        # ملاحظات
        notes_group = QGroupBox("ملاحظات")
        notes_layout = QVBoxLayout()

        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("ملاحظات إضافية...")
        notes_layout.addWidget(self.notes_input)

        notes_group.setLayout(notes_layout)
        layout.addWidget(notes_group)

        # الأزرار
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("حفظ المرتجع")
        save_btn.clicked.connect(self.save_return)
        buttons_layout.addWidget(save_btn)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def generate_return_number(self):
        """توليد رقم مرتجع جديد"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            today = datetime.now().strftime("%Y%m%d")

            # التأكد من وجود جدول purchase_returns
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS purchase_returns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    return_number TEXT UNIQUE NOT NULL,
                    return_date TEXT NOT NULL,
                    original_invoice_id INTEGER NOT NULL,
                    supplier_id INTEGER,
                    total_amount REAL NOT NULL DEFAULT 0,
                    notes TEXT,
                    user_id INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (original_invoice_id) REFERENCES invoices (id),
                    FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')

            cursor.execute('''
                SELECT COUNT(*) FROM purchase_returns
                WHERE return_number LIKE ?
            ''', (f"PR{today}%",))

            count = cursor.fetchone()[0] + 1
            conn.close()

            return f"PR{today}{count:03d}"

        except Exception as e:
            print(f"خطأ في توليد رقم مرتجع المشتريات: {str(e)}")
            # في حالة الخطأ، إرجاع رقم افتراضي
            today = datetime.now().strftime("%Y%m%d")
            return f"PR{today}001"

    def search_invoice(self):
        """البحث عن الفاتورة الأصلية"""
        invoice_number = self.invoice_input.text().strip()
        if not invoice_number:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم الفاتورة")
            return

        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        # البحث عن الفاتورة
        cursor.execute('''
            SELECT i.*, s.name as supplier_name
            FROM invoices i
            LEFT JOIN suppliers s ON i.supplier_id = s.id
            WHERE i.invoice_number = ? AND i.invoice_type = 'purchase'
        ''', (invoice_number,))

        invoice = cursor.fetchone()

        if not invoice:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة")
            conn.close()
            return

        # تحميل أصناف الفاتورة
        cursor.execute('''
            SELECT ii.*, p.name as product_name
            FROM invoice_items ii
            JOIN products p ON ii.product_id = p.id
            WHERE ii.invoice_id = ?
        ''', (invoice['id'],))

        items = cursor.fetchall()
        conn.close()

        # تحديث الواجهة
        self.original_invoice = invoice
        self.supplier_label.setText(invoice['supplier_name'] or "مورد غير محدد")

        # تحميل الأصناف في الجدول
        self.load_invoice_items(items)

    def load_invoice_items(self, items):
        """تحميل أصناف الفاتورة في الجدول"""
        self.items_table.setRowCount(len(items))

        for row, item in enumerate(items):
            # اسم الصنف
            self.items_table.setItem(row, 0, QTableWidgetItem(item['product_name']))

            # الكمية الأصلية
            self.items_table.setItem(row, 1, QTableWidgetItem(str(item['quantity'])))

            # الكمية المرتجعة (قابلة للتعديل)
            return_qty = QSpinBox()
            return_qty.setMaximum(int(item['quantity']))
            return_qty.valueChanged.connect(self.calculate_totals)
            self.items_table.setCellWidget(row, 2, return_qty)

            # السعر
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{item['unit_price']:.2f}"))

            # الإجمالي (سيتم حسابه)
            self.items_table.setItem(row, 4, QTableWidgetItem("0.00"))

            # السبب (قابل للتعديل)
            reason_combo = QComboBox()
            reason_combo.addItems([
                "عيب في المنتج",
                "منتج خاطئ",
                "تلف أثناء النقل",
                "عدم مطابقة المواصفات",
                "انتهاء صلاحية",
                "أخرى"
            ])
            self.items_table.setCellWidget(row, 5, reason_combo)

            # حفظ معرف الصنف
            item_widget = QTableWidgetItem()
            item_widget.setData(Qt.UserRole, {
                'product_id': item['product_id'],
                'unit_price': item['unit_price'],
                'original_quantity': item['quantity']
            })
            self.items_table.setItem(row, 0, item_widget)

    def calculate_totals(self):
        """حساب الإجماليات"""
        total = 0.0

        for row in range(self.items_table.rowCount()):
            return_qty_widget = self.items_table.cellWidget(row, 2)
            if return_qty_widget:
                return_qty = return_qty_widget.value()
                price_item = self.items_table.item(row, 3)
                if price_item:
                    price = float(price_item.text())
                    row_total = return_qty * price

                    # تحديث إجمالي الصف
                    self.items_table.setItem(row, 4, QTableWidgetItem(f"{row_total:.2f}"))
                    total += row_total

        self.total_amount_label.setText(f"{total:.2f}")

    def save_return(self):
        """حفظ المرتجع"""
        if not hasattr(self, 'original_invoice') or not self.original_invoice:
            QMessageBox.warning(self, "خطأ", "يجب تحديد الفاتورة الأصلية أولاً")
            return

        # التحقق من وجود أصناف للإرجاع
        has_returns = False
        for row in range(self.items_table.rowCount()):
            return_qty_widget = self.items_table.cellWidget(row, 2)
            if return_qty_widget and return_qty_widget.value() > 0:
                has_returns = True
                break

        if not has_returns:
            QMessageBox.warning(self, "خطأ", "يجب تحديد كمية للإرجاع لصنف واحد على الأقل")
            return

        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # إدراج المرتجع الرئيسي
            cursor.execute('''
                INSERT INTO purchase_returns
                (return_number, return_date, original_invoice_id, supplier_id,
                 total_amount, notes, user_id, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
            ''', (
                self.return_number_input.text(),
                self.return_date.date().toString('yyyy-MM-dd'),
                self.original_invoice['id'],
                self.original_invoice['supplier_id'],
                float(self.total_amount_label.text()),
                self.notes_input.toPlainText(),
                self.user_data['id']
            ))

            return_id = cursor.lastrowid

            # إدراج أصناف المرتجع
            for row in range(self.items_table.rowCount()):
                return_qty_widget = self.items_table.cellWidget(row, 2)
                reason_widget = self.items_table.cellWidget(row, 5)

                if return_qty_widget and return_qty_widget.value() > 0:
                    item_data = self.items_table.item(row, 0).data(Qt.UserRole)

                    cursor.execute('''
                        INSERT INTO purchase_return_items
                        (return_id, product_id, quantity, unit_price, total_price, reason)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        return_id,
                        item_data['product_id'],
                        return_qty_widget.value(),
                        item_data['unit_price'],
                        return_qty_widget.value() * item_data['unit_price'],
                        reason_widget.currentText()
                    ))

                    # تحديث المخزون (خصم الكمية المرتجعة)
                    cursor.execute('''
                        UPDATE products
                        SET stock_quantity = stock_quantity - ?
                        WHERE id = ?
                    ''', (return_qty_widget.value(), item_data['product_id']))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", "تم حفظ المرتجع بنجاح")
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ المرتجع:\n{str(e)}")


class ViewReturnDialog(QDialog):
    """نافذة عرض تفاصيل المرتجع"""

    def __init__(self, db_manager, return_id, return_type, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.return_id = return_id
        self.return_type = return_type  # 'sales' or 'purchase'
        self.user_data = user_data
        self.init_ui()
        self.load_return_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        title = "عرض مرتجع المبيعات" if self.return_type == 'sales' else "عرض مرتجع المشتريات"
        self.setWindowTitle(title)
        self.setFixedSize(700, 500)

        layout = QVBoxLayout()

        # معلومات المرتجع
        info_group = QGroupBox("معلومات المرتجع")
        info_layout = QFormLayout()

        self.return_number_label = QLabel()
        info_layout.addRow("رقم المرتجع:", self.return_number_label)

        self.return_date_label = QLabel()
        info_layout.addRow("تاريخ المرتجع:", self.return_date_label)

        self.invoice_number_label = QLabel()
        info_layout.addRow("الفاتورة الأصلية:", self.invoice_number_label)

        self.client_label = QLabel()
        client_text = "العميل:" if self.return_type == 'sales' else "المورد:"
        info_layout.addRow(client_text, self.client_label)

        self.total_amount_label = QLabel()
        info_layout.addRow("إجمالي المرتجع:", self.total_amount_label)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        # أصناف المرتجع
        items_group = QGroupBox("أصناف المرتجع")
        items_layout = QVBoxLayout()

        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels([
            "الصنف", "الكمية المرتجعة", "السعر", "الإجمالي", "السبب"
        ])
        self.items_table.horizontalHeader().setStretchLastSection(True)
        items_layout.addWidget(self.items_table)

        items_group.setLayout(items_layout)
        layout.addWidget(items_group)

        # ملاحظات
        notes_group = QGroupBox("ملاحظات")
        notes_layout = QVBoxLayout()

        self.notes_label = QLabel()
        self.notes_label.setWordWrap(True)
        self.notes_label.setStyleSheet("padding: 10px; background-color: #f5f5f5; border: 1px solid #ddd;")
        notes_layout.addWidget(self.notes_label)

        notes_group.setLayout(notes_layout)
        layout.addWidget(notes_group)

        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)

        self.setLayout(layout)

    def load_return_data(self):
        """تحميل بيانات المرتجع"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # تحديد الجدول والاستعلام حسب نوع المرتجع
            if self.return_type == 'sales':
                cursor.execute('''
                    SELECT sr.*, i.invoice_number, c.name as client_name
                    FROM sales_returns sr
                    LEFT JOIN invoices i ON sr.original_invoice_id = i.id
                    LEFT JOIN customers c ON sr.customer_id = c.id
                    WHERE sr.id = ?
                ''', (self.return_id,))
            else:
                cursor.execute('''
                    SELECT pr.*, i.invoice_number, s.name as client_name
                    FROM purchase_returns pr
                    LEFT JOIN invoices i ON pr.original_invoice_id = i.id
                    LEFT JOIN suppliers s ON pr.supplier_id = s.id
                    WHERE pr.id = ?
                ''', (self.return_id,))

            return_data = cursor.fetchone()

            if not return_data:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على المرتجع")
                conn.close()
                return

            # تحميل أصناف المرتجع
            if self.return_type == 'sales':
                cursor.execute('''
                    SELECT sri.*, p.name as product_name
                    FROM sales_return_items sri
                    JOIN products p ON sri.product_id = p.id
                    WHERE sri.return_id = ?
                ''', (self.return_id,))
            else:
                cursor.execute('''
                    SELECT pri.*, p.name as product_name
                    FROM purchase_return_items pri
                    JOIN products p ON pri.product_id = p.id
                    WHERE pri.return_id = ?
                ''', (self.return_id,))

            items = cursor.fetchall()
            conn.close()

            # تحديث الواجهة
            self.return_number_label.setText(return_data['return_number'])
            self.return_date_label.setText(return_data['return_date'])
            self.invoice_number_label.setText(return_data['invoice_number'] or "غير محدد")
            self.client_label.setText(return_data['client_name'] or "غير محدد")
            self.total_amount_label.setText(f"{return_data['total_amount']:.2f}")
            self.notes_label.setText(return_data['notes'] or "لا توجد ملاحظات")

            # تحميل الأصناف
            self.load_items(items)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات المرتجع:\n{str(e)}")

    def load_items(self, items):
        """تحميل أصناف المرتجع في الجدول"""
        self.items_table.setRowCount(len(items))

        for row, item in enumerate(items):
            self.items_table.setItem(row, 0, QTableWidgetItem(item['product_name']))
            self.items_table.setItem(row, 1, QTableWidgetItem(str(item['quantity'])))
            self.items_table.setItem(row, 2, QTableWidgetItem(f"{item['unit_price']:.2f}"))
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{item['total_price']:.2f}"))
            self.items_table.setItem(row, 4, QTableWidgetItem(item['reason'] or "غير محدد"))
