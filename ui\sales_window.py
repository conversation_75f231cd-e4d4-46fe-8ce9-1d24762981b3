#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة فواتير البيع - نظام نقاط البيع والمحاسبة
Sales Invoices Window - POS and Accounting System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QComboBox, QDialog, QFormLayout,
                            QMessageBox, QHeaderView, QAbstractItemView,
                            QFrame, QGroupBox, QSpinBox, QDoubleSpinBox,
                            QDateEdit, QTextEdit, QCheckBox, QSplitter,
                            QGridLayout, QCompleter)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTimer
from PyQt5.QtGui import QFont, QColor
from utils.config import Config
from datetime import datetime
import uuid

class SalesWindow(QWidget):
    """نافذة فواتير البيع"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        self.load_invoices()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("فواتير البيع")
        self.setGeometry(100, 100, 1200, 700)

        # تطبيق التصميم العصري
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                font-family: 'Segoe UI', Arial, sans-serif;
                color: #2c3e50;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f1f3f4);
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px 15px;
                font-size: 13px;
                font-weight: 500;
                color: #2c3e50;
                min-height: 25px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-color: #667eea;
                color: white;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5a67d8, stop:1 #6b46c1);
            }
            QTableWidget {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f0f0f0;
                selection-background-color: #667eea;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #667eea;
                color: white;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: black;
                padding: 10px;
                border: none;
                font-weight: 600;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
                background-color: white;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
                border-color: #667eea;
                outline: none;
            }
            QGroupBox {
                font-weight: 600;
                font-size: 14px;
                color: #2c3e50;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                background-color: #f8f9fa;
            }
        """)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        new_invoice_btn = QPushButton("فاتورة جديدة")
        new_invoice_btn.clicked.connect(self.new_invoice)
        toolbar_layout.addWidget(new_invoice_btn)
        
        view_invoice_btn = QPushButton("عرض الفاتورة")
        view_invoice_btn.clicked.connect(self.view_invoice)
        toolbar_layout.addWidget(view_invoice_btn)

        preview_invoice_btn = QPushButton("👁️ معاينة الفاتورة")
        preview_invoice_btn.clicked.connect(self.preview_invoice_from_list)
        toolbar_layout.addWidget(preview_invoice_btn)

        print_invoice_btn = QPushButton("🖨️ طباعة الفاتورة")
        print_invoice_btn.clicked.connect(self.print_invoice)
        toolbar_layout.addWidget(print_invoice_btn)
        
        toolbar_layout.addStretch()
        
        # البحث
        search_label = QLabel("البحث:")
        toolbar_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("رقم الفاتورة أو اسم العميل...")
        self.search_input.textChanged.connect(self.search_invoices)
        toolbar_layout.addWidget(self.search_input)
        
        main_layout.addLayout(toolbar_layout)
        
        # جدول الفواتير
        self.invoices_table = QTableWidget()
        self.invoices_table.setColumnCount(8)
        self.invoices_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "التاريخ", "العميل", "المبلغ الإجمالي",
            "المدفوع", "المتبقي", "طريقة الدفع", "الحالة"
        ])
        
        # تنسيق الجدول
        header = self.invoices_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.invoices_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.invoices_table.setAlternatingRowColors(True)
        self.invoices_table.doubleClicked.connect(self.view_invoice)
        
        main_layout.addWidget(self.invoices_table)
        
        # شريط الحالة
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("جاري التحميل...")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # إحصائيات سريعة
        self.stats_label = QLabel()
        self.stats_label.setStyleSheet("color: blue; font-weight: bold;")
        status_layout.addWidget(self.stats_label)
        
        main_layout.addLayout(status_layout)
        
        self.setLayout(main_layout)
        
    def load_invoices(self):
        """تحميل الفواتير من قاعدة البيانات"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT i.*, c.name as customer_name
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE i.invoice_type = 'sale' AND i.is_active = 1
            ORDER BY i.invoice_date DESC
        ''')
        
        invoices = cursor.fetchall()
        conn.close()
        
        # تحديث الجدول
        self.invoices_table.setRowCount(len(invoices))
        
        total_amount = 0
        total_paid = 0
        total_remaining = 0
        
        for row, invoice in enumerate(invoices):
            self.invoices_table.setItem(row, 0, QTableWidgetItem(invoice['invoice_number']))
            self.invoices_table.setItem(row, 1, QTableWidgetItem(invoice['invoice_date']))
            self.invoices_table.setItem(row, 2, QTableWidgetItem(invoice['customer_name'] or 'عميل نقدي'))
            self.invoices_table.setItem(row, 3, QTableWidgetItem(f"{invoice['final_amount']:.2f}"))
            self.invoices_table.setItem(row, 4, QTableWidgetItem(f"{invoice['paid_amount']:.2f}"))
            
            # تلوين المتبقي
            remaining_item = QTableWidgetItem(f"{invoice['remaining_amount']:.2f}")
            if invoice['remaining_amount'] > 0:
                remaining_item.setBackground(QColor(255, 200, 200))  # أحمر للمتبقي
            self.invoices_table.setItem(row, 5, remaining_item)
            
            payment_method = Config.PAYMENT_METHODS.get(invoice['payment_method'], invoice['payment_method'])
            self.invoices_table.setItem(row, 6, QTableWidgetItem(payment_method))
            
            status = "مدفوعة" if invoice['remaining_amount'] == 0 else "غير مدفوعة"
            self.invoices_table.setItem(row, 7, QTableWidgetItem(status))
            
            total_amount += invoice['final_amount']
            total_paid += invoice['paid_amount']
            total_remaining += invoice['remaining_amount']
            
        # تحديث شريط الحالة
        self.status_label.setText(f"إجمالي الفواتير: {len(invoices)}")
        self.stats_label.setText(f"المبيعات: {total_amount:.2f} | المدفوع: {total_paid:.2f} | المتبقي: {total_remaining:.2f}")
        
    def search_invoices(self):
        """البحث في الفواتير"""
        search_text = self.search_input.text().lower()
        
        for row in range(self.invoices_table.rowCount()):
            show_row = False
            
            # البحث في رقم الفاتورة واسم العميل
            invoice_number_item = self.invoices_table.item(row, 0)
            customer_name_item = self.invoices_table.item(row, 2)
            
            if invoice_number_item and search_text in invoice_number_item.text().lower():
                show_row = True
            elif customer_name_item and search_text in customer_name_item.text().lower():
                show_row = True
                
            self.invoices_table.setRowHidden(row, not show_row)
            
    def new_invoice(self):
        """إنشاء فاتورة جديدة"""
        dialog = SalesInvoiceDialog(self.db_manager, self.user_data, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_invoices()
            
    def view_invoice(self):
        """عرض فاتورة"""
        current_row = self.invoices_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للعرض")
            return
            
        invoice_number = self.invoices_table.item(current_row, 0).text()
        
        # البحث عن الفاتورة
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM invoices 
            WHERE invoice_number = ? AND invoice_type = 'sale'
        ''', (invoice_number,))
        
        invoice = cursor.fetchone()
        conn.close()
        
        if invoice:
            dialog = SalesInvoiceDialog(self.db_manager, self.user_data, 
                                      invoice['id'], parent=self)
            dialog.exec_()
            
    def print_invoice(self):
        """طباعة فاتورة"""
        current_row = self.invoices_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للطباعة")
            return

        invoice_number = self.invoices_table.item(current_row, 0).text()

        # البحث عن الفاتورة
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id FROM invoices
            WHERE invoice_number = ? AND invoice_type = 'sale'
        ''', (invoice_number,))

        invoice = cursor.fetchone()
        conn.close()

        if invoice:
            from ui.professional_printer import ProfessionalPrinter
            printer_dialog = ProfessionalPrinter(self.db_manager, invoice['id'], 'sale', self)
            printer_dialog.exec_()

    def preview_invoice_from_list(self):
        """معاينة فاتورة من القائمة"""
        current_row = self.invoices_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للمعاينة")
            return

        invoice_number = self.invoices_table.item(current_row, 0).text()

        # البحث عن الفاتورة
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id FROM invoices
            WHERE invoice_number = ? AND invoice_type = 'sale'
        ''', (invoice_number,))

        invoice = cursor.fetchone()
        conn.close()

        if invoice:
            from ui.invoice_preview import InvoicePreview
            preview_dialog = InvoicePreview(self.db_manager, invoice['id'], 'sale', self)
            preview_dialog.exec_()


class SalesInvoiceDialog(QDialog):
    """نافذة حوار فاتورة البيع"""

    # إشارات للفواتير المتعددة
    invoice_saved = pyqtSignal()
    invoice_printed = pyqtSignal()

    def __init__(self, db_manager, user_data, invoice_id=None, parent=None, multi_mode=False):
        super().__init__(parent)
        self.db_manager = db_manager
        self.multi_mode = multi_mode  # وضع الفواتير المتعددة
        self.user_data = user_data
        self.invoice_id = invoice_id
        self.invoice_items = []
        self.init_ui()

        if invoice_id:
            self.load_invoice_data()
        else:
            self.generate_invoice_number()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        if self.multi_mode:
            title = "فاتورة بيع"
            # في وضع الفواتير المتعددة، لا نحتاج لتعيين الحجم والموقع
        else:
            title = "عرض فاتورة البيع" if self.invoice_id else "فاتورة بيع جديدة"
            self.setGeometry(150, 150, 1200, 800)

        self.setWindowTitle(title)

        # إضافة اختصارات لوحة المفاتيح
        from PyQt5.QtWidgets import QShortcut
        from PyQt5.QtGui import QKeySequence

        # اختصار F11 لملء الشاشة
        self.fullscreen_shortcut = QShortcut(QKeySequence("F11"), self)
        self.fullscreen_shortcut.activated.connect(self.toggle_fullscreen)

        # اختصار Ctrl+M لتكبير النافذة
        self.maximize_shortcut = QShortcut(QKeySequence("Ctrl+M"), self)
        self.maximize_shortcut.activated.connect(self.toggle_maximize)

        # تعيين الألوان المطابقة للصورة
        self.setStyleSheet("""
            QWidget {
                background-color: #c8e6c9;
                font-family: 'Arial';
                font-size: 11px;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #81c784;
                border-radius: 3px;
                margin: 2px;
                padding-top: 8px;
                background-color: #e8f5e8;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 3px 0 3px;
                color: #2e7d32;
                font-size: 12px;
                font-weight: bold;
            }
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
                border: 1px solid #a5d6a7;
                border-radius: 2px;
                padding: 3px;
                background-color: white;
                font-size: 11px;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
                border: 2px solid #66bb6a;
            }
            QPushButton {
                background-color: #66bb6a;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 3px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #4caf50;
            }
            QPushButton:pressed {
                background-color: #388e3c;
            }
            QTableWidget {
                gridline-color: #a5d6a7;
                background-color: white;
                alternate-background-color: #f1f8e9;
                border: 1px solid #81c784;
                border-radius: 3px;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #c8e6c9;
            }
            QHeaderView::section {
                background-color: #81c784;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
                font-size: 11px;
            }
            QLabel {
                color: #2e7d32;
                font-size: 11px;
            }
        """)

        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # معلومات الفاتورة - تصميم مطابق للصورة
        header_group = QGroupBox("معلومات الفاتورة")
        header_layout = QGridLayout()
        header_layout.setSpacing(8)
        header_layout.setContentsMargins(10, 15, 10, 10)

        # الصف الأول - رقم الفاتورة والتاريخ
        invoice_num_label = QLabel("رقم الفاتورة:")
        invoice_num_label.setStyleSheet("font-weight: bold;")
        header_layout.addWidget(invoice_num_label, 0, 0)

        self.invoice_number_label = QLabel("سيتم إنشاؤه تلقائياً")
        self.invoice_number_label.setStyleSheet("""
            background-color: #fff3e0;
            padding: 4px 8px;
            border: 1px solid #ffcc02;
            border-radius: 2px;
            font-weight: bold;
            color: #e65100;
        """)
        header_layout.addWidget(self.invoice_number_label, 0, 1)

        date_label = QLabel("التاريخ:")
        date_label.setStyleSheet("font-weight: bold;")
        header_layout.addWidget(date_label, 0, 2)

        self.invoice_date = QDateEdit()
        self.invoice_date.setDate(QDate.currentDate())
        self.invoice_date.setEnabled(not self.invoice_id)
        self.invoice_date.setStyleSheet("min-width: 100px;")
        header_layout.addWidget(self.invoice_date, 0, 3)

        # الصف الثاني - العميل ونوع السعر
        customer_label = QLabel("العميل:")
        customer_label.setStyleSheet("font-weight: bold;")
        header_layout.addWidget(customer_label, 1, 0)

        self.customer_combo = QComboBox()
        self.customer_combo.setEnabled(not self.invoice_id)

        # إصلاح شريط التمرير الكبير في قائمة العميل
        self.customer_combo.setMaxVisibleItems(8)
        self.customer_combo.view().setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        self.customer_combo.setStyleSheet("""
            QComboBox {
                min-width: 180px;
                min-height: 30px;
                font-size: 12px;
                padding: 5px;
                padding-right: 25px;
            }
            QComboBox QAbstractItemView {
                max-height: 250px;
                min-height: 80px;
                selection-background-color: #2196F3;
                selection-color: white;
                outline: none;
            }
            QComboBox QAbstractItemView::item {
                height: 25px;
                padding: 3px;
            }
            QComboBox::drop-down {
                width: 25px;
                border: none;
                background: transparent;
            }
            QComboBox::down-arrow {
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzMzMzMzMyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
                width: 12px;
                height: 8px;
            }
            QComboBox::down-arrow:hover {
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzRDQUY1MCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
            }
        """)

        self.load_customers()
        header_layout.addWidget(self.customer_combo, 1, 1)

        price_type_label = QLabel("نوع السعر:")
        price_type_label.setStyleSheet("font-weight: bold;")
        header_layout.addWidget(price_type_label, 1, 2)

        self.price_type_combo = QComboBox()
        self.price_type_combo.addItems(["قطاعي", "نص جملة", "جملة"])
        self.price_type_combo.setEnabled(not self.invoice_id)

        # إضافة السهم لقائمة نوع السعر
        self.price_type_combo.setMaxVisibleItems(8)
        self.price_type_combo.view().setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        self.price_type_combo.setStyleSheet("""
            QComboBox {
                min-width: 100px;
                min-height: 30px;
                font-size: 12px;
                padding: 5px;
                padding-right: 25px;
            }
            QComboBox QAbstractItemView {
                max-height: 200px;
                min-height: 80px;
                selection-background-color: #2196F3;
                selection-color: white;
                outline: none;
            }
            QComboBox QAbstractItemView::item {
                height: 25px;
                padding: 3px;
            }
            QComboBox::drop-down {
                width: 25px;
                border: none;
                background: transparent;
            }
            QComboBox::down-arrow {
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzMzMzMzMyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
                width: 12px;
                height: 8px;
            }
            QComboBox::down-arrow:hover {
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzRDQUY1MCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
            }
        """)
        header_layout.addWidget(self.price_type_combo, 1, 3)

        # إضافة مساحة فارغة
        header_layout.setColumnStretch(4, 1)

        header_group.setLayout(header_layout)
        main_layout.addWidget(header_group)

        # أصناف الفاتورة - تصميم مطابق للصورة
        items_group = QGroupBox("أصناف الفاتورة")
        items_layout = QVBoxLayout()

        # شريط إضافة صنف - تصميم مبسط مثل الصورة
        if not self.invoice_id:
            add_item_layout = QHBoxLayout()
            add_item_layout.setSpacing(5)

            # المنتج
            product_label = QLabel("المنتج:")
            product_label.setStyleSheet("font-weight: bold; min-width: 50px;")
            add_item_layout.addWidget(product_label)

            self.product_combo = QComboBox()
            self.product_combo.setEnabled(not self.invoice_id)
            self.product_combo.setEditable(True)
            self.product_combo.setInsertPolicy(QComboBox.NoInsert)

            # إصلاح حجم القائمة المنسدلة لإظهار المنتجات بشكل صحيح
            self.product_combo.setMaxVisibleItems(8)  # عرض 8 عناصر كحد أقصى
            self.product_combo.view().setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

            self.product_combo.setStyleSheet("""
                QComboBox {
                    min-width: 200px;
                    min-height: 30px;
                    font-size: 12px;
                    padding: 5px;
                    padding-right: 25px;
                }
                QComboBox QAbstractItemView {
                    max-height: 250px;
                    min-height: 80px;
                    selection-background-color: #2196F3;
                    selection-color: white;
                    outline: none;
                }
                QComboBox QAbstractItemView::item {
                    height: 25px;
                    padding: 3px;
                }
                QComboBox::drop-down {
                    width: 25px;
                    border: none;
                    background: transparent;
                }
                QComboBox::down-arrow {
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzMzMzMzMyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
                    width: 12px;
                    height: 8px;
                }
                QComboBox::down-arrow:hover {
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzRDQUY1MCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
                }
            """)

            # إعداد البحث في القائمة
            completer = self.product_combo.completer()
            if completer:
                completer.setCompletionMode(QCompleter.PopupCompletion)
                completer.setFilterMode(Qt.MatchContains)
                completer.setCaseSensitivity(Qt.CaseInsensitive)

            # إعداد القائمة لتظهر عند النقر أو التركيز
            def setup_auto_popup():
                """إعداد القائمة المنسدلة لتظهر تلقائياً"""
                line_edit = self.product_combo.lineEdit()

                if line_edit:
                    # عند النقر على الحقل النصي
                    original_mouse_press = line_edit.mousePressEvent
                    def line_edit_mouse_press(event):
                        original_mouse_press(event)
                        # إظهار القائمة فوراً عند النقر
                        if not self.product_combo.view().isVisible():
                            QTimer.singleShot(10, lambda: self.product_combo.showPopup())
                    line_edit.mousePressEvent = line_edit_mouse_press

                    # عند التركيز على الحقل
                    original_focus_in = line_edit.focusInEvent
                    def line_edit_focus_in(event):
                        original_focus_in(event)
                        # إظهار القائمة عند التركيز
                        if not self.product_combo.view().isVisible():
                            QTimer.singleShot(50, lambda: self.product_combo.showPopup())
                    line_edit.focusInEvent = line_edit_focus_in

                # عند النقر على السهم أو أي مكان في الـ ComboBox
                original_combo_mouse_press = self.product_combo.mousePressEvent
                def combo_mouse_press(event):
                    original_combo_mouse_press(event)
                    # إظهار القائمة عند النقر على أي مكان في الـ ComboBox
                    if not self.product_combo.view().isVisible():
                        QTimer.singleShot(10, lambda: self.product_combo.showPopup())
                self.product_combo.mousePressEvent = combo_mouse_press

            setup_auto_popup()

            self.load_products()
            # تعطيل فلترة المنتجات مؤقتاً لحل مشكلة إضافة الأصناف
            # self.product_combo.lineEdit().textChanged.connect(self.filter_products)
            add_item_layout.addWidget(self.product_combo)

            # زر منتج جديد
            add_product_btn = QPushButton("منتج جديد")
            add_product_btn.setStyleSheet("""
                QPushButton {
                    background-color: #ff9800;
                    color: white;
                    font-weight: bold;
                    padding: 4px 8px;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #f57c00;
                }
            """)
            add_product_btn.clicked.connect(self.add_new_product)
            add_item_layout.addWidget(add_product_btn)

            # الكمية
            quantity_label = QLabel("الكمية:")
            quantity_label.setStyleSheet("font-weight: bold; min-width: 40px;")
            add_item_layout.addWidget(quantity_label)

            self.quantity_input = QSpinBox()
            self.quantity_input.setMinimum(1)
            self.quantity_input.setMaximum(9999)
            self.quantity_input.setValue(1)
            self.quantity_input.setEnabled(not self.invoice_id)
            self.quantity_input.setStyleSheet("min-width: 60px;")
            add_item_layout.addWidget(self.quantity_input)

            # السعر
            price_label = QLabel("السعر:")
            price_label.setStyleSheet("font-weight: bold; min-width: 40px;")
            add_item_layout.addWidget(price_label)

            self.price_input = QDoubleSpinBox()
            self.price_input.setMaximum(999999.99)
            self.price_input.setDecimals(2)
            self.price_input.setEnabled(not self.invoice_id)
            self.price_input.setStyleSheet("min-width: 80px;")
            add_item_layout.addWidget(self.price_input)

            # زر الإضافة
            add_item_btn = QPushButton("إضافة")
            add_item_btn.setStyleSheet("""
                QPushButton {
                    background-color: #4caf50;
                    color: white;
                    font-weight: bold;
                    padding: 4px 12px;
                    min-width: 60px;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
            """)
            add_item_btn.clicked.connect(self.add_item)
            add_item_layout.addWidget(add_item_btn)

            add_item_layout.addStretch()
            items_layout.addLayout(add_item_layout)

        # جدول الأصناف - تصميم مطابق للصورة
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels([
            "اسم الصنف", "الكمية", "السعر", "الإجمالي", "حذف"
        ])

        # تنسيق الجدول مطابق للصورة
        header = self.items_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # عمود المنتج قابل للتمدد
        header.setSectionResizeMode(1, QHeaderView.Fixed)    # عمود الكمية ثابت
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # عمود السعر ثابت
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # عمود الإجمالي ثابت
        header.setSectionResizeMode(4, QHeaderView.Fixed)    # عمود الحذف ثابت

        # تحديد عرض الأعمدة مطابق للصورة
        self.items_table.setColumnWidth(1, 70)   # الكمية
        self.items_table.setColumnWidth(2, 90)   # السعر
        self.items_table.setColumnWidth(3, 100)  # الإجمالي
        self.items_table.setColumnWidth(4, 60)   # حذف

        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setAlternatingRowColors(True)

        # تحديد ارتفاع الصفوف
        self.items_table.verticalHeader().setDefaultSectionSize(25)
        self.items_table.verticalHeader().setVisible(False)

        items_layout.addWidget(self.items_table)

        items_group.setLayout(items_layout)
        main_layout.addWidget(items_group)

        # إجماليات الفاتورة - تصميم مطابق للصورة
        totals_frame = QFrame()
        totals_frame.setStyleSheet("""
            QFrame {
                background-color: #e8f5e8;
                border: 1px solid #81c784;
                border-radius: 3px;
                margin: 2px;
            }
        """)
        totals_layout = QHBoxLayout()
        totals_layout.setSpacing(15)
        totals_layout.setContentsMargins(10, 8, 10, 8)

        # المجموع الفرعي
        subtotal_label = QLabel("المجموع الفرعي:")
        subtotal_label.setStyleSheet("font-weight: bold;")
        totals_layout.addWidget(subtotal_label)

        self.subtotal_label = QLabel("0.00")
        self.subtotal_label.setStyleSheet("""
            background-color: white;
            padding: 3px 8px;
            border: 1px solid #a5d6a7;
            border-radius: 2px;
            font-weight: bold;
            min-width: 80px;
        """)
        totals_layout.addWidget(self.subtotal_label)

        # الخصم
        discount_label = QLabel("الخصم:")
        discount_label.setStyleSheet("font-weight: bold;")
        totals_layout.addWidget(discount_label)

        self.discount_input = QDoubleSpinBox()
        self.discount_input.setMaximum(999999.99)
        self.discount_input.setDecimals(2)
        self.discount_input.setEnabled(not self.invoice_id)
        self.discount_input.valueChanged.connect(self.calculate_totals)
        self.discount_input.setStyleSheet("min-width: 80px;")
        totals_layout.addWidget(self.discount_input)

        # الإجمالي النهائي
        total_label = QLabel("الإجمالي النهائي:")
        total_label.setStyleSheet("font-weight: bold;")
        totals_layout.addWidget(total_label)

        self.total_label = QLabel("0.00")
        self.total_label.setStyleSheet("""
            background-color: #fff3e0;
            padding: 3px 8px;
            border: 1px solid #ffcc02;
            border-radius: 2px;
            font-weight: bold;
            color: #e65100;
            min-width: 80px;
        """)
        totals_layout.addWidget(self.total_label)

        # الإجمالي بعد الرصيد السابق
        final_total_label = QLabel("الإجمالي بعد الرصيد السابق:")
        final_total_label.setStyleSheet("font-weight: bold;")
        totals_layout.addWidget(final_total_label)

        self.final_total_label = QLabel("0.00")
        self.final_total_label.setStyleSheet("""
            background-color: #e3f2fd;
            padding: 3px 8px;
            border: 1px solid #2196f3;
            border-radius: 2px;
            font-weight: bold;
            color: #0d47a1;
            min-width: 80px;
        """)
        totals_layout.addWidget(self.final_total_label)

        totals_layout.addStretch()
        totals_frame.setLayout(totals_layout)
        main_layout.addWidget(totals_frame)

        # طريقة الدفع - تصميم مطابق للصورة
        if not self.invoice_id:
            payment_group = QGroupBox("طريقة الدفع")
            payment_layout = QHBoxLayout()
            payment_layout.setSpacing(10)
            payment_layout.setContentsMargins(10, 10, 10, 10)

            # طريقة الدفع
            payment_method_label = QLabel("طريقة الدفع:")
            payment_method_label.setStyleSheet("font-weight: bold; min-width: 80px;")
            payment_layout.addWidget(payment_method_label)

            self.payment_method_combo = QComboBox()
            for key, value in Config.PAYMENT_METHODS.items():
                self.payment_method_combo.addItem(value, key)
            self.payment_method_combo.currentTextChanged.connect(self.on_payment_method_changed)

            # إضافة السهم لقائمة طريقة الدفع
            self.payment_method_combo.setMaxVisibleItems(8)
            self.payment_method_combo.view().setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

            self.payment_method_combo.setStyleSheet("""
                QComboBox {
                    min-width: 120px;
                    min-height: 30px;
                    font-size: 12px;
                    padding: 5px;
                    padding-right: 25px;
                }
                QComboBox QAbstractItemView {
                    max-height: 200px;
                    min-height: 80px;
                    selection-background-color: #2196F3;
                    selection-color: white;
                    outline: none;
                }
                QComboBox QAbstractItemView::item {
                    height: 25px;
                    padding: 3px;
                }
                QComboBox::drop-down {
                    width: 25px;
                    border: none;
                    background: transparent;
                }
                QComboBox::down-arrow {
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzMzMzMzMyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
                    width: 12px;
                    height: 8px;
                }
                QComboBox::down-arrow:hover {
                    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzRDQUY1MCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
                }
            """)
            payment_layout.addWidget(self.payment_method_combo)

            # المبلغ المدفوع
            paid_amount_label = QLabel("المبلغ المدفوع:")
            paid_amount_label.setStyleSheet("font-weight: bold; min-width: 80px;")
            payment_layout.addWidget(paid_amount_label)

            self.paid_amount_input = QDoubleSpinBox()
            self.paid_amount_input.setMaximum(999999.99)
            self.paid_amount_input.setDecimals(2)
            self.paid_amount_input.setStyleSheet("min-width: 100px;")
            self.paid_amount_input.valueChanged.connect(self.calculate_remaining)
            payment_layout.addWidget(self.paid_amount_input)

            # تسمية توضيحية لطريقة الدفع
            self.payment_info_label = QLabel("")
            self.payment_info_label.setStyleSheet("""
                color: #666;
                font-size: 11px;
                font-style: italic;
                padding: 2px;
            """)
            payment_layout.addWidget(self.payment_info_label)

            # المتبقي
            remaining_label = QLabel("المتبقي:")
            remaining_label.setStyleSheet("font-weight: bold; min-width: 50px;")
            payment_layout.addWidget(remaining_label)

            self.remaining_label = QLabel("0.00")
            self.remaining_label.setStyleSheet("""
                background-color: #ffebee;
                padding: 3px 8px;
                border: 1px solid #f44336;
                border-radius: 2px;
                font-weight: bold;
                color: #c62828;
                min-width: 80px;
            """)
            payment_layout.addWidget(self.remaining_label)

            payment_layout.addStretch()
            payment_group.setLayout(payment_layout)
            main_layout.addWidget(payment_group)

        # الأزرار - تصميم مطابق للصورة
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(8)
        buttons_layout.setContentsMargins(10, 10, 10, 10)

        if not self.invoice_id:
            save_btn = QPushButton("حفظ الفاتورة")
            save_btn.setStyleSheet("""
                QPushButton {
                    background-color: #4caf50;
                    color: white;
                    font-weight: bold;
                    padding: 8px 16px;
                    border-radius: 3px;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
            """)
            save_btn.clicked.connect(self.save_invoice)
            buttons_layout.addWidget(save_btn)

            # زر فاتورة جديدة في وضع الفواتير المتعددة
            if self.multi_mode:
                new_invoice_btn = QPushButton("🆕 فاتورة جديدة")
                new_invoice_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #FF9800;
                        color: white;
                        font-weight: bold;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 6px;
                        font-size: 14px;
                    }
                    QPushButton:hover {
                        background-color: #F57C00;
                    }
                """)
                new_invoice_btn.clicked.connect(self.clear_invoice)
                buttons_layout.addWidget(new_invoice_btn)
        else:
            # زر المعاينة
            preview_btn = QPushButton("👁️ معاينة")
            preview_btn.setStyleSheet("""
                QPushButton {
                    background-color: #9c27b0;
                    color: white;
                    font-weight: bold;
                    padding: 8px 16px;
                    border-radius: 3px;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #7b1fa2;
                }
            """)
            preview_btn.clicked.connect(self.preview_invoice)
            buttons_layout.addWidget(preview_btn)

            print_btn = QPushButton("طباعة")
            print_btn.setStyleSheet("""
                QPushButton {
                    background-color: #2196f3;
                    color: white;
                    font-weight: bold;
                    padding: 8px 16px;
                    border-radius: 5px;
                    border: none;
                }
                QPushButton:hover {
                    background-color: #1976d2;
                }
            """)
            print_btn.clicked.connect(self.print_invoice)
            buttons_layout.addWidget(print_btn)

        # أزرار التحكم في حجم النافذة
        fullscreen_btn = QPushButton("🖥️ ملء الشاشة")
        fullscreen_btn.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 3px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #7B1FA2;
            }
        """)
        fullscreen_btn.clicked.connect(self.toggle_fullscreen)
        fullscreen_btn.setToolTip("اضغط F11 أو هذا الزر لملء الشاشة")
        buttons_layout.addWidget(fullscreen_btn)

        maximize_btn = QPushButton("📐 تكبير")
        maximize_btn.setStyleSheet("""
            QPushButton {
                background-color: #607D8B;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 3px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #455A64;
            }
        """)
        maximize_btn.clicked.connect(self.toggle_maximize)
        maximize_btn.setToolTip("اضغط Ctrl+M أو هذا الزر لتكبير النافذة")
        buttons_layout.addWidget(maximize_btn)

        cancel_btn = QPushButton("إغلاق")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 3px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        cancel_btn.clicked.connect(self.reject)

        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_btn)
        main_layout.addLayout(buttons_layout)

        self.setLayout(main_layout)

        # ربط الأحداث
        if not self.invoice_id:
            self.customer_combo.currentTextChanged.connect(self.on_customer_changed)
            # استخدام currentIndexChanged بدلاً من currentTextChanged لتجنب التداخل
            self.product_combo.currentIndexChanged.connect(self.on_product_changed)
            if hasattr(self, 'paid_amount_input'):
                self.paid_amount_input.valueChanged.connect(self.calculate_remaining)

    def generate_invoice_number(self):
        """توليد رقم فاتورة جديد"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        today = datetime.now().strftime("%Y%m%d")

        # البحث عن رقم فاتورة فريد
        counter = 1
        while True:
            invoice_number = f"{Config.INVOICE_PREFIX_SALE}{today}{counter:03d}"

            # التحقق من عدم وجود هذا الرقم
            cursor.execute('''
                SELECT COUNT(*) FROM invoices
                WHERE invoice_number = ?
            ''', (invoice_number,))

            if cursor.fetchone()[0] == 0:
                break

            counter += 1

        conn.close()
        self.invoice_number_label.setText(invoice_number)
        return invoice_number

    def load_customers(self):
        """تحميل العملاء"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, name FROM customers
            WHERE is_active = 1
            ORDER BY name
        ''')

        customers = cursor.fetchall()
        conn.close()

        # مسح القائمة وإعادة تعبئتها
        self.customer_combo.clear()
        self.customer_combo.addItem("عميل نقدي", 0)
        for customer in customers:
            # الحصول على الرصيد من آخر فاتورة
            last_balance = self.db_manager.get_customer_last_invoice_balance(customer['id'])
            self.customer_combo.addItem(f"{customer['name']} ({last_balance:.2f})", customer['id'])

    def load_products(self):
        """تحميل المنتجات"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, name, barcode, retail_price, wholesale_price, semi_wholesale_price, current_stock
            FROM products
            WHERE is_active = 1 AND current_stock > 0
            ORDER BY name
        ''')

        products = cursor.fetchall()
        conn.close()

        # مسح القائمة وإعادة تعبئتها
        self.product_combo.clear()
        self.product_combo.addItem("اختر منتج...", 0)

        for product in products:
            # تحسين عرض المنتج مع الباركود والسعر والمخزون
            barcode_text = product['barcode'] if product['barcode'] else "بدون باركود"
            price_text = f"{product['retail_price']:.2f}" if product['retail_price'] else "0.00"
            stock_text = f"متوفر: {product['current_stock']}"
            product_text = f"{product['name']} | {barcode_text} | {price_text} ج.م | {stock_text}"
            self.product_combo.addItem(product_text, product['id'])

    def on_customer_changed(self):
        """معالج تغيير العميل"""
        customer_id = self.customer_combo.currentData()
        if customer_id and customer_id > 0:
            # الحصول على المتبقي من آخر فاتورة للعميل
            last_balance = self.db_manager.get_customer_last_invoice_balance(customer_id)

            # تحديث نوع السعر حسب نوع العميل
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute('SELECT customer_type FROM customers WHERE id = ?', (customer_id,))
            customer = cursor.fetchone()
            conn.close()

            if customer:
                # تحديد نوع السعر حسب نوع العميل
                if customer['customer_type'] == 'wholesale':
                    self.price_type_combo.setCurrentText("جملة")
                elif customer['customer_type'] == 'semi_wholesale':
                    self.price_type_combo.setCurrentText("نص جملة")
                else:
                    self.price_type_combo.setCurrentText("قطاعي")

            self.calculate_totals()
        else:
            self.calculate_totals()

    def on_product_changed(self):
        """معالج تغيير المنتج"""
        product_id = self.product_combo.currentData()

        if product_id and product_id > 0:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT retail_price, wholesale_price, semi_wholesale_price
                FROM products WHERE id = ?
            ''', (product_id,))

            product = cursor.fetchone()
            conn.close()

            if product:
                # تحديد السعر حسب نوع العميل
                customer_id = self.customer_combo.currentData()
                if customer_id and customer_id > 0:
                    conn = self.db_manager.get_connection()
                    cursor = conn.cursor()
                    cursor.execute('SELECT customer_type FROM customers WHERE id = ?', (customer_id,))
                    customer = cursor.fetchone()
                    conn.close()

                    if customer:
                        if customer['customer_type'] == 'wholesale':
                            price = product['wholesale_price']
                        elif customer['customer_type'] == 'semi_wholesale':
                            price = product['semi_wholesale_price']
                        else:
                            price = product['retail_price']
                    else:
                        price = product['retail_price']
                else:
                    price = product['retail_price']

                self.price_input.setValue(price)
        else:
            self.price_input.setValue(0)

    def add_new_product(self):
        """إضافة منتج جديد من الفاتورة"""
        from ui.products_window import ProductDialog

        dialog = ProductDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            # إعادة تحميل قائمة المنتجات
            current_text = self.product_combo.currentText()
            self.load_products()

            # محاولة تحديد المنتج الجديد
            for i in range(self.product_combo.count()):
                if current_text.lower() in self.product_combo.itemText(i).lower():
                    self.product_combo.setCurrentIndex(i)
                    break

            QMessageBox.information(self, "نجح", "تم إضافة المنتج بنجاح!\nيمكنك الآن إضافته للفاتورة.")

    def add_new_product_with_name(self, suggested_name):
        """إضافة منتج جديد مع اسم مقترح"""
        from ui.quick_product_dialog import QuickProductDialog

        dialog = QuickProductDialog(self.db_manager, suggested_name, parent=self)

        if dialog.exec_() == QDialog.Accepted:
            # إعادة تحميل قائمة المنتجات
            self.load_products()

            # البحث عن المنتج الجديد وتحديده
            new_product_id = dialog.get_new_product_id()
            if new_product_id:
                for i in range(self.product_combo.count()):
                    if self.product_combo.itemData(i) == new_product_id:
                        self.product_combo.setCurrentIndex(i)
                        # تحديث السعر تلقائياً
                        self.on_product_changed()
                        break

    def filter_products(self, text):
        """فلترة المنتجات حسب النص المدخل"""
        # تعطيل الفلترة مؤقتاً لحل مشكلة إضافة الأصناف
        return

        if len(text) < 2:  # البدء في البحث من حرفين
            return

        # البحث في المنتجات وتحديد المطابق
        for i in range(self.product_combo.count()):
            item_text = self.product_combo.itemText(i).lower()
            if text.lower() in item_text and i > 0:  # تجاهل العنصر الأول "اختر منتج..."
                self.product_combo.setCurrentIndex(i)
                self.on_product_changed()
                break

    def add_item(self):
        """إضافة صنف للفاتورة"""
        # الحصول على بيانات المنتج المختار
        product_id = self.product_combo.currentData()
        product_text = self.product_combo.currentText().strip()

        # التحقق من صحة اختيار المنتج
        if not product_id or product_id == 0:
            if product_text and product_text != "اختر منتج...":
                reply = QMessageBox.question(self, "منتج غير موجود",
                                           f"المنتج '{product_text}' غير موجود في المخزن.\n"
                                           "هل تريد إضافته كمنتج جديد؟",
                                           QMessageBox.Yes | QMessageBox.No)

                if reply == QMessageBox.Yes:
                    # فتح نافذة إضافة منتج جديد مع اسم مقترح
                    self.add_new_product_with_name(product_text)
                    return
                else:
                    return
            else:
                QMessageBox.warning(self, "خطأ", "يرجى اختيار منتج من القائمة")
                return

        quantity = self.quantity_input.value()
        price = self.price_input.value()

        if price <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال سعر صحيح")
            return

        # التحقق من المخزون
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT name, current_stock FROM products WHERE id = ?', (product_id,))
        product = cursor.fetchone()
        conn.close()

        if not product:
            QMessageBox.warning(self, "خطأ", "المنتج غير موجود")
            return

        if quantity > product['current_stock']:
            QMessageBox.warning(self, "خطأ", f"الكمية المطلوبة ({quantity}) أكبر من المتوفر ({product['current_stock']})")
            return

        # إضافة الصنف
        item = {
            'product_id': product_id,
            'product_name': product['name'],
            'quantity': quantity,
            'price': price,
            'total': quantity * price
        }

        self.invoice_items.append(item)
        self.update_items_table()
        self.calculate_totals()

        # إعادة تعيين الحقول
        self.product_combo.setCurrentIndex(0)
        self.quantity_input.setValue(1)
        self.price_input.setValue(0)

    def remove_item(self, index):
        """حذف صنف من الفاتورة"""
        if 0 <= index < len(self.invoice_items):
            del self.invoice_items[index]
            self.update_items_table()
            self.calculate_totals()

    def update_items_table(self):
        """تحديث جدول الأصناف"""
        self.items_table.setRowCount(len(self.invoice_items))

        for row, item in enumerate(self.invoice_items):
            self.items_table.setItem(row, 0, QTableWidgetItem(item['product_name']))
            self.items_table.setItem(row, 1, QTableWidgetItem(str(item['quantity'])))
            self.items_table.setItem(row, 2, QTableWidgetItem(f"{item['price']:.2f}"))
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{item['total']:.2f}"))

            if not self.invoice_id:
                remove_btn = QPushButton("حذف")
                remove_btn.clicked.connect(lambda checked, r=row: self.remove_item(r))
                self.items_table.setCellWidget(row, 4, remove_btn)

    def calculate_totals(self):
        """حساب الإجماليات"""
        subtotal = sum(item['total'] for item in self.invoice_items)
        discount = self.discount_input.value()
        total = subtotal - discount

        # حساب الرصيد السابق من آخر فاتورة فقط
        customer_id = self.customer_combo.currentData()
        previous_balance = 0
        if customer_id and customer_id > 0:
            previous_balance = self.db_manager.get_customer_last_invoice_balance(customer_id)

        final_total = total + previous_balance

        # تحديث التسميات مع التنسيق الجديد
        self.subtotal_label.setText(f"{subtotal:.2f}")
        self.total_label.setText(f"{total:.2f}")
        self.final_total_label.setText(f"{final_total:.2f}")

        if not self.invoice_id:
            # تطبيق منطق الدفع حسب طريقة الدفع المختارة
            self.apply_payment_logic()
            self.calculate_remaining()

    def apply_payment_logic(self):
        """تطبيق منطق الدفع حسب طريقة الدفع المختارة"""
        if hasattr(self, 'payment_method_combo') and hasattr(self, 'paid_amount_input'):
            payment_method = self.payment_method_combo.currentData()
            final_total = float(self.final_total_label.text()) if self.final_total_label.text() else 0.0

            if payment_method == 'cash':
                # الدفع نقداً = مدفوع بالكامل (غير قابل للتعديل)
                self.paid_amount_input.setValue(final_total)
                self.paid_amount_input.setEnabled(False)

            elif payment_method == 'credit':
                # الدفع آجل = غير مدفوع (غير قابل للتعديل)
                self.paid_amount_input.setValue(0)
                self.paid_amount_input.setEnabled(False)

            elif payment_method == 'bank_transfer':
                # التحويل البنكي = قابل للتعديل (دفع جزئي أو كامل)
                # نحتفظ بالقيمة الحالية إذا كانت موجودة، وإلا نضع المبلغ كامل
                current_value = self.paid_amount_input.value()
                if current_value == 0:
                    self.paid_amount_input.setValue(final_total)
                self.paid_amount_input.setEnabled(True)

            else:
                # طرق دفع أخرى = قابل للتعديل
                self.paid_amount_input.setEnabled(True)

    def update_totals(self):
        """تحديث الإجماليات - دالة مساعدة"""
        self.calculate_totals()

    def calculate_remaining(self):
        """حساب المتبقي"""
        if hasattr(self, 'paid_amount_input') and hasattr(self, 'remaining_label'):
            final_total = float(self.final_total_label.text())
            paid_amount = self.paid_amount_input.value()
            remaining = final_total - paid_amount

            self.remaining_label.setText(f"{remaining:.2f}")

    def on_payment_method_changed(self):
        """معالج تغيير طريقة الدفع"""
        payment_method = self.payment_method_combo.currentData()
        final_total = float(self.final_total_label.text()) if self.final_total_label.text() else 0.0

        if payment_method == 'cash':
            # الدفع نقداً = مدفوع بالكامل (غير قابل للتعديل)
            self.paid_amount_input.setValue(final_total)
            self.paid_amount_input.setEnabled(False)
            self.payment_info_label.setText("💰 دفع نقدي - مدفوع بالكامل")
            print(f"💰 تم تحديد الدفع نقداً - المبلغ المدفوع: {final_total:.2f} جنيه")

        elif payment_method == 'credit':
            # الدفع آجل = غير مدفوع (يعلق على العميل)
            self.paid_amount_input.setValue(0)
            self.paid_amount_input.setEnabled(False)
            self.payment_info_label.setText("📝 دفع آجل - يعلق على العميل")
            print(f"📝 تم تحديد الدفع آجل - سيعلق المبلغ على العميل: {final_total:.2f} جنيه")

        elif payment_method == 'bank_transfer':
            # التحويل البنكي = قابل للتعديل (دفع جزئي أو كامل)
            self.paid_amount_input.setValue(final_total)  # افتراضي: المبلغ كامل
            self.paid_amount_input.setEnabled(True)  # السماح بالتعديل للدفع الجزئي
            self.payment_info_label.setText("🏦 تحويل بنكي - يمكن الدفع الجزئي")
            print(f"🏦 تم تحديد التحويل البنكي - يمكن تعديل المبلغ للدفع الجزئي")

        else:
            # طرق دفع أخرى = قابل للتعديل
            self.paid_amount_input.setValue(final_total)
            self.paid_amount_input.setEnabled(True)
            self.payment_info_label.setText("💳 يمكن تعديل المبلغ المدفوع")

        # تحديث المبلغ المتبقي
        self.calculate_remaining()

    def save_invoice(self):
        """حفظ الفاتورة"""
        if not self.invoice_items:
            QMessageBox.warning(self, "خطأ", "يرجى إضافة أصناف للفاتورة")
            return

        customer_id = self.customer_combo.currentData()
        if customer_id == 0:
            customer_id = None

        invoice_data = {
            'invoice_number': self.invoice_number_label.text(),
            'customer_id': customer_id,
            'user_id': self.user_data['id'],
            'total_amount': float(self.subtotal_label.text()),
            'discount_amount': self.discount_input.value(),
            'final_amount': float(self.total_label.text()),
            'paid_amount': self.paid_amount_input.value(),
            'remaining_amount': float(self.remaining_label.text()),
            'payment_method': self.payment_method_combo.currentData(),
            'invoice_date': self.invoice_date.date().toString('yyyy-MM-dd')
        }

        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        try:
            # بدء معاملة قاعدة البيانات
            cursor.execute('BEGIN IMMEDIATE')
            # إدراج الفاتورة
            cursor.execute('''
                INSERT INTO invoices
                (invoice_number, invoice_type, customer_id, user_id, total_amount,
                 discount_amount, final_amount, paid_amount, remaining_amount,
                 payment_method, invoice_date)
                VALUES (?, 'sale', ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                invoice_data['invoice_number'], invoice_data['customer_id'],
                invoice_data['user_id'], invoice_data['total_amount'],
                invoice_data['discount_amount'], invoice_data['final_amount'],
                invoice_data['paid_amount'], invoice_data['remaining_amount'],
                invoice_data['payment_method'], invoice_data['invoice_date']
            ))

            invoice_id = cursor.lastrowid

            # إدراج أصناف الفاتورة بشكل مجمع
            invoice_items_data = []
            stock_updates_data = []
            stock_movements_data = []

            for item in self.invoice_items:
                invoice_items_data.append((
                    invoice_id, item['product_id'], item['quantity'],
                    item['price'], item['total']
                ))
                stock_updates_data.append((item['quantity'], item['product_id']))
                stock_movements_data.append((
                    item['product_id'], 'out', item['quantity'],
                    'invoice', invoice_id, self.user_data['id']
                ))

            # تنفيذ العمليات بشكل مجمع
            cursor.executemany('''
                INSERT INTO invoice_items
                (invoice_id, product_id, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?)
            ''', invoice_items_data)

            cursor.executemany('''
                UPDATE products
                SET current_stock = current_stock - ?
                WHERE id = ?
            ''', stock_updates_data)

            cursor.executemany('''
                INSERT INTO stock_movements
                (product_id, movement_type, quantity, reference_type, reference_id, user_id)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', stock_movements_data)

            # تحديث النظام المالي المتكامل حسب طريقة الدفع
            try:
                from database.financial_manager import FinancialManager
                import os

                db_path = os.path.join('database', 'business_system.db')
                financial_manager = FinancialManager(db_path)

                payment_method = invoice_data['payment_method']
                paid_amount = invoice_data['paid_amount']

                if paid_amount > 0:  # فقط إذا كان هناك مبلغ مدفوع
                    if payment_method == 'cash':
                        # الدفع نقدي - إضافة للخزينة
                        financial_manager.add_cash_transaction(
                            "إيراد",
                            "مبيعات",
                            paid_amount,
                            f"مبيعات نقدية - فاتورة رقم {invoice_data['invoice_number']}"
                        )
                        print(f"✅ تم إضافة {paid_amount:,.2f} جنيه للخزينة من المبيعات النقدية")

                    elif payment_method == 'bank_transfer':
                        # الدفع بتحويل بنكي - إضافة للبنك
                        financial_manager.add_bank_transaction(
                            "إيداع",
                            "عميل",
                            paid_amount,
                            f"تحويل بنكي - فاتورة مبيعات رقم {invoice_data['invoice_number']}"
                        )
                        print(f"✅ تم إضافة {paid_amount:,.2f} جنيه للبنك من التحويل البنكي")

                    elif payment_method == 'credit':
                        # الدفع آجل - لا نضيف شيء للخزينة أو البنك الآن
                        print(f"📝 فاتورة آجلة بمبلغ {invoice_data['final_amount']:,.2f} جنيه")

            except Exception as e:
                # في حالة فشل تحديث النظام المالي، نتجاهل الخطأ ونكمل حفظ الفاتورة
                print(f"❌ خطأ في تحديث النظام المالي: {str(e)}")
                import traceback
                traceback.print_exc()

            # لا نحتاج لتحديث current_balance في جدول العملاء
            # لأننا نحسب الرصيد من آخر فاتورة مباشرة

            # إذا كان الدفع بالتحويل البنكي، سجل التحويل وحدث رصيد البنك
            if (invoice_data['payment_method'] == 'bank_transfer' and
                invoice_data['paid_amount'] > 0):

                try:
                    # التحقق من وجود جدول bank_accounts
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='bank_accounts'")
                    if cursor.fetchone():
                        # الحصول على الحساب البنكي الافتراضي
                        cursor.execute("SELECT setting_value FROM system_settings WHERE setting_key = 'default_bank_account_id'")
                        result = cursor.fetchone()
                        default_account_id = int(result['setting_value']) if result else 1

                        # إدراج التحويل البنكي
                        cursor.execute('''
                            INSERT INTO bank_transfers
                            (bank_account_id, customer_id, amount, transfer_type,
                             reference_number, notes, user_id)
                            VALUES (?, ?, ?, 'in', ?, ?, ?)
                        ''', (
                            default_account_id,
                            customer_id,
                            invoice_data['paid_amount'],
                            f"فاتورة مبيعات رقم {invoice_data['invoice_number']}",
                            f"تحويل من فاتورة رقم {invoice_id}",
                            self.user_data['id']
                        ))

                        # تحديث رصيد الحساب البنكي
                        cursor.execute('''
                            UPDATE bank_accounts
                            SET current_balance = current_balance + ?, updated_at = CURRENT_TIMESTAMP
                            WHERE id = ?
                        ''', (invoice_data['paid_amount'], default_account_id))

                        # إضافة المبلغ لحساب البنك في النظام الجديد
                        bank_account = self.db_manager.get_account_by_type('bank')
                        if bank_account:
                            self.db_manager.update_account_balance(
                                bank_account['id'],
                                invoice_data['paid_amount'],
                                'add',
                                'bank_transfer',
                                invoice_id,
                                f"تحويل بنكي من فاتورة مبيعات رقم {invoice_data['invoice_number']}",
                                self.user_data['id']
                            )

                except Exception as e:
                    # في حالة فشل تسجيل التحويل، نتجاهل الخطأ ونكمل حفظ الفاتورة
                    print(f"خطأ في تسجيل التحويل البنكي: {str(e)}")

            conn.commit()
            conn.close()

            # إرسال إشارة الحفظ في وضع الفواتير المتعددة
            if self.multi_mode:
                self.invoice_saved.emit()
                # مسح الفاتورة الحالية لبدء فاتورة جديدة
                self.clear_invoice()
                QMessageBox.information(self, "نجح", "تم حفظ الفاتورة بنجاح\nيمكنك الآن بدء فاتورة جديدة")
            else:
                QMessageBox.information(self, "نجح", "تم حفظ الفاتورة بنجاح")
                self.accept()

        except Exception as e:
            conn.rollback()
            conn.close()
            # عرض تفاصيل الخطأ للتشخيص
            import traceback
            error_details = traceback.format_exc()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الفاتورة:\n{str(e)}\n\nتفاصيل الخطأ:\n{error_details}")

    def clear_invoice(self):
        """مسح الفاتورة الحالية لبدء فاتورة جديدة"""
        try:
            # مسح جدول الأصناف
            self.invoice_items.clear()
            self.items_table.setRowCount(0)

            # إعادة تعيين القيم
            self.customer_combo.setCurrentIndex(0)
            self.payment_method_combo.setCurrentIndex(0)
            self.discount_input.setValue(0)
            self.paid_amount_input.setValue(0)

            # إعادة تفعيل حقل المبلغ المدفوع ومسح الرسالة التوضيحية
            self.paid_amount_input.setEnabled(True)
            if hasattr(self, 'payment_info_label'):
                self.payment_info_label.setText("")

            # تحديث الإجماليات
            self.update_totals()

            # إنشاء رقم فاتورة جديد
            self.invoice_number_label.setText(self.generate_invoice_number())

            # إعادة تعيين التاريخ للتاريخ الحالي
            self.invoice_date.setDate(QDate.currentDate())

        except Exception as e:
            print(f"خطأ في مسح الفاتورة: {str(e)}")

    def load_invoice_data(self):
        """تحميل بيانات الفاتورة للعرض"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        # تحميل الفاتورة
        cursor.execute('''
            SELECT i.*, c.name as customer_name
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE i.id = ?
        ''', (self.invoice_id,))

        invoice = cursor.fetchone()

        if invoice:
            self.invoice_number_label.setText(invoice['invoice_number'])
            self.invoice_date.setDate(QDate.fromString(invoice['invoice_date'], 'yyyy-MM-dd'))

            # تحديد العميل
            if invoice['customer_id']:
                for i in range(self.customer_combo.count()):
                    if self.customer_combo.itemData(i) == invoice['customer_id']:
                        self.customer_combo.setCurrentIndex(i)
                        break

            self.discount_input.setValue(invoice['discount_amount'])

        # تحميل أصناف الفاتورة
        cursor.execute('''
            SELECT ii.*, p.name as product_name
            FROM invoice_items ii
            JOIN products p ON ii.product_id = p.id
            WHERE ii.invoice_id = ?
        ''', (self.invoice_id,))

        items = cursor.fetchall()

        for item in items:
            self.invoice_items.append({
                'product_id': item['product_id'],
                'product_name': item['product_name'],
                'quantity': item['quantity'],
                'price': item['unit_price'],
                'total': item['total_price']
            })

        conn.close()

        self.update_items_table()
        self.calculate_totals()

    def preview_invoice(self):
        """معاينة الفاتورة"""
        if self.invoice_id:
            from ui.invoice_preview import InvoicePreview
            preview_dialog = InvoicePreview(self.db_manager, self.invoice_id, 'sale', self)
            preview_dialog.exec_()
        else:
            QMessageBox.warning(self, "تحذير", "يجب حفظ الفاتورة أولاً قبل المعاينة")

    def print_invoice(self):
        """طباعة الفاتورة"""
        if self.invoice_id:
            from ui.simple_printer import SimplePrinter
            printer_dialog = SimplePrinter(self.db_manager, self.invoice_id, 'sale', self)
            printer_dialog.exec_()

            # إرسال إشارة الطباعة في وضع الفواتير المتعددة
            if self.multi_mode:
                self.invoice_printed.emit()
        else:
            QMessageBox.warning(self, "خطأ", "يجب حفظ الفاتورة أولاً قبل الطباعة")

    def toggle_fullscreen(self):
        """تبديل وضع ملء الشاشة"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()

    def toggle_maximize(self):
        """تبديل وضع تكبير النافذة"""
        if self.isMaximized():
            self.showNormal()
        else:
            self.showMaximized()
