#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
لوحة التحكم الرئيسية - نظام نقاط البيع والمحاسبة
Main Dashboard - POS and Accounting System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QFrame, QGridLayout, QStackedWidget,
                            QMenuBar, QAction, QMessageBox, QToolBar, QSizePolicy,
                            QMainWindow)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor
from utils.config import Config
from ui.products_window import ProductsWindow
from ui.customers_window import CustomersWindow
from ui.suppliers_window import SuppliersWindow
from ui.sales_window import SalesWindow

from ui.purchases_main import PurchasesMainWindow
from ui.basic_returns import BasicReturnsWindow

from ui.banking_window import BankingWindow
from ui.reports_window import ReportsWindow
from ui.users_window import UsersWindow
from ui.expenses_window import ExpensesWindow
from ui.financial_management import FinancialManagementWindow
# from ui.dashboard_analytics import DashboardWidget  # تم حذف الملف
from ui.multi_sales_window import MultiSalesWindow
from ui.backup_window import BackupWindow
from ui.modern_dashboard import ModernDashboard

from datetime import datetime

class MainDashboard(QWidget):
    """لوحة التحكم الرئيسية"""
    
    logout_requested = pyqtSignal()
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.current_module = None
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                font-family: 'Segoe UI', Arial, sans-serif;
                color: #2c3e50;
            }
            QFrame#header {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                padding: 15px;
                border-radius: 12px;
                margin-bottom: 15px;
                border: none;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            }
            QLabel#header_title {
                color: black;
                font-size: 20px;
                font-weight: 600;
                text-shadow: 1px 1px 2px rgba(255,255,255,0.5);
            }
            QLabel#user_info {
                color: #2c3e50;
                font-size: 13px;
                font-weight: 500;
            }
            QPushButton#menu_btn {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f1f3f4);
                border: 2px solid #e0e0e0;
                border-radius: 12px;
                padding: 20px;
                margin: 8px;
                font-size: 15px;
                font-weight: 500;
                text-align: center;
                min-height: 90px;
                color: #2c3e50;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }
            QPushButton#menu_btn:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-color: #667eea;
                color: white;
                transform: translateY(-2px);
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            }
            QPushButton#menu_btn:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5a67d8, stop:1 #6b46c1);
                transform: translateY(0px);
            }
            QPushButton#logout_btn {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff6b6b, stop:1 #ee5a52);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 12px;
                font-weight: 500;
                font-size: 11px;
                box-shadow: 0 2px 6px rgba(255, 107, 107, 0.3);
            }
            QPushButton#logout_btn:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ff5252, stop:1 #e53e3e);
                box-shadow: 0 3px 8px rgba(255, 82, 82, 0.4);
            }
        """)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # الترويسة
        header_frame = QFrame()
        header_frame.setObjectName("header")
        header_layout = QHBoxLayout(header_frame)
        
        # معلومات الشركة والنظام
        company_layout = QVBoxLayout()
        
        title_label = QLabel(Config.COMPANY_NAME)
        title_label.setObjectName("header_title")
        company_layout.addWidget(title_label)
        
        system_label = QLabel("نظام نقاط البيع والمحاسبة")
        system_label.setObjectName("user_info")
        company_layout.addWidget(system_label)
        
        header_layout.addLayout(company_layout)
        
        # معلومات المستخدم والوقت
        user_layout = QVBoxLayout()
        user_layout.setAlignment(Qt.AlignRight)
        
        user_label = QLabel(f"المستخدم: {self.user_data['full_name']}")
        user_label.setObjectName("user_info")
        user_layout.addWidget(user_label)
        
        role_text = "مدير" if self.user_data['role'] == 'admin' else "كاشير"
        role_label = QLabel(f"الصلاحية: {role_text}")
        role_label.setObjectName("user_info")
        user_layout.addWidget(role_label)
        
        # الوقت الحالي
        self.time_label = QLabel()
        self.time_label.setObjectName("user_info")
        user_layout.addWidget(self.time_label)
        
        # تحديث الوقت كل ثانية
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)
        self.update_time()
        
        header_layout.addLayout(user_layout)
        
        # زر تسجيل الخروج (مصغر وأنيق)
        logout_btn = QPushButton("خروج")
        logout_btn.setObjectName("logout_btn")
        logout_btn.clicked.connect(self.logout)
        logout_btn.setFixedSize(60, 30)  # حجم أصغر
        header_layout.addWidget(logout_btn)
        
        main_layout.addWidget(header_frame)
        
        # قائمة الوحدات
        self.create_menu_grid(main_layout)
        
        self.setLayout(main_layout)
        
    def create_menu_grid(self, main_layout):
        """إنشاء شبكة قائمة الوحدات"""
        menu_frame = QFrame()
        menu_layout = QGridLayout(menu_frame)
        menu_layout.setSpacing(10)
        
        # تعريف الوحدات حسب الصلاحيات
        modules = self.get_available_modules()
        
        # ترتيب الوحدات في شبكة 3x3
        row, col = 0, 0
        for module in modules:
            btn = QPushButton(module['name'])
            btn.setObjectName("menu_btn")
            btn.clicked.connect(lambda checked, m=module: self.open_module(m))
            
            menu_layout.addWidget(btn, row, col)
            
            col += 1
            if col > 2:  # 3 أعمدة
                col = 0
                row += 1
                
        main_layout.addWidget(menu_frame)
        
    def get_available_modules(self):
        """الحصول على الوحدات المتاحة حسب صلاحيات المستخدم"""
        modules = [
            {'name': 'إدارة المنتجات\nوالمخزون', 'key': 'products', 'admin_only': False},

            {'name': 'إدارة العملاء', 'key': 'customers', 'admin_only': False},
            {'name': 'إدارة الموردين', 'key': 'suppliers', 'admin_only': True},
            {'name': 'فواتير البيع', 'key': 'sales', 'admin_only': False},
            {'name': '🔥 فواتير متعددة\nMulti Sales', 'key': 'multi_sales', 'admin_only': False},
            {'name': '🛒 إدارة المشتريات', 'key': 'purchases', 'admin_only': True},

            {'name': '🔄 إدارة المرتجعات', 'key': 'returns', 'admin_only': False},

            {'name': 'إدارة المصاريف', 'key': 'expenses', 'admin_only': True},
            {'name': 'إدارة رأس المال', 'key': 'capital', 'admin_only': True},
            {'name': 'إدارة البنك\nوالتحويلات', 'key': 'banking', 'admin_only': True},
            {'name': 'التقارير المالية', 'key': 'reports', 'admin_only': True},
            {'name': '💾 النسخ الاحتياطي\nBackup', 'key': 'backup', 'admin_only': True},
            {'name': 'إدارة المستخدمين', 'key': 'users', 'admin_only': True}
        ]
        
        # تصفية الوحدات حسب الصلاحيات
        if self.user_data['role'] == 'admin':
            return modules
        else:
            return [m for m in modules if not m['admin_only']]
            
    def open_module(self, module):
        """فتح وحدة معينة"""
        print(f"فتح الوحدة: {module['name']}")

        if module['key'] == 'dashboard':
            try:
                print("🚀 فتح لوحة التحكم الحديثة...")
                self.dashboard_window = ModernDashboard(self.db_manager, self.user_data)
                self.dashboard_window.show()
            except Exception as e:
                print(f"❌ خطأ في فتح لوحة التحكم: {str(e)}")
                import traceback
                traceback.print_exc()
                QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء فتح لوحة التحكم:\n{str(e)}")
        elif module['key'] == 'products':
            self.products_window = ProductsWindow(self.db_manager, self.user_data)
            self.products_window.show()

        elif module['key'] == 'customers':
            self.customers_window = CustomersWindow(self.db_manager, self.user_data)
            self.customers_window.show()
        elif module['key'] == 'suppliers':
            self.suppliers_window = SuppliersWindow(self.db_manager, self.user_data)
            self.suppliers_window.show()
        elif module['key'] == 'sales':
            self.sales_window = SalesWindow(self.db_manager, self.user_data)
            self.sales_window.show()
        elif module['key'] == 'multi_sales':
            print("🚀 فتح نافذة الفواتير المتعددة...")
            self.multi_sales_window = MultiSalesWindow(self.db_manager, self.user_data)
            self.multi_sales_window.show()
        elif module['key'] == 'purchases':
            print("🛒 فتح نافذة إدارة المشتريات...")
            self.purchases_window = PurchasesMainWindow(self.db_manager, self.user_data)
            self.purchases_window.show()

        elif module['key'] == 'returns':
            self.returns_window = BasicReturnsWindow(self.db_manager, self.user_data)
            self.returns_window.show()

        elif module['key'] == 'banking':
            self.banking_window = BankingWindow(self.db_manager, self.user_data)
            self.banking_window.show()
        elif module['key'] == 'reports':
            self.reports_window = ReportsWindow(self.db_manager, self.user_data)
            self.reports_window.show()
        elif module['key'] == 'users':
            self.users_window = UsersWindow(self.db_manager, self.user_data)
            self.users_window.show()
        elif module['key'] == 'expenses':
            self.expenses_window = ExpensesWindow(self.db_manager, self.user_data)
            self.expenses_window.show()
        elif module['key'] == 'capital':
            print("🚀 فتح النظام المالي المتكامل...")
            self.financial_window = FinancialManagementWindow(self.db_manager, self.user_data)
            self.financial_window.show()
        elif module['key'] == 'backup':
            print("🚀 فتح نظام النسخ الاحتياطي...")
            self.backup_window = BackupWindow(self.db_manager, self.user_data)
            self.backup_window.show()
        else:
            QMessageBox.information(self, "غير متاح",
                                  f"وحدة {module['name']} غير متاحة حالياً")
        
    def update_time(self):
        """تحديث الوقت الحالي"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(f"التاريخ والوقت: {current_time}")
        
    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(self, "تأكيد", 
                                   "هل تريد تسجيل الخروج؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.logout_requested.emit()
