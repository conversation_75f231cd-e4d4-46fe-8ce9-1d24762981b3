#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة إدارة البنك والتحويلات - نظام نقاط البيع والمحاسبة
Banking and Transfers Management Window - POS and Accounting System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QLineEdit, QComboBox, QDialog, QFormLayout, 
                            QMessageBox, QHeaderView, QAbstractItemView,
                            QFrame, QGroupBox, QDoubleSpinBox, QDateEdit, 
                            QTextEdit, QTabWidget)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QColor
from utils.config import Config
from datetime import datetime

class BankingWindow(QWidget):
    """نافذة إدارة البنك والتحويلات"""
    
    def __init__(self, db_manager, user_data):
        super().__init__()
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        self.load_transfers()
        self.update_bank_balance()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة البنك والتحويلات")
        self.setGeometry(100, 100, 1200, 700)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()

        new_transfer_btn = QPushButton("تحويل بنكي جديد")
        new_transfer_btn.clicked.connect(self.new_transfer)
        toolbar_layout.addWidget(new_transfer_btn)

        view_transfer_btn = QPushButton("عرض التحويل")
        view_transfer_btn.clicked.connect(self.view_transfer)
        toolbar_layout.addWidget(view_transfer_btn)

        link_invoice_btn = QPushButton("ربط بفاتورة")
        link_invoice_btn.clicked.connect(self.link_to_invoice)
        toolbar_layout.addWidget(link_invoice_btn)

        toolbar_layout.addStretch()

        # عرض رصيد الحساب البنكي
        bank_balance_label = QLabel("رصيد الحساب البنكي:")
        bank_balance_label.setStyleSheet("font-weight: bold; color: #2e7d32;")
        toolbar_layout.addWidget(bank_balance_label)

        self.bank_balance_value = QLabel("0.00 جنيه")
        self.bank_balance_value.setStyleSheet("""
            background-color: #e8f5e8;
            padding: 5px 10px;
            border: 1px solid #81c784;
            border-radius: 3px;
            font-weight: bold;
            color: #1b5e20;
            min-width: 100px;
        """)
        toolbar_layout.addWidget(self.bank_balance_value)
        
        # البحث
        search_label = QLabel("البحث:")
        toolbar_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("رقم المرجع أو اسم العميل/المورد...")
        self.search_input.textChanged.connect(self.search_transfers)
        toolbar_layout.addWidget(self.search_input)
        
        main_layout.addLayout(toolbar_layout)
        
        # جدول التحويلات
        self.transfers_table = QTableWidget()
        self.transfers_table.setColumnCount(8)
        self.transfers_table.setHorizontalHeaderLabels([
            "التاريخ", "العميل/المورد", "النوع", "المبلغ",
            "رقم المرجع", "البنك", "الفاتورة المربوطة", "ملاحظات"
        ])
        
        # تنسيق الجدول
        header = self.transfers_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Interactive)
        
        self.transfers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.transfers_table.setAlternatingRowColors(True)
        self.transfers_table.doubleClicked.connect(self.view_transfer)
        
        main_layout.addWidget(self.transfers_table)
        
        # شريط الحالة
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("جاري التحميل...")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # إحصائيات سريعة
        self.stats_label = QLabel()
        self.stats_label.setStyleSheet("color: blue; font-weight: bold;")
        status_layout.addWidget(self.stats_label)
        
        main_layout.addLayout(status_layout)
        
        self.setLayout(main_layout)
        
    def load_transfers(self):
        """تحميل التحويلات من قاعدة البيانات"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT bt.*,
                   c.name as customer_name,
                   s.name as supplier_name,
                   i.invoice_number as linked_invoice_number
            FROM bank_transfers bt
            LEFT JOIN customers c ON bt.customer_id = c.id
            LEFT JOIN suppliers s ON bt.supplier_id = s.id
            LEFT JOIN invoices i ON bt.linked_invoice_id = i.id
            ORDER BY bt.transfer_date DESC
        ''')
        
        transfers = cursor.fetchall()
        conn.close()
        
        # تحديث الجدول
        self.transfers_table.setRowCount(len(transfers))
        
        total_amount = 0
        customer_transfers = 0
        supplier_transfers = 0
        
        for row, transfer in enumerate(transfers):
            self.transfers_table.setItem(row, 0, QTableWidgetItem(transfer['transfer_date']))
            
            # تحديد نوع التحويل والاسم
            if transfer['customer_id']:
                name = transfer['customer_name']
                transfer_type = "عميل"
                customer_transfers += 1
            else:
                name = transfer['supplier_name']
                transfer_type = "مورد"
                supplier_transfers += 1
                
            self.transfers_table.setItem(row, 1, QTableWidgetItem(name or ''))
            self.transfers_table.setItem(row, 2, QTableWidgetItem(transfer_type))
            self.transfers_table.setItem(row, 3, QTableWidgetItem(f"{transfer['amount']:.2f}"))
            self.transfers_table.setItem(row, 4, QTableWidgetItem(transfer['reference_number'] or ''))
            self.transfers_table.setItem(row, 5, QTableWidgetItem(transfer['bank_name'] or ''))

            # الفاتورة المربوطة
            linked_invoice = transfer['linked_invoice_number'] or 'غير مربوط'
            linked_item = QTableWidgetItem(linked_invoice)
            if transfer['linked_invoice_number']:
                linked_item.setBackground(QColor(235, 255, 235))  # خلفية خضراء فاتحة
            else:
                linked_item.setBackground(QColor(255, 235, 235))  # خلفية حمراء فاتحة
            self.transfers_table.setItem(row, 6, linked_item)

            self.transfers_table.setItem(row, 7, QTableWidgetItem(transfer['notes'] or ''))

            # حفظ معرف التحويل للاستخدام لاحقاً
            id_item = QTableWidgetItem(str(transfer['id']))
            id_item.setData(Qt.UserRole, transfer['id'])
            self.transfers_table.setItem(row, 0, id_item)
            
            total_amount += transfer['amount']
            
        # تحديث شريط الحالة
        self.status_label.setText(f"إجمالي التحويلات: {len(transfers)}")
        self.stats_label.setText(f"المبلغ الإجمالي: {total_amount:.2f} | العملاء: {customer_transfers} | الموردين: {supplier_transfers}")

    def update_bank_balance(self):
        """تحديث رصيد الحساب البنكي"""
        try:
            default_account_id = self.db_manager.get_default_bank_account_id()
            balance = self.db_manager.get_bank_account_balance(default_account_id)
            self.bank_balance_value.setText(f"{balance:.2f} جنيه")
            print(f"Debug: Bank balance updated to: {balance}")
        except Exception as e:
            print(f"Debug: Error updating bank balance: {str(e)}")
            self.bank_balance_value.setText("خطأ في التحديث")

    def search_transfers(self):
        """البحث في التحويلات"""
        search_text = self.search_input.text().lower()
        
        for row in range(self.transfers_table.rowCount()):
            show_row = False
            
            # البحث في رقم المرجع والاسم
            name_item = self.transfers_table.item(row, 1)
            reference_item = self.transfers_table.item(row, 4)
            
            if name_item and search_text in name_item.text().lower():
                show_row = True
            elif reference_item and search_text in reference_item.text().lower():
                show_row = True
                
            self.transfers_table.setRowHidden(row, not show_row)
            
    def new_transfer(self):
        """إضافة تحويل بنكي جديد"""
        dialog = BankTransferDialog(self.db_manager, self.user_data, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_transfers()
            self.update_bank_balance()
            
    def view_transfer(self):
        """عرض تفاصيل التحويل"""
        current_row = self.transfers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار تحويل للعرض")
            return
            
        # عرض تفاصيل التحويل
        transfer_info = []
        for col in range(self.transfers_table.columnCount()):
            header = self.transfers_table.horizontalHeaderItem(col).text()
            item = self.transfers_table.item(current_row, col)
            value = item.text() if item else ''
            transfer_info.append(f"{header}: {value}")
            
        QMessageBox.information(self, "تفاصيل التحويل", "\n".join(transfer_info))
        
    def link_to_invoice(self):
        """ربط التحويل بفاتورة"""
        current_row = self.transfers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار تحويل للربط")
            return

        # الحصول على معرف التحويل
        transfer_id = self.transfers_table.item(current_row, 0).data(Qt.UserRole)
        if not transfer_id:
            QMessageBox.warning(self, "خطأ", "لا يمكن الحصول على معرف التحويل")
            return

        # فتح نافذة ربط التحويل بالفاتورة
        dialog = LinkTransferDialog(self.db_manager, transfer_id, self.user_data, self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_transfers()  # إعادة تحميل البيانات


class BankTransferDialog(QDialog):
    """نافذة حوار التحويل البنكي"""
    
    def __init__(self, db_manager, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تحويل بنكي جديد")
        self.setFixedSize(500, 400)
        
        layout = QVBoxLayout()
        
        # نوع التحويل
        type_group = QGroupBox("نوع التحويل")
        type_layout = QFormLayout()
        
        self.transfer_type_combo = QComboBox()
        self.transfer_type_combo.addItem("من عميل", "customer")
        self.transfer_type_combo.addItem("إلى مورد", "supplier")
        self.transfer_type_combo.currentTextChanged.connect(self.on_type_changed)
        type_layout.addRow("نوع التحويل:", self.transfer_type_combo)
        
        type_group.setLayout(type_layout)
        layout.addWidget(type_group)
        
        # تفاصيل التحويل
        details_group = QGroupBox("تفاصيل التحويل")
        details_layout = QFormLayout()
        
        # العميل/المورد
        self.entity_combo = QComboBox()
        details_layout.addRow("العميل/المورد:", self.entity_combo)
        
        # المبلغ
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setMaximum(999999.99)
        self.amount_input.setDecimals(2)
        self.amount_input.setSuffix(" جنيه")
        details_layout.addRow("المبلغ:", self.amount_input)
        
        # التاريخ
        self.transfer_date = QDateEdit()
        self.transfer_date.setDate(QDate.currentDate())
        details_layout.addRow("تاريخ التحويل:", self.transfer_date)
        
        # رقم المرجع
        self.reference_input = QLineEdit()
        self.reference_input.setPlaceholderText("رقم المرجع البنكي")
        details_layout.addRow("رقم المرجع:", self.reference_input)
        
        # اسم البنك
        self.bank_input = QLineEdit()
        self.bank_input.setPlaceholderText("اسم البنك")
        details_layout.addRow("البنك:", self.bank_input)
        
        # ملاحظات
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("ملاحظات إضافية")
        details_layout.addRow("ملاحظات:", self.notes_input)
        
        details_group.setLayout(details_layout)
        layout.addWidget(details_group)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("حفظ التحويل")
        save_btn.clicked.connect(self.save_transfer)
        buttons_layout.addWidget(save_btn)
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
        
        # تحميل البيانات الأولية
        self.on_type_changed()
        
    def on_type_changed(self):
        """معالج تغيير نوع التحويل"""
        transfer_type = self.transfer_type_combo.currentData()
        
        self.entity_combo.clear()
        
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        if transfer_type == "customer":
            cursor.execute('''
                SELECT id, name, current_balance FROM customers 
                WHERE is_active = 1 
                ORDER BY name
            ''')
            
            entities = cursor.fetchall()
            
            for entity in entities:
                self.entity_combo.addItem(f"{entity['name']} (رصيد: {entity['current_balance']:.2f})", entity['id'])
                
        else:  # supplier
            cursor.execute('''
                SELECT id, name, current_balance FROM suppliers 
                WHERE is_active = 1 
                ORDER BY name
            ''')
            
            entities = cursor.fetchall()
            
            for entity in entities:
                self.entity_combo.addItem(f"{entity['name']} (رصيد: {entity['current_balance']:.2f})", entity['id'])
                
        conn.close()
        
    def save_transfer(self):
        """حفظ التحويل البنكي"""
        if self.amount_input.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ صحيح")
            return
            
        entity_id = self.entity_combo.currentData()
        if not entity_id:
            QMessageBox.warning(self, "خطأ", "يرجى اختيار عميل أو مورد")
            return
            
        transfer_type = self.transfer_type_combo.currentData()
        
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            # الحصول على الحساب البنكي الافتراضي
            default_account_id = self.db_manager.get_default_bank_account_id()
            print(f"✅ معرف الحساب البنكي الافتراضي: {default_account_id}")

            # التحقق من صحة البيانات
            if not default_account_id:
                raise Exception("لا يمكن العثور على حساب بنكي افتراضي")

            if not entity_id:
                raise Exception("لا يمكن العثور على العميل/المورد المحدد")

            amount = self.amount_input.value()
            if amount <= 0:
                raise Exception("يجب أن يكون المبلغ أكبر من صفر")

            # إدراج التحويل البنكي
            if transfer_type == "customer":
                print(f"💰 إضافة تحويل من عميل - المبلغ: {amount:.2f}")
                cursor.execute('''
                    INSERT INTO bank_transfers
                    (bank_account_id, customer_id, amount, transfer_type, transfer_date, reference_number, bank_name, notes, user_id)
                    VALUES (?, ?, ?, 'in', ?, ?, ?, ?, ?)
                ''', (
                    default_account_id,
                    entity_id,
                    amount,
                    self.transfer_date.date().toString('yyyy-MM-dd'),
                    self.reference_input.text().strip() or None,
                    self.bank_input.text().strip() or None,
                    self.notes_input.toPlainText().strip() or None,
                    self.user_data['id']
                ))

                print(f"✅ تم إدراج التحويل في قاعدة البيانات")

                # تحديث رصيد البنك (إضافة)
                print(f"🏦 تحديث رصيد البنك - إضافة {amount:.2f}")
                self.db_manager.update_bank_account_balance(default_account_id, amount, 'add')

                # إضافة المبلغ للخزينة (دفعات العملاء تذهب للخزينة)
                try:
                    treasury_account = self.db_manager.get_account_by_type('treasury')
                    if treasury_account:
                        self.db_manager.update_account_balance(
                            treasury_account['id'],
                            self.amount_input.value(),
                            'add',
                            'bank_transfer',
                            None,
                            f"تحويل بنكي من العميل - مرجع: {self.reference_input.text().strip() or 'غير محدد'}",
                            self.user_data['id']
                        )
                except Exception as e:
                    print(f"خطأ في تحديث الخزينة: {str(e)}")

                # إضافة دفعة للعميل لتقليل المديونية
                cursor.execute('''
                    INSERT INTO payments
                    (customer_id, amount, payment_date, payment_method, notes, user_id)
                    VALUES (?, ?, ?, 'bank_transfer', ?, ?)
                ''', (
                    entity_id,
                    self.amount_input.value(),
                    self.transfer_date.date().toString('yyyy-MM-dd'),
                    f"تحويل بنكي - {self.bank_input.text().strip() or 'غير محدد'} - مرجع: {self.reference_input.text().strip() or 'غير محدد'}",
                    self.user_data['id']
                ))

                # إضافة المبلغ للنظام المالي المتكامل (دفعات العملاء تذهب للبنك)
                try:
                    from database.financial_manager import FinancialManager
                    import os

                    db_path = os.path.join('database', 'business_system.db')
                    financial_manager = FinancialManager(db_path)

                    # إضافة للبنك (دفعة العميل عبر تحويل بنكي)
                    financial_manager.add_bank_transaction(
                        "إيداع",
                        "عميل",
                        self.amount_input.value(),
                        f"دفعة من عميل - مرجع: {self.reference_input.text().strip() or 'غير محدد'}"
                    )
                    print(f"✅ تم إضافة {self.amount_input.value():,.2f} جنيه للبنك من دفعة العميل")

                except Exception as e:
                    print(f"خطأ في تحديث النظام المالي: {str(e)}")
                
            else:  # supplier
                print(f"💸 إضافة تحويل لمورد - المبلغ: {amount:.2f}")
                cursor.execute('''
                    INSERT INTO bank_transfers
                    (bank_account_id, supplier_id, amount, transfer_type, transfer_date, reference_number, bank_name, notes, user_id)
                    VALUES (?, ?, ?, 'out', ?, ?, ?, ?, ?)
                ''', (
                    default_account_id,
                    entity_id,
                    amount,
                    self.transfer_date.date().toString('yyyy-MM-dd'),
                    self.reference_input.text().strip() or None,
                    self.bank_input.text().strip() or None,
                    self.notes_input.toPlainText().strip() or None,
                    self.user_data['id']
                ))

                print(f"✅ تم إدراج التحويل للمورد في قاعدة البيانات")

                # تحديث رصيد البنك (خصم)
                print(f"🏦 تحديث رصيد البنك - خصم {amount:.2f}")
                self.db_manager.update_bank_account_balance(default_account_id, amount, 'subtract')

                # خصم المبلغ من النظام المالي المتكامل (دفعات الموردين تخصم من البنك)
                try:
                    from database.financial_manager import FinancialManager
                    import os

                    db_path = os.path.join('database', 'business_system.db')
                    financial_manager = FinancialManager(db_path)

                    # خصم من البنك (دفعة للمورد عبر تحويل بنكي)
                    financial_manager.add_bank_transaction(
                        "سحب",
                        "مورد",
                        self.amount_input.value(),
                        f"دفعة للمورد - مرجع: {self.reference_input.text().strip() or 'غير محدد'}"
                    )
                    print(f"✅ تم خصم {self.amount_input.value():,.2f} جنيه من البنك لدفعة المورد")

                except Exception as e:
                    print(f"خطأ في تحديث النظام المالي: {str(e)}")

                # تحديث رصيد المورد (تقليل المستحقات)
                cursor.execute('''
                    UPDATE suppliers
                    SET current_balance = current_balance - ?
                    WHERE id = ?
                ''', (self.amount_input.value(), entity_id))

                # إضافة دفعة
                cursor.execute('''
                    INSERT INTO payments
                    (supplier_id, amount, payment_method, payment_date, reference_number, notes, user_id)
                    VALUES (?, ?, 'bank_transfer', ?, ?, ?, ?)
                ''', (
                    entity_id, self.amount_input.value(),
                    self.transfer_date.date().toString('yyyy-MM-dd'),
                    self.reference_input.text().strip() or None,
                    self.notes_input.toPlainText().strip() or None,
                    self.user_data['id']
                ))
                
            conn.commit()
            conn.close()

            # رسالة نجاح مفصلة
            entity_name = self.entity_combo.currentText().split(' - ')[0] if self.entity_combo.currentText() else "غير محدد"
            success_message = (
                f"🎉 تم حفظ التحويل البنكي بنجاح!\n\n"
                f"💰 المبلغ: {amount:,.2f} جنيه\n"
                f"👤 {'العميل' if transfer_type == 'customer' else 'المورد'}: {entity_name}\n"
                f"📅 التاريخ: {self.transfer_date.date().toString('yyyy-MM-dd')}\n"
                f"🏦 البنك: {self.bank_input.text().strip() or 'غير محدد'}\n"
                f"📋 المرجع: {self.reference_input.text().strip() or 'غير محدد'}\n\n"
                f"✅ تم تحديث رصيد البنك تلقائياً"
            )

            QMessageBox.information(self, "نجح العملية ✅", success_message)
            self.accept()

        except Exception as e:
            print(f"❌ خطأ في حفظ التحويل: {str(e)}")
            import traceback
            traceback.print_exc()

            conn.rollback()
            conn.close()

            # رسالة خطأ مفصلة
            error_message = f"حدث خطأ أثناء حفظ التحويل:\n{str(e)}\n\nتفاصيل إضافية:\n"

            if "no such table" in str(e).lower():
                error_message += "• جدول قاعدة البيانات غير موجود"
            elif "no such column" in str(e).lower():
                error_message += "• عمود في قاعدة البيانات غير موجود"
            elif "constraint" in str(e).lower():
                error_message += "• خطأ في قيود قاعدة البيانات"
            elif "default_bank_account_id" in str(e).lower():
                error_message += "• لا يوجد حساب بنكي افتراضي"
            else:
                error_message += f"• خطأ تقني: {str(e)}"

            QMessageBox.critical(self, "خطأ في حفظ التحويل", error_message)


class LinkTransferDialog(QDialog):
    """نافذة ربط التحويل بالفاتورة"""

    def __init__(self, db_manager, transfer_id, user_data, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.transfer_id = transfer_id
        self.user_data = user_data
        self.init_ui()
        self.load_invoices()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("ربط التحويل بفاتورة")
        self.setFixedSize(600, 500)

        layout = QVBoxLayout()

        # معلومات التحويل
        transfer_info_group = QGroupBox("معلومات التحويل")
        transfer_info_layout = QFormLayout()

        self.transfer_info_label = QLabel()
        transfer_info_layout.addRow("التحويل:", self.transfer_info_label)

        transfer_info_group.setLayout(transfer_info_layout)
        layout.addWidget(transfer_info_group)

        # البحث عن الفواتير
        search_group = QGroupBox("البحث عن الفواتير")
        search_layout = QVBoxLayout()

        search_form_layout = QHBoxLayout()

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث برقم الفاتورة أو اسم العميل/المورد")
        self.search_input.textChanged.connect(self.search_invoices)
        search_form_layout.addWidget(QLabel("البحث:"))
        search_form_layout.addWidget(self.search_input)

        self.invoice_type_combo = QComboBox()
        self.invoice_type_combo.addItem("جميع الفواتير", "all")
        self.invoice_type_combo.addItem("فواتير المبيعات", "sale")
        self.invoice_type_combo.addItem("فواتير المشتريات", "purchase")
        self.invoice_type_combo.currentTextChanged.connect(self.load_invoices)
        search_form_layout.addWidget(QLabel("النوع:"))
        search_form_layout.addWidget(self.invoice_type_combo)

        search_layout.addLayout(search_form_layout)
        search_group.setLayout(search_layout)
        layout.addWidget(search_group)

        # جدول الفواتير
        invoices_group = QGroupBox("الفواتير المتاحة")
        invoices_layout = QVBoxLayout()

        self.invoices_table = QTableWidget()
        self.invoices_table.setColumnCount(6)
        self.invoices_table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "النوع", "العميل/المورد", "التاريخ", "المبلغ", "الحالة"
        ])
        self.invoices_table.horizontalHeader().setStretchLastSection(True)
        self.invoices_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.invoices_table.setAlternatingRowColors(True)

        invoices_layout.addWidget(self.invoices_table)
        invoices_group.setLayout(invoices_layout)
        layout.addWidget(invoices_group)

        # الأزرار
        buttons_layout = QHBoxLayout()

        link_btn = QPushButton("ربط بالفاتورة المحددة")
        link_btn.clicked.connect(self.link_transfer)
        buttons_layout.addWidget(link_btn)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

        # تحميل معلومات التحويل
        self.load_transfer_info()

    def load_transfer_info(self):
        """تحميل معلومات التحويل"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT bt.*, c.name as customer_name, s.name as supplier_name
            FROM bank_transfers bt
            LEFT JOIN customers c ON bt.customer_id = c.id
            LEFT JOIN suppliers s ON bt.supplier_id = s.id
            WHERE bt.id = ?
        ''', (self.transfer_id,))

        transfer = cursor.fetchone()
        conn.close()

        if transfer:
            entity_name = transfer['customer_name'] or transfer['supplier_name'] or "غير محدد"
            transfer_type = "وارد" if transfer['transfer_type'] == 'in' else "صادر"
            info_text = f"مبلغ: {transfer['amount']:.2f} جنيه - نوع: {transfer_type} - من/إلى: {entity_name}"
            self.transfer_info_label.setText(info_text)

    def load_invoices(self):
        """تحميل الفواتير المتاحة"""
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        invoice_type = self.invoice_type_combo.currentData()

        if invoice_type == "all":
            cursor.execute('''
                SELECT i.*, c.name as customer_name, s.name as supplier_name
                FROM invoices i
                LEFT JOIN customers c ON i.customer_id = c.id
                LEFT JOIN suppliers s ON i.supplier_id = s.id
                ORDER BY i.invoice_date DESC, i.id DESC
            ''')
        else:
            cursor.execute('''
                SELECT i.*, c.name as customer_name, s.name as supplier_name
                FROM invoices i
                LEFT JOIN customers c ON i.customer_id = c.id
                LEFT JOIN suppliers s ON i.supplier_id = s.id
                WHERE i.invoice_type = ?
                ORDER BY i.invoice_date DESC, i.id DESC
            ''', (invoice_type,))

        invoices = cursor.fetchall()
        conn.close()

        self.invoices_table.setRowCount(len(invoices))

        for row, invoice in enumerate(invoices):
            # رقم الفاتورة
            invoice_item = QTableWidgetItem(invoice['invoice_number'])
            invoice_item.setData(Qt.UserRole, invoice['id'])
            self.invoices_table.setItem(row, 0, invoice_item)

            # النوع
            invoice_type_text = "مبيعات" if invoice['invoice_type'] == 'sale' else "مشتريات"
            self.invoices_table.setItem(row, 1, QTableWidgetItem(invoice_type_text))

            # العميل/المورد
            entity_name = invoice['customer_name'] or invoice['supplier_name'] or "غير محدد"
            self.invoices_table.setItem(row, 2, QTableWidgetItem(entity_name))

            # التاريخ
            self.invoices_table.setItem(row, 3, QTableWidgetItem(invoice['invoice_date']))

            # المبلغ
            self.invoices_table.setItem(row, 4, QTableWidgetItem(f"{invoice['final_amount']:.2f}"))

            # الحالة
            if invoice['remaining_amount'] > 0:
                status = f"متبقي: {invoice['remaining_amount']:.2f}"
                status_item = QTableWidgetItem(status)
                status_item.setBackground(QColor(255, 235, 235))  # خلفية حمراء فاتحة
            else:
                status_item = QTableWidgetItem("مدفوعة بالكامل")
                status_item.setBackground(QColor(235, 255, 235))  # خلفية خضراء فاتحة

            self.invoices_table.setItem(row, 5, status_item)

    def search_invoices(self):
        """البحث في الفواتير"""
        search_text = self.search_input.text().lower()

        for row in range(self.invoices_table.rowCount()):
            show_row = False

            # البحث في رقم الفاتورة والعميل/المورد
            invoice_item = self.invoices_table.item(row, 0)
            entity_item = self.invoices_table.item(row, 2)

            if invoice_item and search_text in invoice_item.text().lower():
                show_row = True
            elif entity_item and search_text in entity_item.text().lower():
                show_row = True

            self.invoices_table.setRowHidden(row, not show_row)

    def link_transfer(self):
        """ربط التحويل بالفاتورة المحددة"""
        current_row = self.invoices_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فاتورة للربط")
            return

        invoice_id = self.invoices_table.item(current_row, 0).data(Qt.UserRole)
        invoice_number = self.invoices_table.item(current_row, 0).text()

        # تأكيد الربط
        reply = QMessageBox.question(self, "تأكيد الربط",
                                   f"هل تريد ربط التحويل بالفاتورة رقم {invoice_number}؟",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply != QMessageBox.Yes:
            return

        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # تحديث التحويل بربطه بالفاتورة
            cursor.execute('''
                UPDATE bank_transfers
                SET linked_invoice_id = ?, notes = COALESCE(notes, '') || ' - مربوط بالفاتورة رقم ' || ?
                WHERE id = ?
            ''', (invoice_id, invoice_number, self.transfer_id))

            conn.commit()
            conn.close()

            QMessageBox.information(self, "نجح", f"تم ربط التحويل بالفاتورة رقم {invoice_number} بنجاح")
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء ربط التحويل:\n{str(e)}")
